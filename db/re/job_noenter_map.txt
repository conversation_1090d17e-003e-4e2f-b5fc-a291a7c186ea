// Defines Job(s) that are restricted to enter map (by flag/zones)
//
// Structure of Database:
// JobID,FlagZone,GroupLevelBypass
//
// JobID: See JOB_* constants or use job number
//
// Legend for 'Flag' field (bitmask):
// 1    - restricted in normal maps
// 2    - restricted in PVP
// 4    - restricted in GVG
// 8    - restricted in Battlegrounds
// 16   - restricted in WOE:TE castles
// Restricted zones - configured by 'restricted <number>' mapflag
// 32   - restricted in zone 1
// 64   - restricted in zone 2
// 128  - restricted in zone 3
// 256  - restricted in zone 4
// 512  - restricted in zone 5
// 1024 - restricted in zone 6
// 2048 - restricted in zone 7
// 4096 - restricted in zone 8
//
// GroupLevelBypass: Group Level (groups.conf) to ignore the restriction
//
// NOTES:
// - Restriction will be overwritten for multiple defines with the same Job ID
// - The flag is used by 'jobcanentermap' script.

JOB_RUNE_KNIGHT,4112,100
JOB_WARLOCK,4112,100
JOB_RANGER,4112,100
JOB_<PERSON>CH_BISHOP,4112,100
JOB_MECHANIC,4112,100
JOB_GUILLOTINE_CROSS,4112,100

JOB_RUNE_KNIGHT_T,4112,100
JOB_WARLOCK_T,4112,100
JOB_RANGER_T,4112,100
JOB_ARCH_BISHOP_T,4112,100
JOB_MECHANIC_T,4112,100
JOB_GUILLOTINE_CROSS_T,4112,100

JOB_ROYAL_GUARD,4112,100
JOB_SORCERER,4112,100
JOB_MINSTREL,4112,100
JOB_WANDERER,4112,100
JOB_SURA,4112,100
JOB_GENETIC,4112,100
JOB_SHADOW_CHASER,4112,100

JOB_ROYAL_GUARD_T,4112,100
JOB_SORCERER_T,4112,100
JOB_MINSTREL_T,4112,100
JOB_WANDERER_T,4112,100
JOB_SURA_T,4112,100
JOB_GENETIC_T,4112,100
JOB_SHADOW_CHASER_T,4112,100

JOB_RUNE_KNIGHT2,4112,100
JOB_RUNE_KNIGHT_T2,4112,100
JOB_ROYAL_GUARD2,4112,100
JOB_ROYAL_GUARD_T2,4112,100
JOB_RANGER2,4112,100
JOB_RANGER_T2,4112,100
JOB_MECHANIC2,4112,100
JOB_MECHANIC_T2,4112,100

JOB_BABY_RUNE_KNIGHT,4112,100
JOB_BABY_WARLOCK,4112,100
JOB_BABY_RANGER,4112,100
JOB_BABY_ARCH_BISHOP,4112,100
JOB_BABY_MECHANIC,4112,100
JOB_BABY_GUILLOTINE_CROSS,4112,100
JOB_BABY_ROYAL_GUARD,4112,100
JOB_BABY_SORCERER,4112,100
JOB_BABY_MINSTREL,4112,100
JOB_BABY_WANDERER,4112,100
JOB_BABY_SURA,4112,100
JOB_BABY_GENETIC,4112,100
JOB_BABY_SHADOW_CHASER,4112,100

JOB_BABY_RUNE_KNIGHT2,4112,100
JOB_BABY_ROYAL_GUARD2,4112,100
JOB_BABY_RANGER2,4112,100
JOB_BABY_MECHANIC2,4112,100

JOB_SUPER_NOVICE_E,4112,100
JOB_SUPER_BABY_E,4112,100

JOB_KAGEROU,4112,100
JOB_BABY_KAGEROU,4112,100
JOB_OBORO,4112,100
JOB_BABY_OBORO,4112,100

JOB_REBELLION,4112,100
JOB_BABY_REBELLION,4112,100

JOB_SUMMONER,4112,100
JOB_BABY_SUMMONER,4112,100

JOB_STAR_EMPEROR,4112,100
JOB_SOUL_REAPER,4112,100
JOB_BABY_STAR_EMPEROR,4112,100
JOB_BABY_SOUL_REAPER,4112,100
JOB_STAR_EMPEROR2,4112,100
JOB_BABY_STAR_EMPEROR2,4112,100

JOB_DRAGON_KNIGHT,4112,100
JOB_MEISTER,4112,100
JOB_SHADOW_CROSS,4112,100
JOB_ARCH_MAGE,4112,100
JOB_CARDINAL,4112,100
JOB_WINDHAWK,4112,100
JOB_IMPERIAL_GUARD,4112,100
JOB_BIOLO,4112,100
JOB_ABYSS_CHASER,4112,100
JOB_ELEMENTAL_MASTER,4112,100
JOB_INQUISITOR,4112,100
JOB_TROUBADOUR,4112,100
JOB_TROUVERE,4112,100

JOB_WINDHAWK2,4112,100
JOB_MEISTER2,4112,100
JOB_DRAGON_KNIGHT2,4112,100
JOB_IMPERIAL_GUARD2,4112,100

JOB_SKY_EMPEROR,4112,100
JOB_SOUL_ASCETIC,4112,100
JOB_SHINKIRO,4112,100
JOB_SHIRANUI,4112,100
JOB_NIGHT_WATCH,4112,100
JOB_HYPER_NOVICE,4112,100
JOB_SPIRIT_HANDLER,4112,100

JOB_SKY_EMPEROR2,4112,100
