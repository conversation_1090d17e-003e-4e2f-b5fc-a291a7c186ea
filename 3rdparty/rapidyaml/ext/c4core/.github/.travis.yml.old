sudo: required
dist: bionic
language: cpp
env:
  global:
    # cmake is installed into /usr/bin
    - PATH=/usr/bin:/usr/local/bin:$PATH

# we're not using combination parameters here to ensure that the builds
# run in the order we want. (We want to perform the fastest tests first so
# failed tests appear as early as possible).

# NOTE: The compiler setting is unused. It simply makes the display on
# travis-ci.org more readable.
# WARNING: do not use the name CXX. <PERSON> will ignore the value here.
matrix:

  include:
    # every entry does both 64 and 32 bit
    # SAN := sanitizers
    # VG := valgrind

    # coverage: in bionic, lcov is incompatible with g++8 and later
    - env: CXX_=g++-7     BT=Coverage STD=11
    - env: CXX_=g++-7     BT=Coverage STD=14
    - env: CXX_=g++-7     BT=Coverage STD=17

    - env: CXX_=g++-10     BT=Debug   STD=11         VG=ON
    - env: CXX_=g++-10     BT=Release STD=11         VG=ON
    - env: CXX_=g++-10     BT=Debug   STD=14         VG=ON
    - env: CXX_=g++-10     BT=Release STD=14         VG=ON
    - env: CXX_=g++-10     BT=Debug   STD=17         VG=ON
    - env: CXX_=g++-10     BT=Release STD=17         VG=ON
    - env: CXX_=g++-10     BT=Debug   STD=20         VG=ON
    - env: CXX_=g++-10     BT=Release STD=20         VG=ON

    - env: CXX_=clang++-10 BT=Debug   STD=11 SAN=ALL VG=ON
    - env: CXX_=clang++-10 BT=Release STD=11 SAN=ALL VG=ON
    - env: CXX_=clang++-10 BT=Debug   STD=14 SAN=ALL VG=ON
    - env: CXX_=clang++-10 BT=Release STD=14 SAN=ALL VG=ON
    - env: CXX_=clang++-10 BT=Debug   STD=17 SAN=ALL VG=ON
    - env: CXX_=clang++-10 BT=Release STD=17 SAN=ALL VG=ON
    - env: CXX_=clang++-10 BT=Debug   STD=20 SAN=ALL VG=ON
    - env: CXX_=clang++-10 BT=Release STD=20 SAN=ALL VG=ON

    - env: CXX_=g++-9      BT=Debug
    - env: CXX_=g++-9      BT=Release
    - env: CXX_=clang++-9  BT=Debug
    - env: CXX_=clang++-9  BT=Release

    - env: CXX_=g++-8      BT=Debug
    - env: CXX_=g++-8      BT=Release
    - env: CXX_=clang++-8  BT=Debug
    - env: CXX_=clang++-8  BT=Release

    - env: CXX_=g++-7      BT=Debug
    - env: CXX_=g++-7      BT=Release
    - env: CXX_=clang++-7  BT=Debug
    - env: CXX_=clang++-7  BT=Release

    - env: CXX_=g++-6       BT=Debug
    - env: CXX_=g++-6       BT=Release
    - env: CXX_=clang++-6.0 BT=Debug
    - env: CXX_=clang++-6.0 BT=Release

    - env: CXX_=g++-5       BT=Debug
    - env: CXX_=g++-5       BT=Release
    - env: CXX_=clang++-5.0 BT=Debug
    - env: CXX_=clang++-5.0 BT=Release

    # gcc 4.9 is not available in 18.04 -- https://stackoverflow.com/questions/48398475/
    #- env: CXX_=g++-4.9     BT=Debug
    #- env: CXX_=g++-4.9     BT=Release
    - env: CXX_=clang++-4.0 BT=Debug
    - env: CXX_=clang++-4.0 BT=Release

    - env: CXX_=clang++-3.9 BT=Debug
    - env: CXX_=clang++-3.9 BT=Release

    # ----------- clang-tidy
    #
    - env: CXX_=clang++-9 BT=Debug   LINT=clang-tidy
    - env: CXX_=clang++-9 BT=Release LINT=clang-tidy

install:
  - bash -x .ci/travis-install.sh

script:
  - source .ci/travis-setenv.sh
  
  - c4core_cfg_test 64 dynamic
  - c4core_run_test 64 dynamic
  
  - c4core_cfg_test 64 static
  - c4core_run_test 64 static
  
  - c4core_cfg_test 32 static
  - c4core_run_test 32 static
  
  - echo "Success!"

after_success:
  - source .ci/travis-setenv.sh
  # coveralls only accepts one submission per job
  #- c4core_submit_coverage 32 static coveralls
  - c4core_submit_coverage 64 static coveralls
  - c4core_submit_coverage 32 static codecov
  - c4core_submit_coverage 64 static codecov
