# Uncomment this if your project is hosted on GitHub:
# github_url = "https://github.com/<user or organization>/<project>/"

[version]
current = "0.1.8"

# Example of a semver regexp.
# Make sure this matches current_version before
# using tbump
regex = '''
  (?P<major>\d+)
  \.
  (?P<minor>\d+)
  \.
  (?P<patch>\d+)
  .*
  '''

[git]
message_template = "Bump to {new_version}"
tag_template = "v{new_version}"

# For each file to patch, add a [[file]] config
# section containing the path of the file, relative to the
# tbump.toml location.
[[file]]
src = "CMakeLists.txt"
search = "c4_project\\(VERSION {current_version}"
[[file]]
src = "test/test_install/CMakeLists.txt"
search = "c4_project\\(VERSION {current_version}"
[[file]]
src = "test/test_singleheader/CMakeLists.txt"
search = "c4_project\\(VERSION {current_version}"

# You can specify a list of commands to
# run after the files have been patched
# and before the git commit is made

#  [[before_commit]]
#  name = "check changelog"
#  cmd = "grep -q {new_version} Changelog.rst"

# Or run some commands after the git tag and the branch
# have been pushed:
#  [[after_push]]
#  name = "publish"
#  cmd = "./publish.sh"
