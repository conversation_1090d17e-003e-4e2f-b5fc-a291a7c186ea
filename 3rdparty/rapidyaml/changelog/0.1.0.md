This is the first ryml release. Future releases will have a more organized changelog; for now, only recent major changes are listed.

Please be aware that there are still some anticipated breaking changes in the API before releasing the 1.0 major version. These are highlighted in [the repo ROADMAP](https://github.com/biojppm/rapidyaml/blob/v0.1.0/ROADMAP.md).

* 2020/October
  * [MR#89](https://github.com/biojppm/rapidyaml/pull/89):
     * fix python API generation in windows
     * use github actions for testing and releasing
  * [MR#88](https://github.com/biojppm/rapidyaml/pull/88): [fix MacOS compilation and installs](https://github.com/biojppm/rapidyaml/issues/75). This is a fix from [c4core](https://github.com/biojppm/cmake/issues/1).
  * [MR#88](https://github.com/biojppm/rapidyaml/pull/88): [fix boolean handling](https://github.com/biojppm/rapidyaml/issues/74). This is a fix from [c4core](https://github.com/biojppm/c4core/pull/18/).  `true` and  `false` are now parsed correctly into `bool` variables:
    ```c++
    auto tree = parse("{foo: true, bar: false}");
    ```
    Emitting `bool` variables still defaults to `0`/`1`, like the default behaviour in the STL. To explicitly request `true`/`false` use `c4::fmt::boolalpha()`:
    ```c++
    node << var;                     // "1"    or "0"
    node << c4::fmt::boolalpha(var); // "true" or "false"
    ```
* 2020/September
  * [***Breaking change***] [MR#85](https://github.com/biojppm/rapidyaml/pull/85) null values in YAML are now parsed to null strings instead of YAML null token "~":
    ```c++
    auto tree = parse("{foo: , bar: ''}");
    // previous:
    assert(tree["foo"].val() == "~");
    assert(tree["bar"].val() == "");
    // now:
    assert(tree["foo"].val() == nullptr); // notice that this is now null
    assert(tree["bar"].val() == "");
    ```
  * [MR#85](https://github.com/biojppm/rapidyaml/pull/85) Commas after tags are now allowed:
    ```yaml
    {foo: !!str, bar: ''}  # now the comma does not cause an error
    ```
  * [MR#81](https://github.com/biojppm/rapidyaml/pull/81): Always compile with extra pedantic warnings.
* 2020/May
  *  [***Breaking change***] the error callback now receives a source location object:
    ```c++
    // previous
    using pfn_error = void (*)(const char* msg, size_t msg_len, void *user_data);
    // now:
    using pfn_error = void (*)(const char* msg, size_t msg_len, Location location, void *user_data);
    ```
  * Parser fixes to improve test suite success: [MR#73](https://github.com/biojppm/rapidyaml/pull/73), [MR#71](https://github.com/biojppm/rapidyaml/pull/71), [MR#68](https://github.com/biojppm/rapidyaml/pull/68), [MR#67](https://github.com/biojppm/rapidyaml/pull/67), [MR#66](https://github.com/biojppm/rapidyaml/pull/66)
  * Fix compilation as DLL on windows [MR#69](https://github.com/biojppm/rapidyaml/pull/69)
