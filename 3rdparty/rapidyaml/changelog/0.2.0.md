### New features & improvements
- Enable parsing into nested nodes ([87f4184](https://github.com/biojppm/rapidyaml/commit/87f4184))
- `as_json()` can now be called with tree and node id ([4c23041](https://github.com/biojppm/rapidyaml/commit/4c23041))
- Add `Parser::reserve_stack()` ([f31fb9f](https://github.com/biojppm/rapidyaml/commit/f31fb9f))
- Add uninstall target ([PR #122](https://github.com/biojppm/rapidyaml/pull/122))
- Update [c4core](https://github.com/biojppm/c4core) to v0.1.1
- Add a [quickstart sample](samples/quickstart.cpp) with build examples.
- Update [README.md](README.md) to refer to the quickstart
- Add [gdb visualizers](src/ryml-gdbtypes.py)
- Add `SO_VERSION` to shared builds

### Fixes
- Fix [#139](https://github.com/biojppm/rapidyaml/issues/139): substr and csubstr not found in ryml namespace
- Fix [#131](https://github.com/biojppm/rapidyaml/issues/131): resolve references to map keys
- Fix [#129](https://github.com/biojppm/rapidyaml/issues/129): quoted strings starting with * parsed as references
- Fix [#128](https://github.com/biojppm/rapidyaml/issues/128): segfault on nonexistent anchor
- Fix [#124](https://github.com/biojppm/rapidyaml/issues/124): parse failure in comments with trailing colon
- Fix [#121](https://github.com/biojppm/rapidyaml/issues/121): preserve quotes when emitting scalars
- Fix [#103](https://github.com/biojppm/rapidyaml/issues/103): ambiguous parsing of null/empty scalars
- Fix [#90](https://github.com/biojppm/rapidyaml/issues/90): CMAKE_CXX_STANDARD ignored
- Fix [#40](https://github.com/biojppm/rapidyaml/issues/40): quadratic complexity from use of `sscanf(%f)`
- Fix emitting json to streams ([dc6af83](https://github.com/biojppm/rapidyaml/commit/dc6af83))
- Set the global memory resource when setting global callbacks ([511cba0](https://github.com/biojppm/rapidyaml/commit/511cba0))
- Fix python packaging ([PR #102](https://github.com/biojppm/rapidyaml/pull/102))

### Special thanks
- @Gei0r
- @litghost
- @costashatz
