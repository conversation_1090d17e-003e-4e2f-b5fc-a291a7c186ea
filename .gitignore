autom4te.cache/

#Global
.vscode
.Apple*
.DS_Store
.clang_complete
.local.vimrc
.ycm_extra_conf.py*
Thumbs.db
/test-builds/*
/test-cmake-debug/*
/test-cmake-manual/*

# /
/*_fifo
/*.exe
/*.exe.*
/*.ilk
/*.iobj
/*.ipdb
/*.log
/*.ncb
/*.opensdf
/*.opt
/*.pdb
/*.pid
/*.sdf
/*.stackdump
/*.suo
*.o
*.a
*.vcxproj.user
/char-server
/csv2yaml
/config.log
/config.status
/core
/enc_temp_folder
/ipch
/login-server
/Makefile
/Makefile.cache
/map-server
/map-server-generator
/mapcache
/nbproject
/web-server
/yaml2sql
/yamlupgrade

# /3rdparty/libconfig/
/3rdparty/libconfig/Makefile
/3rdparty/libconfig/*.o
/3rdparty/libconfig/obj

# /3rdparty/rapidyaml/
/3rdparty/rapidyaml/Makefile

# /3rdparty/yaml-cpp/
/3rdparty/yaml-cpp/Makefile

# /3rdparty/httplib/
/3rdparty/httplib/Makefile
/3rdparty/httplib/obj

# /db/
/db/import

# /conf/
/conf/import
/conf/msg_conf/import

# /lib
/lib

# /log/
/log/*.log
/log/*.leaks

# /src/char/
/src/char/Makefile
/src/char/obj_sql

# /src/common/
/src/common/Makefile
/src/common/obj_all
/src/common/obj_sql
/src/common/version.hpp

# /src/custom/
/src/custom

# /src/login/
/src/login/Makefile
/src/login/obj_sql

# /src/map/
/src/map/Makefile
/src/map/obj_sql
/src/map/pcre.hpp

# /src/web
/src/web/Makefile
/src/web/obj

# /src/tool/
/src/tool/Makefile
/src/tool/obj_all

# /tools/
/tools/convert
/tools/adduser
/tools/*.exe
/tools/*.ilk
/tools/*.pdb

# Visual Studio
.vs
*.VC.db
*.VC.opendb
*.slnLaunch.user

/.idea/.name
/.idea/misc.xml
/.idea/modules.xml
/.idea/rathena.iml
/.idea/vcs.xml
/.idea/workspace.xml
/build/
/cbuild/

# bat tools
/charserv.bat
/csv2yaml.bat
/logserv.bat
/mapcache.bat
/mapserv.bat
/runserver.bat
/serv.bat
/webserv.bat
/yaml2sql.bat
/yamlupgrade.bat
/navigenerator.bat

# dlls
/libmysql.dll
/pcre8.dll
/zlib.dll

# CMakeFiles
/CMakeFiles/

# generated files
generated
