#!/bin/bash

# rAthena Parallel Build Configuration Script
# This script helps configure optimal parallel build settings
# and integrates with rAthena's traditional configure options

set -e

# Store configure arguments for later use
CONFIGURE_ARGS="$@"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to detect system information
detect_system_info() {
    print_info "Detecting system information..."
    
    # Detect OS
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="Linux"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macOS"
    elif [[ "$OSTYPE" == "cygwin" ]] || [[ "$OSTYPE" == "msys" ]]; then
        OS="Windows"
    else
        OS="Unknown"
    fi
    
    # Detect CPU cores
    if command -v nproc >/dev/null 2>&1; then
        CPU_CORES=$(nproc)
    elif [ -f /proc/cpuinfo ]; then
        CPU_CORES=$(grep -c ^processor /proc/cpuinfo)
    elif command -v sysctl >/dev/null 2>&1; then
        CPU_CORES=$(sysctl -n hw.ncpu 2>/dev/null || echo 4)
    else
        CPU_CORES=4
    fi
    
    # Detect available memory (in GB)
    if command -v free >/dev/null 2>&1; then
        MEMORY_GB=$(free -g | awk '/^Mem:/{print $2}')
    elif command -v vm_stat >/dev/null 2>&1; then
        # macOS
        MEMORY_BYTES=$(sysctl -n hw.memsize)
        MEMORY_GB=$((MEMORY_BYTES / 1024 / 1024 / 1024))
    else
        MEMORY_GB=8  # Default assumption
    fi
    
    # Detect compiler
    if command -v gcc >/dev/null 2>&1; then
        GCC_VERSION=$(gcc --version | head -n1)
        HAS_GCC=true
    else
        HAS_GCC=false
    fi
    
    if command -v clang >/dev/null 2>&1; then
        CLANG_VERSION=$(clang --version | head -n1)
        HAS_CLANG=true
    else
        HAS_CLANG=false
    fi
    
    # Detect build tools
    HAS_CMAKE=$(command -v cmake >/dev/null 2>&1 && echo true || echo false)
    HAS_MAKE=$(command -v make >/dev/null 2>&1 && echo true || echo false)
    HAS_CCACHE=$(command -v ccache >/dev/null 2>&1 && echo true || echo false)
    HAS_NINJA=$(command -v ninja >/dev/null 2>&1 && echo true || echo false)
    
    print_info "System: $OS"
    print_info "CPU Cores: $CPU_CORES"
    print_info "Memory: ${MEMORY_GB}GB"
    if [ "$HAS_GCC" = true ]; then
        print_info "GCC: $GCC_VERSION"
    fi
    if [ "$HAS_CLANG" = true ]; then
        print_info "Clang: $CLANG_VERSION"
    fi
    print_info "CMake: $HAS_CMAKE"
    print_info "Make: $HAS_MAKE"
    print_info "ccache: $HAS_CCACHE"
    print_info "Ninja: $HAS_NINJA"
}

# Function to calculate optimal build settings
calculate_optimal_settings() {
    print_info "Calculating optimal build settings..."
    
    # Calculate optimal number of parallel jobs
    # Rule of thumb: Use 1.5x CPU cores, but limit based on memory
    OPTIMAL_JOBS=$((CPU_CORES * 3 / 2))
    
    # Limit based on memory (assume 1GB per job for C++ compilation)
    MAX_JOBS_BY_MEMORY=$((MEMORY_GB))
    if [ $OPTIMAL_JOBS -gt $MAX_JOBS_BY_MEMORY ]; then
        OPTIMAL_JOBS=$MAX_JOBS_BY_MEMORY
        print_warning "Limited parallel jobs to $OPTIMAL_JOBS due to memory constraints"
    fi
    
    # Minimum of 1, maximum of CPU_CORES * 2
    if [ $OPTIMAL_JOBS -lt 1 ]; then
        OPTIMAL_JOBS=1
    elif [ $OPTIMAL_JOBS -gt $((CPU_CORES * 2)) ]; then
        OPTIMAL_JOBS=$((CPU_CORES * 2))
    fi
    
    # Recommend build system
    if [ "$HAS_CMAKE" = true ] && [ "$HAS_NINJA" = true ]; then
        RECOMMENDED_GENERATOR="Ninja"
        RECOMMENDED_SYSTEM="CMake with Ninja"
    elif [ "$HAS_CMAKE" = true ]; then
        RECOMMENDED_GENERATOR="Unix Makefiles"
        RECOMMENDED_SYSTEM="CMake with Make"
    elif [ "$HAS_MAKE" = true ]; then
        RECOMMENDED_SYSTEM="Traditional Make"
    else
        print_error "No suitable build system found!"
        exit 1
    fi
    
    print_success "Optimal parallel jobs: $OPTIMAL_JOBS"
    print_success "Recommended build system: $RECOMMENDED_SYSTEM"
}

# Function to generate build configuration
generate_build_config() {
    print_info "Generating build configuration..."
    
    # Create build configuration file
    cat > build-config.sh << EOF
#!/bin/bash
# rAthena Build Configuration
# Generated by configure-parallel.sh on $(date)

# System Information
export RATHENA_OS="$OS"
export RATHENA_CPU_CORES=$CPU_CORES
export RATHENA_MEMORY_GB=$MEMORY_GB

# Build Settings
export RATHENA_PARALLEL_JOBS=$OPTIMAL_JOBS
export RATHENA_BUILD_SYSTEM="$RECOMMENDED_SYSTEM"

# Compiler Settings
export RATHENA_HAS_GCC=$HAS_GCC
export RATHENA_HAS_CLANG=$HAS_CLANG
export RATHENA_HAS_CCACHE=$HAS_CCACHE

# Build Tools
export RATHENA_HAS_CMAKE=$HAS_CMAKE
export RATHENA_HAS_MAKE=$HAS_MAKE
export RATHENA_HAS_NINJA=$HAS_NINJA

# Recommended CMake generator
export RATHENA_CMAKE_GENERATOR="$RECOMMENDED_GENERATOR"

# Helper functions
rathena_build_cmake() {
    local build_type=\${1:-Release}
    local jobs=\${2:-\$RATHENA_PARALLEL_JOBS}
    
    echo "Building with CMake (\$build_type, \$jobs jobs)..."
    mkdir -p build
    cd build
    cmake -G "\$RATHENA_CMAKE_GENERATOR" \\
          -DCMAKE_BUILD_TYPE=\$build_type \\
          -DENABLE_PARALLEL_BUILD=ON \\
          -DPARALLEL_BUILD_JOBS=\$jobs \\
          ..
    cmake --build . -j \$jobs
    cd ..
}

rathena_build_make() {
    local jobs=\${1:-\$RATHENA_PARALLEL_JOBS}
    
    echo "Building with Make (\$jobs jobs)..."
    if [ ! -f Makefile ]; then
        ./configure
    fi
    make -j \$jobs server
}

rathena_clean() {
    echo "Cleaning build artifacts..."
    if [ -d build ]; then
        rm -rf build
    fi
    if [ -f Makefile ]; then
        make clean
    fi
}

echo "rAthena build configuration loaded."
echo "Optimal parallel jobs: \$RATHENA_PARALLEL_JOBS"
echo "Recommended build system: \$RATHENA_BUILD_SYSTEM"
echo ""
echo "Usage:"
echo "  rathena_build_cmake [Debug|Release] [jobs]"
echo "  rathena_build_make [jobs]"
echo "  rathena_clean"
EOF

    chmod +x build-config.sh
    print_success "Build configuration saved to build-config.sh"
}

# Function to show recommendations
show_recommendations() {
    print_info "Build Recommendations:"
    echo ""
    echo "1. OPTIMAL SETTINGS:"
    echo "   - Parallel jobs: $OPTIMAL_JOBS"
    echo "   - Build system: $RECOMMENDED_SYSTEM"
    echo ""
    echo "2. QUICK START:"
    echo "   source ./build-config.sh"
    echo "   rathena_build_cmake Release"
    echo ""
    echo "3. MANUAL COMMANDS:"
    if [ "$HAS_CMAKE" = true ]; then
        echo "   # CMake build:"
        echo "   mkdir build && cd build"
        echo "   cmake -DENABLE_PARALLEL_BUILD=ON -DPARALLEL_BUILD_JOBS=$OPTIMAL_JOBS .."
        echo "   cmake --build . -j $OPTIMAL_JOBS"
        echo ""
    fi
    if [ "$HAS_MAKE" = true ]; then
        echo "   # Traditional make:"
        echo "   ./configure"
        echo "   make -j $OPTIMAL_JOBS server"
        echo ""
    fi
    echo "4. USING BUILD SCRIPT:"
    echo "   ./build-parallel.sh -j $OPTIMAL_JOBS"
    echo ""
    
    if [ "$HAS_CCACHE" = true ]; then
        print_info "ccache detected - this will speed up rebuilds significantly!"
    else
        print_warning "Consider installing ccache for faster rebuilds: apt-get install ccache"
    fi
    
    if [ "$MEMORY_GB" -lt 4 ]; then
        print_warning "Low memory detected. Consider using fewer parallel jobs if builds fail."
    fi
}

# Main execution
main() {
    echo "rAthena Parallel Build Configuration"
    echo "===================================="
    echo ""
    
    detect_system_info
    echo ""
    calculate_optimal_settings
    echo ""
    generate_build_config
    echo ""
    show_recommendations
}

# Run main function
main "$@"
