@SET_MAKE@

# Parallel build support
# Use MAKEFLAGS to pass -j option to sub-makes automatically
# This enables parallel builds with make -j<N>
MAKEFLAGS += --no-print-directory

HAVE_MYSQL=@HAVE_MYSQL@
OMAP=@OMAP@
ifeq ($(HAVE_MYSQL),yes)
	ALL_DEPENDS=server tools
	SERVER_DEPENDS=common login char map web import
	# 3rdparty dependencies - these can build in parallel
	COMMON_DEPENDS=libconfig rapidyaml yaml-cpp
	LOGIN_DEPENDS=libconfig common
	CHAR_DEPENDS=libconfig common rapidyaml
	MAP_DEPENDS=libconfig common rapidyaml
	WEB_DEPENDS=libconfig common yaml-cpp httplib
	# Tools only depend on minicore, can build in parallel with servers
	TOOLS_DEPENDS=libconfig
else
	ALL_DEPENDS=needs_mysql
	SERVER_DEPENDS=needs_mysql
	COMMON_DEPENDS=needs_mysql
	LOGIN_DEPENDS=needs_mysql
	CHAR_DEPENDS=needs_mysql
	MAP_DEPENDS=needs_mysql
	WEB_DEPENDS=needs_mysql
	TOOLS_DEPENDS=needs_mysql
endif


#####################################################################
.PHONY: all server sql \
	common \
	login \
	char \
	map \
	web \
	tools \
	import \
	clean help \
	install uninstall bin-clean \

all: $(ALL_DEPENDS)

sql: $(SERVER_DEPENDS)
	@echo "-!- 'make sql' is now deprecated. Please run 'make server' to continue. -!-"

server: $(SERVER_DEPENDS)

# Build 3rdparty libraries first (can be built in parallel)
libconfig:
	@$(MAKE) -C 3rdparty/libconfig

rapidyaml:
	@$(MAKE) -C 3rdparty/rapidyaml

yaml-cpp:
	@$(MAKE) -C 3rdparty/yaml-cpp

httplib:
	@$(MAKE) -C 3rdparty/httplib

# Build common library (required by all servers)
common: $(COMMON_DEPENDS)
	@$(MAKE) -C src/common server

# Server targets (can build in parallel once common is ready)
login: $(LOGIN_DEPENDS)
	@$(MAKE) -C src/login server

char: $(CHAR_DEPENDS)
	@$(MAKE) -C src/char

map: $(MAP_DEPENDS)
	@$(MAKE) -C src/map server

web: $(WEB_DEPENDS)
	@$(MAKE) -C src/web server

# Tools (can build in parallel with servers, only needs libconfig)
tools: $(TOOLS_DEPENDS)
	@$(MAKE) -C src/tool
	@$(MAKE) -C src/map tools

# Parallel build targets for 3rdparty libraries
.PHONY: 3rdparty-parallel
3rdparty-parallel:
	@echo "Building 3rdparty libraries in parallel..."
	@$(MAKE) -j libconfig rapidyaml yaml-cpp httplib

# Parallel build targets for servers (after common is built)
.PHONY: servers-parallel
servers-parallel: common
	@echo "Building servers in parallel..."
	@$(MAKE) -j login char map web

import:
# 1) create conf/import folder
# 2) add missing files
# 3) remove remaining .svn folder
	@echo "building conf/import, conf/msg_conf/import and db/import folder..."
	@if test ! -d conf/import ; then mkdir conf/import ; fi
	@for f in $$(ls conf/import-tmpl) ; do if test ! -e conf/import/$$f ; then cp conf/import-tmpl/$$f conf/import ; fi ; done
	@rm -rf conf/import/.svn
	@if test ! -d conf/msg_conf/import ; then mkdir conf/msg_conf/import ; fi
	@for f in $$(ls conf/msg_conf/import-tmpl) ; do if test ! -e conf/msg_conf/import/$$f ; then cp conf/msg_conf/import-tmpl/$$f conf/msg_conf/import ; fi ; done
	@rm -rf conf/msg_conf/import/.svn
	@if test ! -d db/import ; then mkdir db/import ; fi
	@for f in $$(ls db/import-tmpl) ; do if test ! -e db/import/$$f ; then cp db/import-tmpl/$$f db/import ; fi ; done
	@rm -rf db/import/.svn

clean:
	@echo "Cleaning all build artifacts..."
	@$(MAKE) -C src/common $@
	@$(MAKE) -C 3rdparty/libconfig $@
	@$(MAKE) -C 3rdparty/rapidyaml $@
	@$(MAKE) -C 3rdparty/yaml-cpp $@
	@$(MAKE) -C 3rdparty/httplib $@
	@$(MAKE) -C src/login $@
	@$(MAKE) -C src/char $@
	@$(MAKE) -C src/map $@
	@$(MAKE) -C src/web $@
	@$(MAKE) -C src/tool $@

# Parallel clean (faster for large projects)
.PHONY: clean-parallel
clean-parallel:
	@echo "Cleaning all build artifacts in parallel..."
	@$(MAKE) -j -C src/common clean & \
	 $(MAKE) -j -C 3rdparty/libconfig clean & \
	 $(MAKE) -j -C 3rdparty/rapidyaml clean & \
	 $(MAKE) -j -C 3rdparty/yaml-cpp clean & \
	 $(MAKE) -j -C 3rdparty/httplib clean & \
	 $(MAKE) -j -C src/login clean & \
	 $(MAKE) -j -C src/char clean & \
	 $(MAKE) -j -C src/map clean & \
	 $(MAKE) -j -C src/web clean & \
	 $(MAKE) -j -C src/tool clean & \
	 wait

help:
	@echo "most common targets are 'all' 'server' 'conf' 'clean' 'help'"
	@echo "possible targets are:"
	@echo "'common'      - builds object files used for the three servers"
	@echo "'libconfig'   - builds object files of libconfig"
	@echo "'rapidyaml'   - builds object files of rapidyaml"
	@echo "'yaml-cpp'    - builds object files of yaml-cpp"
	@echo "'httplib'     - builds object files of httplib"
	@echo "'login'       - builds login server"
	@echo "'char'        - builds char server"
	@echo "'map'         - builds map server"
	@echo "'web'         - builds web server"
	@echo "'tools'       - builds all the tools in src/tools"
	@echo "'import'      - builds conf/import, conf/msg_conf/import and db/import folders from their template folders (x-tmpl)"
	@echo "'all'         - builds all the above targets"
	@echo "'server'      - builds servers (targets 'common' 'login' 'char' 'map' and 'import')"
	@echo "'clean'            - cleans builds and objects"
	@echo "'clean-parallel'   - cleans builds and objects in parallel (faster)"
	@echo "'3rdparty-parallel'- builds 3rdparty libraries in parallel"
	@echo "'servers-parallel' - builds servers in parallel (after common)"
	@echo "'install'          - run installer which sets up rathena in /opt/"
	@echo "'bin-clean'        - deletes installed binaries"
	@echo "'uninstall'        - run uninstaller which erases all installation changes"
	@echo "'help'             - outputs this message"
	@echo ""
	@echo "PARALLEL BUILD USAGE:"
	@echo "  make -j<N> server    # Build with N parallel jobs"
	@echo "  make -j8 all         # Build everything with 8 parallel jobs"
	@echo "  make -j\$$(nproc) server # Build with all available CPU cores"
	@echo ""
	@echo "EXAMPLES:"
	@echo "  make -j4 server      # Build servers with 4 parallel jobs"
	@echo "  make -j20 all        # Build everything with 20 parallel jobs"
	@echo "  ./build-parallel.sh  # Use the parallel build script (recommended)"

needs_mysql:
	@echo "MySQL not found or disabled by the configure script"
	@exit 1

install:
	@sh ./install.sh

bin-clean:
	@sh ./uninstall.sh bin

uninstall:
	@sh ./uninstall.sh all

#####################################################################
