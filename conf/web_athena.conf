// rAthena Web Server configuration file.

// Note: "Comments" are all text on the right side of a double slash "//"
// Whatever text is commented will not be parsed by the servers, and serves
// only as information/reference.

// The web server listens on the interface with this IP address.
// NOTE: This allows you to run multiple servers on multiple interfaces
// while using the same ports for each server.
//bind_ip: 127.0.0.1
 
// Web Server Port
web_port: 8888

//Time-stamp format which will be printed before all messages.
//Can at most be 20 characters long.
//Common formats:
// %I:%M:%S %p (hour:minute:second 12 hour, AM/PM format)
// %H:%M:%S (hour:minute:second, 24 hour format)
// %d/%b/%Y (day/Month/year)
//For full format information, consult the strftime() manual.
//timestamp_format: [%d/%b %H:%M]

//If redirected output contains escape sequences (color codes)
stdout_with_ansisequence: no

//Makes server log selected message types to a file in the /log/ folder
//1: Log Warning Messages
//2: Log Error and SQL Error messages.
//4: Log Debug Messages
//Example: "console_msg_log: 7" logs all 3 kinds
//Messages logged by this overrides console_silent setting
console_msg_log: 0

// File path to store the console messages above
console_log_filepath: ./log/web-msg_log.log

//Makes server output more silent by omitting certain types of messages:
//1: Hide Information messages
//2: Hide Status messages
//4: Hide Notice Messages
//8: Hide Warning Messages
//16: Hide Error and SQL Error messages.
//32: Hide Debug Messages
//Example: "console_silent: 7" Hides information, status and notice messages (1+2+4)
console_silent: 0

// Print requests and responses?
// This is useful for debugging purposes, it will print the entire
// request and response for each transaction.
print_req_res: off

// Allow GIF images to be uploaded as guild emblem?
allow_gifs: yes

import: conf/import/web_conf.txt
