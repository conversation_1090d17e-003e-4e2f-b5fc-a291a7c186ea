[General]
config_proj_version=1

[memcheck]
alignment=8
freelist-vol=10000000
leak-check=full
leak-resolution=low
partial-loads-ok=no
show-reachable=no
track-origins=yes
workaround-gcc296-bugs=no

[valgrind]
child-silent-after-fork=yes
db-attach=no
db-command=/usr/bin/gdb -nw %f %p
demangle=yes
error-limit=yes
gen-suppressions=no
input-fd=0
kernel-variant=none
log-fd=2
log-file=
log-socket=
max-stackframe=2000000
num-callers=12
run-libc-freeres=yes
show-below-main=no
show-emwarns=no
sim-hints=none
smc-check=stack
suppressions=
time-stamp=no
tool=memcheck
trace-children=no
track-fds=no
verbosity=1
xml=yes
xml-user-comment=

[valkyrie]
binary=./char-server
binary-flags=--run-once
browser=/usr/bin/perl
default-logdir=./tmp
font-gen-sys=true
font-gen-user="Luxi Sans,10,-1,5,50,0,0,0,0,0"
font-tool-user="Misc Fixed,11,-1,5,50,0,0,0,0,0"
project-file=/home/<USER>/Documents/Dev/RO/rathena/conf/valkyrie_sample.cfg
show-butt-text=false
show-tooltips=true
src-editor=/bin/geany
src-lines=2
use-vk-palette=true
vg-exec=/bin/valgrind
view-log=
working-dir=../
