// rA<PERSON>na msg_athena.conf
// Message Configuration of char-server
// -----------------------
0: <PERSON><PERSON>
1: <PERSON><PERSON>
2: <PERSON><PERSON>
3: <PERSON>
4: <PERSON>colyte
5: <PERSON>
6: <PERSON><PERSON>ef
7: <PERSON>
8: <PERSON>
9: <PERSON>
10: <PERSON><PERSON>
11: <PERSON>
12: <PERSON><PERSON><PERSON>
13: <PERSON><PERSON>
14: <PERSON>
15: <PERSON>
16: <PERSON>
17: <PERSON><PERSON>
18: <PERSON><PERSON>
19: <PERSON>
20: <PERSON>
21: <PERSON> <PERSON><PERSON>
22: <PERSON><PERSON>
23: <PERSON>
24: Christmas
25: <PERSON>
26: <PERSON> Swordsman
27: High Magician
28: <PERSON>
29: High Acolyte
30: <PERSON>
31: High Thief
32: <PERSON>
33: <PERSON>
34: <PERSON> Wizard
35: <PERSON><PERSON>
//35: <PERSON>mith //IRO name
36: <PERSON><PERSON><PERSON>
37: <PERSON><PERSON><PERSON> <PERSON>
38: <PERSON><PERSON><PERSON>
39: Champion
40: Professor
//40: Scholar //IRO name
41: <PERSON>alker
42: Creator
//42: Biochemist //IRO Name
43: Clown
//43: Min<PERSON><PERSON> //IRO Name
44: <PERSON>
45: Baby <PERSON>
46: Baby <PERSON>
47: Baby <PERSON>
48: Baby <PERSON>
49: Baby <PERSON>lyte
50: <PERSON>
51: Baby <PERSON>hi<PERSON>
52: <PERSON> <PERSON>
53: Baby <PERSON>
54: Baby <PERSON>
55: <PERSON>
56: Baby <PERSON>
57: Baby <PERSON>
58: Baby <PERSON>
59: <PERSON>
60: <PERSON>
61: Baby <PERSON>
62: <PERSON>
63: Baby <PERSON><PERSON>
64: Baby <PERSON>
65: <PERSON> Baby
66: <PERSON><PERSON><PERSON>
67: <PERSON> Gladiator
68: <PERSON>er
//79: FREE
//70: FREE
71: Summer
72: Gangsi
73: <PERSON> Knight
74: Dark Collector
75: Rune <PERSON>
76: Warlock
77: <PERSON>
78: Arch Bishop
79: Mechanic
80: Guillotine Cross
81: <PERSON> Guard
82: Sorcerer
83: Minstrel
//83: <PERSON>stro //IRO Name
84: Wanderer
85: Sura
86: <PERSON>tic
//86: Geneticist //IRO Name
87: Shadow Chaser
88: Baby Rune Knight
89: Baby Warlock
90: Baby Ranger
91: Baby Arch Bishop
92: Baby Mechanic
93: Baby Guillotine Cross
94: Baby Royal Guard
95: Baby Sorcerer
96: Baby Minstrel
97: Baby Wanderer
98: Baby Sura
99: Baby Genetic
100: Baby Shadow Chaser
101: Expanded Super Novice
102: Expanded Super Baby
103: Kagerou
104: Oboro
105: Hanbok
106: Rebellion
107: Oktoberfest
108: Summoner
109: Baby Summoner
110: Baby Ninja
111: Baby Kagerou
112: Baby Oboro
113: Baby Taekwon
114: Baby Star Gladiator
115: Baby Soul Linker
116: Baby Gunslinger
117: Baby Rebellion
118: Star Emperor
119: Soul Reaper
120: Baby Star Emperor
121: Baby Soul Reaper
122: Dragon Knight
123: Meister
124: Shadow Cross
125: Arch Mage
126: Cardinal
127: Windhawk
128: Imperial Guard
129: Biolo
130: Abyss Chaser
131: Elemental Master
132: Inquisitor
133: Troubadour
134: Trouvere
135: Sky Emperor
136: Soul Ascetic
137: Shinkiro
138: Shiranui
139: Night Watch
140: Hyper Novice
141: Spirit Handler

199: Unknown Job

//Auction
200: Auction Manager
201: Auction
202: Thanks, you won the auction!.
203: Payment for your auction!.
204: No buyers have been found for your auction.
205: Auction canceled.
206: Auction closed.
207: Auction winner.
208: Someone has placed a higher bid.
209: You have placed a higher bid.
210: You have won the auction.
211: Payment for your auction!.

// @accountinfo
212: No matches were found for your criteria, '%s'
213: An error occured, bother your admin about it.
214: Your query returned the following %d results, please be more specific...
215: [AID: %d] %s | %s | Level: %d/%d | %s
216: No account with ID '%d' was found.
217: -- Account %d --
218: User: %s | GM Group: %d | State: %d
//219: FREE
//220: FREE
221: Account e-mail: %s | Birthdate: %s
222: Last IP: %s (%s)
223: This user has logged in %d times, the last time was at %s
224: -- Character Details --
225: [Slot/CID: %d/%d] %s | %s | Level: %d/%d | %s
226: This account doesn't have characters.

// Achievements
227: GM
228: Achievement Reward Mail
229: [%s] Achievement Reward.
