// rAthena msg_athena.conf
// Message Configuration
// For translation, just change msg here (second line), no need to modify source code,
// or alternatively, use conf/import/msg_conf.txt
//
// Format:
// msg_number: message (max length is 255 chars)
//
// To disable a string (%s) field, make its max length 0:
// eg:
// 270: *%s %s* (@me format)
// 270: *%.0s%s* (remove the character's name from it)

// Define a custom maximum value for MAP_MAX_MSG in /src/custom/defines_pre.hpp to increase the maximum message limit.

// Messages of GM commands
// -----------------------

0: Warped.
1: Map not found.
2: Invalid coordinates, using random target cell.
3: Character not found.
4: Jump to %s
5: Jump to %d %d
6: Your save point has been changed.
7: Warping to save point.
8: Speed changed.
9: Options changed.
10: Invisible: Off
11: Invisible: On
12: Your job has been changed.
13: You've died.
14: Character killed.
15: Player warped (message sent to player too).
16: You've been revived!
17: <PERSON>, <PERSON> recovered.
18: Item created.
19: Invalid item ID or name.
20: All of your items have been removed.
21: Base level raised.
22: Base level lowered.
23: Job level can't go any higher.
24: Job level raised.
25: Job level lowered.
26: Help commands:
//27: free
28: No player found.
29: 1 player found.
30: %d players found.
31: PvP: Off.
32: PvP: On.
33: GvG: Off.
34: GvG: On.
35: You can't use this command with this class.
36: Appearance changed.
37: An invalid number was specified.
38: Invalid location number, or name.
39: All monsters summoned!
40: Invalid monster ID or name.
41: Unable to decrease the number/value.
42: Stat changed.
43: You're not in a guild.
44: You're not the master of your guild.
45: Guild level change failed.
46: %s recalled!
47: Base level can't go any higher.
48: Character's job changed.
49: Invalid job ID.
50: You already have some GM powers.
51: Character revived.
52: This option cannot be used in PK Mode.
53: '%s' stats:
54: No player found in map '%s'.
55: 1 player found in map '%s'.
56: %d players found in map '%s'.
57: Character's save point changed.
58: Character's options changed.
59: Night Mode Activated.
60: Day Mode Activated.
61: The holy messenger has given judgement.
62: Judgement has passed.
63: Mercy has been shown.
64: Mercy has been granted.
65: Character's base level raised.
66: Character's base level lowered.
67: Character's job level can't go any higher.
68: Character's job level raised.
69: Character's job level lowered.
70: You have learned the skill.
71: You have forgotten the skill.
72: War of Emperium has been initiated.
73: War of Emperium is currently in progress.
74: War of Emperium has been ended.
75: War of Emperium is currently not in progress.
76: All skills have been added to your skill tree.
77: The reference result of '%s' (name: id):
78: - %s: %u
79: It is %d affair above.
80: Give the display name or monster name/id please.
81: Your GM level doesn't authorize you to perform this action on the specified player.
82: Please provide a name or number from the list provided:
83: Monster 'Emperium' cannot be spawned.
84: All stats changed!
85: Invalid time for %s command (time=%d).
86: Sorry, player names have to be at least 4 characters.
87: Sorry, player names can be no longer than 23 characters.
88: Sending request to %s server...
89: Night mode is already enabled.
90: Day mode is already enabled.
91: Character's base level can't go any higher.
92: All characters recalled!
93: All online characters of the %s guild have been recalled to your position.
94: Incorrect name/ID, or no one from the specified guild is online.
95: All online characters of the %s party have been recalled to your position.
96: Incorrect name/ID, or no one from the specified party is online.
97: Item database has been reloaded.
98: Monster database has been reloaded.
99: Skill database has been reloaded.
100: Scripts have been reloaded.
101: Login-server asked to reload GM accounts and their level.
102: You have mounted a Peco Peco.
103: No longer spying on the %s guild.
104: Spying on the %s guild.
105: No longer spying on the %s party.
106: Spying on the %s party.
107: All items have been repaired.
108: No item need to be repaired.
109: Player has been nuked!
110: NPC Enabled.
111: This NPC doesn't exist.
112: NPC Disabled.
113: %d item(s) removed by a GM.
114: %d item(s) removed from the player.
115: %d item(s) removed. Player had only %d on %d items.
116: Character does not have the specified item.
117: You have been placed in jail by a GM.
118: Player warped to jail.
119: This player is not in jail.
120: A GM has discharged you from jail.
121: Player unjailed.
122: Disguise applied.
123: Invalid Monster/NPC name/ID specified.
124: Undisguise applied.
125: You're not disguised.
//Clone Messages
126: Cannot clone a player of higher GM level than yourself.
127: You've reached your slave clones limit.
128: Evil clone spawned.
129: Unable to spawn evil clone.
130: Clone spawned.
131: Unable to spawn clone.
132: Slave clone spawned.
133: Unable to spawn slave clone.
//Messages 134-139 are no longer used, available for future reuse (preferrable for more variations of @clone)
140: Character's disguise applied.
141: Character's undisguise applied.
142: Character is not disguised.
143: Commands are disabled on this map.
144: Invalid e-mail. If you have default e-mail, type <EMAIL>.
145: Invalid new e-mail. Please enter a real e-mail.
146: New e-mail must be a real e-mail.
147: New e-mail must be different from the current e-mail.
148: Information sent to login-server via char-server.
149: Impossible to increase the number/value.
150: No GM found.
151: 1 GM found.
152: %d GMs found.
153: %s is Unknown Command.
154: %s failed.
155: You are unable to change your job.
156: HP or/and SP modified.
157: HP and SP have already been recovered.
158: Base level can't go any lower.
159: Job level can't go any lower.
160: PvP is already Off.
161: PvP is already On.
162: GvG is already Off.
163: GvG is already On.
164: Your memo point #%d doesn't exist.
165: All monsters killed!
166: No item has been refined.
167: 1 item has been refined.
168: %d items have been refined.
169: The item (%u: '%s') is not equipable.
170: The item is not equipable.
171: %d - void
//172: You replace previous memo position %d - %s (%d,%d).
//173: Note: you don't have the 'Warp' skill level to use it.
174: Number of status points changed.
175: Number of skill points changed.
176: Current amount of zeny changed.
177: You cannot decrease that stat anymore.
178: You cannot increase that stat anymore.
179: Guild level changed.
180: The monster/egg name/ID doesn't exist.
181: You already have a pet.
182: Pet intimacy changed.
183: Pet intimacy is already at maximum.
184: Sorry, but you have no pet.
185: Pet hunger changed.
186: Pet hunger is already at maximum.
187: You can now rename your pet.
188: You can already rename your pet.
189: This player can now rename his/her pet.
190: This player can already rename his/her pet.
191: Sorry, but this player has no pet.
192: Unable to change the specified character's job.
193: Character's base level can't go any lower.
194: Character's job level can't go any lower.
195: All players have been kicked!
196: You already have this quest skill.
197: This skill number doesn't exist or isn't a quest skill.
198: This skill number doesn't exist.
199: This player has learned the skill.
200: This player already has this quest skill.
201: You don't have this quest skill.
202: This player has forgotten the skill.
203: This player doesn't have this quest skill.
204: You can't open a shop on this cell.
205: Maybe you meant:
206: '%s' skill points reset.
207: '%s' stats points reset.
208: '%s' skill and stat points have been reset.
209: Character's skill points changed.
210: Character's status points changed.
211: Character's current zeny changed.
212: Cannot mount while in disguise.
213: You can not mount a Peco Peco with your current job.
214: You have released your Peco Peco.
215: This player cannot mount a Peco Peco while in disguise.
216: This player has mounted a Peco Peco.
217: This player cannot mount a Peco Peco with his/her current job.
218: This player's Peco Peco has been released.
219: %d day
220: %d days
221: %s %d hour
222: %s %d hours
223: %s %d minute
224: %s %d minutes
225: %s and %d second
226: %s and %d seconds
227: Party modification is disabled on this map.
228: Guild modification is disabled on this map.
229: Your effect has changed.
230: Server time (normal time): %A, %B %d %Y %X.
231: Game time: The game is in permanent daylight.
232: Game time: The game is in permanent night.
233: Game time: The game is in night for %s.
234: Game time: After, the game will be in permanent daylight.
235: Game time: The game is in daylight for %s.
236: Game time: After, the game will be in permanent night.
237: Game time: After, the game will be in night for %s.
238: Game time: A day cycle has a normal duration of %s.
239: Game time: After, the game will be in daylight for %s.
240: %d monster(s) summoned!
241: You can now attack and kill players freely.
242: You can now be attacked and killed by players.
243: Skills have been disabled on this map.
244: Skills have been enabled on this map.
245: Server Uptime: %ld days, %ld hours, %ld minutes, %ld seconds.
246: Your GM level doesn't authorize you to perform this action.
247: You are not authorized to warp to this map.
248: You are not authorized to warp from your current map.
249: You are not authorized to warp to your save map.
250: You have already opened your storage. Close it first.
251: You have already opened your guild storage. Close it first.
252: You are not in a guild.
//253: You are not authorized to memo this map.
254: GM command configuration has been reloaded.
255: Battle configuration has been reloaded.
256: Status database has been reloaded.
257: Player database has been reloaded.
258: Sent packet 0x%x (%d)
259: Invalid packet
260: This item cannot be traded.
261: Script could not be loaded.
262: Script loaded.
263: This item cannot be dropped.
264: This item cannot be stored.
265: %s has bought your item(s).
266: Some of your items cannot be vended and were removed from the shop.
267: '%s' designated maps reset.
268: Reloaded the Message of the Day.
269: Displaying first %d matches
//@me output format
270: * :%s %s: *
271: You can't drop items on this map.
272: You can't trade on this map.
273: Commands available:
274: %d commands found.
275: No commands found.
276: You can't open a shop on this map
277: Usage: @request <petition/message to online GMs>.
278: (@request): %s
279: @request sent.
280: Invalid name.
281: You can't create chat rooms on this map.
//Party-related
282: You need to be a party leader to use this command.
283: Target character must be online and in your current party.
284: Leadership transferred.
285: You've become the party leader.
286: There's been no change in the setting.
287: You cannot change party leaders on this map.
//Missing stuff for @killer related commands.
288: You are no longer killable.
289: The player is now killable.
290: The player is no longer killable.
291: Weather effects will dispell on warp/refresh
292: Killer state reset.
// Item Bound System
293: This bounded item cannot be traded to that character.
294: This bounded item cannot be stored there.
295: Please enter an item name or ID (usage: @item <item name/ID> <quantity> <bound type>).
296: Please enter all parameters (usage: @item2 <item name/ID> <quantity>
297:   <identify_flag> <refine> <attribute> <card1> <card2> <card3> <card4> <bound type>).
298: Invalid bound type. Valid types: 1-Account, 2-Guild, 3-Party, 4-Character
// Guild Castles Number
// --------------------
//299: ?? Castles
300: None Taken
301: One Castle
302: Two Castles
303: Three Castles
304: Four Castles
305: Five Castles
306: Six Castles
307: Seven Castles
308: Eight Castles
309: Nine Castles
310: Ten Castles
311: Eleven Castles
312: Twelve Castles
313: Thirteen Castles
314: Fourteen Castles
315: Fifteen Castles
316: Sixteen Castles
317: Seventeen Castles
318: Eighteen Castles
319: Nineteen Castles
320: Twenty Castles
321: Twenty-One Castles
322: Twenty-Two Castles
323: Twenty-Three Castles
324: Twenty-Four Castles
325: Twenty-Five Castles
326: Twenty-Six Castles
327: Twenty-Seven Castles
328: Twenty-Eight Castles
329: Twenty-Nine Castles
330: Thirty Castles
331: Thirty-One Castles
332: Thirty-Two Castles
333: Thirty-Three Castles
// 334: Thirty-Four Castles
334: Total Domination

// Battlegrounds Queue
337: You can't apply to a battleground queue from this map.
338: You can't apply to a battleground queue due to recently deserting a battleground. Time remaining: %d minutes and %d seconds.
339: You can't apply to a battleground queue for %d seconds due to recently leaving one.
340: Participants were unable to join. Delaying entry for more participants.

// Templates for @who output
343: Name: %s
344: (%s)
345: | Party: '%s'
346: | Guild: '%s'
//You may ommit the last %s, then you won't see players job name
347: | Lv:%d/%d | Job: %s
//You may ommit 2 last %d, then you won't see players coords, just map name
348: | Location: %s %d %d
// @fullstrip
349: Please enter a player name (usage: @fullstrip <char name/ID>).
// @duel (part 1)
350: Duel: You can't use @invite. You aren't a duelist.
351: Duel: The limit of players has been reached.
352: Duel: Player name not found.
353: Duel: The Player is in the duel already.
354: Duel: Invitation has been sent.
355: Duel: You can't use @duel without @reject.
356: Duel: You can take part in duel once per %d minutes.
357: Duel: Invalid value.
358: Duel: You can't use @leave. You aren't a duelist.
359: Duel: You've left the duel.
360: Duel: You can't use @accept without a duel invitation.
361: Duel: The duel invitation has been accepted.
362: Duel: You can't use @reject without a duel invitation.
363: Duel: The duel invitation has been rejected.
364: Duel: You can't invite %s because he/she isn't on the same map.
365: Duel: Can't use %s in duel.
// @duel (part 2)
370:  -- Duels: %d/%d, Members: %d/%d, Max players: %d --
371:  -- Duels: %d/%d, Members: %d/%d --
372:  -- Duel has been created (Use @invite/@leave) --
373:  -- Player %s invites %s to duel --
374: Blue -- Player %s invites you to PVP duel (Use @accept/@reject) --
375:  <- Player %s has left the duel --
376:  -> Player %s has accepted the duel --
377:  -- Player %s has rejected the duel --
//etc
378: Eleanor is now in %s mode.
379: Item Failed. [%s] is cooling down. Wait %.1f minutes.
380: Item Failed. [%s] is cooling down. Wait %d seconds.
381: Skill Failed. [%s] requires %dx %s.
382: You're too close to a stone or emperium to use this skill.
383: You cannot create a savepoint in an instance.
384: You cannot create a memo in an instance.
//emblem chk
385: You're not allowed to change emblem during WOE
386: The chosen emblem was detected invalid
387: The chosen emblem was detected invalid as it contain too much transparency (limit=%d)
//etc
388: You cannot use this item while storage is open.
389: Speed returned to normal.
//NoAsk
390: Autorejecting is activated.
391: Autorejecting is deactivated.
392: You request has been rejected by autoreject option.
393: Autorejected trade request from %s.
394: Autorejected party invite from %s.
395: Autorejected guild invite from %s.
396: Autorejected alliance request from %s.
397: Autorejected opposition request from %s.
398: Autorejected friend request from %s.
400: Usage: @jailfor <time> <character name>
401: You have been jailed for %d years, %d months, %d days, %d hours and %d minutes
402: %s in jail for %d years, %d months, %d days, %d hours and %d minutes
// WoE SE (@agitstart2/@agitend2)
403: War of Emperium SE has been initiated.
404: War of Emperium SE is currently in progress.
405: War of Emperium SE has been ended.
406: War of Emperium SE is currently not in progress.
//chrif related
407: Char-Server disconnected
408: Need disconnection to perform change-sex request...
409: Your sex has been changed (need disconnection by the server)...
//410-411 used by cash shop
412: Your account has 'Unregistered'.
413: Your account has an 'Incorrect Password'...
414: Your account has expired.
415: Your account has been rejected from server.
416: Your account has been blocked by the GM Team.
417: Your Game's EXE file is not the latest version.
418: Your account has been prohibited to log in.
419: Server is jammed due to over populated.
420: Your account has not more authorised.
421: Your account has been totally erased.
423: Your %s has been banished until %s 
424: Login-serv has been asked to %s the player '%.*s'.
425: The player '%.*s' doesn't exist.
426: Your GM level doesn't authorise you to %s the player '%.*s'.
427: Login-server is offline. Impossible to %s the player '%.*s'.
428: block
429: ban
430: unblock
431: unban
432: change the sex of
433: This character has been banned until 
434: Char-server has been asked to %s the character '%.*s'.
435: Please enter a player name (usage: %s <char name>).
436: VIP
437: GM's cannot become a VIP.
438: You are no longer VIP.

// Homunculus messages
450: You already have a homunculus

451: Cash Shop is disabled on this map.

// Message System
460: Please enter a valid language (usage: @langtype <language>).
461: Language is now set to %s.
462: This language is currently disabled.
463: Message configuration has been reloaded.
464: ---- Available languages:

480: ----- Players in Map -----
481: Player '%s' (session #%d) | Location: %d,%d
482: ----- NPCs in Map -----
483: ----- Chats in Map -----
484: Chat: %s | Player: %s | Location: %d %d
485:    Users: %d/%d | Password: %s | Public: %s
486: Yes
487: No
488: Please enter at least one valid list number (usage: @mapinfo <0-3> <map>).
489: NPC %d: %s::%s | Direction: %s | Sprite: %d | Location: %d %d
490: NPC %d: %s | Direction: %s | Sprite: %d | Location: %d %d
491: North
492: North West
493: West
494: South West
495: South
496: South East
497: East
498: North East
499: Unknown

// Messages of others (not for GM commands)
// ----------------------------------------
//500 free
501: Your account time limit is: %d-%m-%Y %H:%M:%S.
502: Day Mode is activated
503: Night Mode is activated

// Cash point change messages
504: Used %d kafra points and %d cash points. %d kafra and %d cash points remaining.
505: Gained %d cash points. Total %d points.
506: Gained %d kafra points. Total %d points.
410: Removed %d cash points. Total %d points.
411: Removed %d kafra points. Total %d points.

// Char ban
507: This player has been banned for %d minute(s).
508: This player hasn't been banned (Ban option is disabled).

509: Script-bound commands:

// mail system
510: You have %d new emails (%d unread)

// Instancing
515: Your instance has been reloaded.
516: Instance database has been reloaded.

// @auction
517: Auction System is disabled.

// @itemlist -- continued
518: Lower Costume Head, 
519: Top Costume Head, 
520: Top/Lower Costume Head, 
521: Mid Costume Head, 
522: Mid/Lower Costume Head, 
523: Top/Mid/Lower Costume Head, 
524: Costume Robe, 
525: Costume Floor, 
526: Ammo, 
527: Shadow Body, 
528: Shadow Right Hand, 
529: Shadow Left Hand, 
530: Shadow Both Hands, 
531: Shadow Shoes, 
532: Shadow Right Accessory, 
533: Shadow Left Accessory, 

534: Shop is out of stock! Please come back later.

// Bot detect messages (currently unused)
535: Possible use of BOT (99%% of chance) or modified client by '%s' (account: %d, char_id: %d). This player ask your name when you are hidden.
536: Character '%s' (account: %d) is trying to use a bot (it tries to detect a fake player).
537: Character '%s' (account: %d) is trying to use a bot (it tries to detect a fake mob).

// Trade Spoof Messages
538: Hack on trade: character '%s' (account: %d) try to trade more items that he has.
539: This player has %d of a kind of item (id: %u), and tried to trade %d of them.
540: This player has been definitivly blocked.

// Rare Items Drop/Steal announce
541: '%s' got %s's %s (chance: %0.02f%%)
//541: %.0s%.0sSomeone got %s
542: '%s' stole %s's %s (chance: %0.02f%%)
//542: %.0s%.0sSomeone stole %s
// 543~548 are not used (previously @away messages)

// @autotrade
549: You should have a shop open to use @autotrade.

//550 -> 650: Job Names
550: Novice
551: Swordsman
552: Magician
553: Archer
554: Acolyte
555: Merchant
556: Thief
557: Knight
558: Priest
559: Wizard
560: Blacksmith
561: Hunter
562: Assassin
563: Crusader
564: Monk
565: Sage
566: Rogue
567: Alchemist
568: Bard
569: Dancer
570: Wedding
571: Super Novice
572: Gunslinger
573: Ninja
574: Christmas
575: High Novice
576: High Swordsman
577: High Magician
578: High Archer
579: High Acolyte
580: High Merchant
581: High Thief
582: Lord Knight
583: High Priest
584: High Wizard
585: Whitesmith
//585: Mastersmith //IRO name
586: Sniper
587: Assassin Cross
588: Paladin
589: Champion
590: Professor
//590: Scholar //IRO name
591: Stalker
592: Creator
//592: Biochemist //IRO Name
593: Clown
//593: Minstrel //IRO Name
594: Gypsy
595: Baby Novice
596: Baby Swordsman
597: Baby Magician
598: Baby Archer
599: Baby Acolyte
600: Baby Merchant
601: Baby Thief
602: Baby Knight
603: Baby Priest
604: Baby Wizard
605: Baby Blacksmith
606: Baby Hunter
607: Baby Assassin
608: Baby Crusader
609: Baby Monk
610: Baby Sage
611: Baby Rogue
612: Baby Alchemist
613: Baby Bard
614: Baby Dancer
615: Super Baby
616: Taekwon
617: Star Gladiator
618: Soul Linker
//619 free
//620 free
621: Summer
622: Gangsi
623: Death Knight
624: Dark Collector
625: Rune Knight
626: Warlock
627: Ranger
628: Arch Bishop
629: Mechanic
630: Guillotine Cross
631: Royal Guard
632: Sorcerer
633: Minstrel
//633: Maestro //IRO Name
634: Wanderer
635: Sura
636: Genetic
//636: Geneticist //IRO Name
637: Shadow Chaser
638: Baby Rune Knight
639: Baby Warlock
640: Baby Ranger
641: Baby Arch Bishop
642: Baby Mechanic
643: Baby Guillotine Cross
644: Baby Royal Guard
645: Baby Sorcerer
646: Baby Minstrel
647: Baby Wanderer
648: Baby Sura
649: Baby Genetic
650: Baby Shadow Chaser
651: Expanded Super Novice
652: Expanded Super Baby
653: Kagerou
654: Oboro
655: Unknown Job

// MvP Tomb
// Added here so it can be easily translated
656: Tomb
657: [ ^EE0000%s^000000 ]
658: Has met its demise
659: Time of death : ^EE0000%s^000000
660: Defeated by
661: [^EE0000%s^000000]

// Etc messages from source
662: You must be at least %d cells away from any NPC.
663: Duel: Can't use this item in duel.
664: You cannot use this command when dead.
665: Can't create chat rooms in this area.
666: Pets are not allowed in Guild Wars.
667: You're not dead.
668: Your actual memo positions are:
669: You can't catch any pet on this map.
670: You can't leave battleground guilds.
671: Friend already exists.
672: Name not found in list.
673: This action can't be performed at the moment. Please try again later.
674: Friend removed.
675: Cannot send mails too fast!!
676: Alliances cannot be made during Guild Wars!
677: Alliances cannot be broken during Guild Wars!
678: You are no longer the Guild Master.
679: You have become the Guild Master!
680: You have been recovered!

681: Rune Knight T
682: Warlock T
683: Ranger T
684: Arch Bishop T
685: Mechanic T
686: Guillotine Cross T
687: Royal Guard T
688: Sorcerer T
689: Minstrel T
690: Wanderer T
691: Sura T
692: Genetic T
693: Shadow Chaser T
694: Hanbok
695: Rebellion
696: Oktoberfest
697: Summoner
698: Baby Summoner
699: Baby Ninja

// @vip
700: Usage: @vip <time> <character name>
701: Invalid time for VIP command.
702: Time parameter format is +/-<value> to alter. y/a = Year, m = Month, d/j = Day, h = Hour, n/mn = Minute, s = Second.
703: GM has removed your VIP time.
704: Player '%s' is no longer VIP.
705: Your VIP status is valid for %d years, %d months, %d days, %d hours, %d minutes and %d seconds.
706: Player '%s' is now VIP for %d years, %d months, %d days, %d hours, %d minutes and %d seconds.
707: You are VIP until: %s
708: The player is now VIP until: %s

709: Item %u has been removed from your inventory.
710: Item %u has been removed from your cart.
711: Item %u has been removed from your storage.

// Item shop
712: You do not have enough %s (%u).
713: You do not have enough '%s'.
714: Item Shop List: %s (%u)
715: Point Shop List: '%s'
716: Your '%s' is now: %d

// MVP EXP reward message
717: Congratulations! You are the MVP! Your reward EXP Points are %llu !!

// @showrate
718: Personal rate information is not displayed now.
719: Personal rate information will be shown.

//Skill messages
//720: Free
721: [%s] Poison effect was applied to the weapon.
//722: Free

// @costume
723: '%s' is an unknown costume
724: You're already wearing a(n) '%s' costume, type '@costume' to remove it.
725: -- %s
726: - Available Costumes
727: '%s' Costume removed.

// Monster Transformation
728: Traaaansformation-!! %s form!!
729: Cannot transform into monster while in disguise.
730: Character cannot be disguised while in monster form.
731: Transforming into monster is not allowed in Guild Wars.

//732: Free

733: Please enter a NPC file name (usage: @reloadnpcfile <file name>).

// @cloneequip/@clonestat
734: Cannot clone your own %s.
735: Usage: %s <char name/ID>
736: Cannot clone %s from this player.
737: '%s' (%d) cannot be cloned, limit (%d).
738: Clone '%s' is done.

// @bodystyle
739: Please enter a body style (usage: @bodystyle <body ID: %d-%d>).
740: This job has no alternate body styles.

// @showexp
741: Gained
742: Lost
743: Experience %s Base:%ld (%0.2f%%) Job:%ld (%0.2f%%)

// @adopt
744: Baby already adopted or is in the process of being adopted.
745: You need to be married and in a party with your partner and the Baby to adopt.
746: Both parents need to have their wedding rings equipped.
747: The Baby is not a Novice.
748: A parent or Baby was not found.

// WoE TE (@agitstart3/@agitend3)
749: War of Emperium TE has been initiated.
750: War of Emperium TE is currently in progress.
751: War of Emperium TE has been ended.
752: War of Emperium TE is currently not in progress.

// Expanded Baby Jobs
753: Baby Kagerou
754: Baby Oboro
755: Baby Taekwon
756: Baby Star Gladiator
757: Baby Soul Linker
758: Baby Gunslinger
759: Baby Rebellion

// Channel System
760: You cannot join channel '%s'. Limit of %d has been met.
761: %s %s has joined.
762: You cannot leave channel '%s'.
763: %s %s left.
764: You cannot change the color for channel '%s'.
765: You're not allowed to ban a player.
766: You cannot kick a player from channel '%s'.
767: You're not allowed to kick a player.
768: %s %s has been kicked.
769: %s %s has been banned.
770: %s %s has been unbanned.

//@reloadachievementdb
771: Achievement database has been reloaded.

// Achievements
772: Achievements are disabled.

// @refineui
773: This command requires packet version 2016-10-12 or newer.
774: This command is disabled via configuration.
775: You have already opened the refine UI.
//776-781 reserved for tax system

782: Star Emperor
783: Soul Reaper
784: Baby Star Emperor
785: Baby Soul Reaper

// Guild Storage Expansion Skill
786: The guild does not have a guild storage.
787: You do not have permission to use the guild storage.

// Attendance
// Mail sender: Officer
788: <MSG>3455</MSG>
// Mail title: %dday attendance has been paid.
789: <MSG>3456,%d</MSG>
// Mail body: %dday attendance has been paid.
790: <MSG>3456,%d</MSG>
791: You are not allowed to use the attendance system.

// Private Airship
792: The private airship system is disabled.

793: Usage @camerainfo range rotation latitude

// pcblock command
794: This action is currently blocked.

// @reloadattendancedb
795: Attendance database has been reloaded.

// NoRODEX Mapflag
796: You cannot use RODEX on this map.

797: This command is unavailable to non-4th class.

// @stylist
798: This command requires packet version 2015-11-04 or newer.
799: You have already opened the stylist UI.

800: Dragon Knight
801: Meister
802: Shadow Cross
803: Arch Mage
804: Cardinal
805: Windhawk
806: Imperial Guard
807: Biolo
808: Abyss Chaser
809: Elemental Master
810: Inquisitor
811: Troubadour
812: Trouvere
813: Sky Emperor
814: Soul Ascetic
815: Shinkiro
816: Shiranui
817: Night Watch
818: Hyper Novice
819: Spirit Handler

// @trpoint
820: Please enter a number (usage: @trpoint <number of points>).

// @chargeap
821: AP recovered.
822: AP modified.
823: AP have already been recovered.

// @displayskillcast
824: Usage: @displayskillcast <skill ID> {<skill level> <ground target flag> <cast time>}

// @displayskill (2nd Message Line)
825: Effect Types: 0: All, 1: Damage, 2: Splash Dmg, 3: No Damage, 4: Ground

// @displayskillunit
826: Usage: @displayskillunit <unit ID> {<skill level> <range>}

// @mobinfo RES/MRES
827:  RES:%d  MRES:%d

// General packet version check messages
828: This command requires packet version %s or newer.

// Enchant UI
829: Enchanting is not possible for your item's enchant grade.

// @reloadbarterdb
830: Barter database has been reloaded.

// NoBank Mapflag
831: You cannot use the Bank on this map.

// @reloadcashdb
832: Cash shop database has been reloaded.

//833-899 free

//------------------------------------
// More atcommands message
//------------------------------------

// @send
900: Usage:
901:	@send len <packet hex number>
902: 	@send <packet hex number> {<value>}*
903: 	Value: <type=B(default),W,L><number> or S<length>"<string>"
904: Packet 0x%x length: %d
905: Unknown packet: 0x%x
906: Not a string:
907: Not a hexadecimal digit:
908: Unknown type of value in:

// @rura
909: Please enter a map (usage: @warp/@rura/@mapmove <mapname> <x> <y>).

// @where
910: Please enter a player name (usage: @where <char name>).

// @jumpto
911: Please enter a player name (usage: @jumpto/@warpto/@goto <char name/ID>).

// @who
912: (CID:%d/AID:%d)

// @whogm
913: Name: %s (GM)
914: Name: %s (GM:%d) | Location: %s %d %d
915:       BLvl: %d | Job: %s (Lvl: %d)
916:       Party: '%s' | Guild: '%s'
917: None

// @speed
918: Please enter a speed value (usage: @speed <%d-%d>).

// @storage
919: Storage opened.

// @guildstorage
920: Guild storage opened.

// @option
921: Please enter at least one option.

// @jobchange
922: Please enter a job ID.
923: You can not change to this job by command.
//924-979 free (future jobs?)

// @kami
980: Please enter a message (usage: @kami <message>).
981: Please enter color and message (usage: @kamic <color> <message>).
982: Invalid color.

// @item
983: Please enter an item name or ID (usage: @item <item name/ID> <quantity>).

// @item2
984: Please enter all parameters (usage: @item2 <item name/ID> <quantity>
985:   <identify_flag> <refine> <attribute> <card1> <card2> <card3> <card4>).

// @baselevelup
986: Please enter a level adjustment (usage: @lvup/@blevel/@baselvlup <number of levels>).

// @joblevelup
987: Please enter a level adjustment (usage: @joblvup/@jlevel/@joblvlup <number of levels>).

// @help
988: There is no help for %c%s.
989: Help for command %c%s:
990: Available aliases:

// @model
991: Please enter at least one value (usage: @model <hair ID: %d-%d> <hair color: %d-%d> <clothes color: %d-%d>).

// @dye
992: Please enter a clothes color (usage: @dye/@ccolor <clothes color: %d-%d>).

// @hairstyle
993: Please enter a hair style (usage: @hairstyle/@hstyle <hair ID: %d-%d>).

// @haircolor
994: Please enter a hair color (usage: @haircolor/@hcolor <hair color: %d-%d>).

// @go
995: You cannot use @go on this map.

// @refine
996: Please enter a position and an amount (usage: @refine <equip position> <+/- amount>).
997: %d: Lower Headgear
998: %d: Right Hand
999: %d: Garment
1000: %d: Left Accessory
1001: %d: Body Armor
1002: %d: Left Hand
1003: %d: Shoes
1004: %d: Right Accessory
1005: %d: Top Headgear
1006: %d: Mid Headgear

// @produce
1007: Please enter at least one item name/ID (usage: @produce <equip name/ID> <element> <# of very's>).

// @memo
1008: Please enter a valid position (usage: @memo <memo_position:%d-%d>).

// @displaystatus
1009: Please enter a status type/flag (usage: @displaystatus <status type> <flag> <tick> {<val1> {<val2> {<val3>}}}).

// @stpoint
1010: Please enter a number (usage: @stpoint <number of points>).

// @skpoint
1011: Please enter a number (usage: @skpoint <number of points>).

// @zeny
1012: Please enter an amount (usage: @zeny <amount>).

// @param
1013: Please enter a valid value (usage: @str/@agi/@vit/@int/@dex/@luk <+/-adjustment>).

// @guildlevelup
1014: Please enter a valid level (usage: @guildlvup/@guildlvlup <# of levels>).

// @makeegg
1015: Please enter a monster/egg name/ID (usage: @makeegg <pet>).

// @petfriendly
1016: Please enter a valid value (usage: @petfriendly <0-1000>).

// @pethungry
1017: Please enter a valid number (usage: @pethungry <0-100>).

// @recall
1018: Please enter a player name (usage: @recall <char name/ID>).
1019: You are not authorized to warp someone to this map.

// @recall
1020: You are not authorized to warp this player from their map.

// @charblock/@charunblock
1021: Please enter a player name (usage: %s <char name>).

// @ban/@charban
1022: Please enter ban time and a player name (usage: %s <time> <char name>).
1023: You are not allowed to alter the time of a ban.

// @rates
1024: MVP Drop Rates: Common %.2fx / Healing %.2fx / Usable %.2fx / Equipment %.2fx / Card %.2fx

// @recall
1025: The player is currently autotrading and cannot be recalled.

// @kick
1026: Please enter a player name (usage: @kick <char name/ID>).

// @questskill / @lostskill
1027: Please enter a quest skill number.

// @spiritball
1028: Please enter an amount (usage: @spiritball <number: 0-%d>).

// @party
1029: Please enter a party name (usage: @party <party_name>).

// @guild
1030: Please enter a guild name (usage: @guild <guild_name>).

// @idsearch
1031: Please enter part of an item name (usage: @idsearch <part_of_item_name>).

// @recallall / @guildrecall / @partyrecall
1032: You are not authorized to warp someone to your current map.
1033: Because you are not authorized to warp from some maps, %d player(s) have not been recalled.

// @guildrecall
1034: Please enter a guild name/ID (usage: @guildrecall <guild_name/ID>).

// @partyrecall
1035: Please enter a party name/ID (usage: @partyrecall <party_name/ID>).

//1036 free
//1037 free

// @mapinfo
1038: Please enter at least one valid list number (usage: @mapinfo <0-3> <map>).
1039: ------ Map Info ------
1040: Map: %s | Players: %d | NPCs: %d | Chats: %d | Vendings: %d
1041: ------ Map Flags ------
1042: Town Map
1043: Autotrade Enabled
1044: Autotrade Disabled
1045: Battlegrounds ON (type %d)
1046: PvP Flags:
1047: GvG Flags:
1048: Teleport Flags:
1049: Weather Flags:
1050: Other Flags:
//1051: free
1052: Skill Damage Adjustments:
1053:  > [Map] %d%%, %d%%, %d%%, %d%% | Caster:%d
1054:  > [Map Skill] Name : Player, Monster, Boss Monster, Other | Caster
1055: Skill Duration Adjustments:
//1056-1064 free
1065:  No Exp Penalty: %s | No Zeny Penalty: %s
1066: On
1067: Off
1068: No Save (Return to last Save Point)
1069: No Save, Save Point: %s,Random
1070: No Save, Save Point: %s,%d,%d


// @mount
1119: You have mounted your Dragon.
1120: You have released your Dragon.
1121: You have mounted your Warg.
1122: You have released your Warg.
1123: You have mounted your Mado Gear.
1124: You have released your Mado Gear.

// @guildspy
1125: The mapserver has spy command support disabled.
1126: Please enter a guild name/ID (usage: @guildspy <guild_name/ID>).

// @partyspy
1127: Please enter a party name/ID (usage: @partyspy <party_name/ID>).

// @nuke
1128: Please enter a player name (usage: @nuke <char name>).

// @tonpc
1129: Please enter a NPC name (usage: @tonpc <NPC_name>).

// @enablenpc
1130: Please enter a NPC name (usage: @enablenpc <NPC_name>).

// @hidenpc
1131: Please enter a NPC name (usage: @hidenpc <NPC_name>).

// @loadnpc
1132: Please enter a script file name (usage: @loadnpc <file name>).

// @unloadnpc
1133: Please enter a NPC name (usage: @unloadnpc <NPC_name>).

// @jail
1134: Please enter a player name (usage: @jail <char_name>).

// @unjail
1135: Please enter a player name (usage: @unjail/@discharge <char_name>).

// @jailfor
1136: Invalid time for jail command.
1137: You are now
1138: This player is now

// @jailtime
1139: You are not in jail.
1140: You have been jailed indefinitely.
1141: You have been jailed for an unknown amount of time.
1142: You will remain

// @disguise
1143: Please enter a Monster/NPC name/ID (usage: @disguise <name/ID>).
1144: Character cannot be disguised while mounted.

// @disguiseall
1145: Please enter a Monster/NPC name/ID (usage: @disguiseall <name/ID>).

// @disguiseguild
1146: Please enter a mob name/ID and guild name/ID (usage: @disguiseguild <mob name/ID>, <guild name/ID>).

// @undisguiseguild
1147: Please enter guild name/ID (usage: @undisguiseguild <guild name/ID>).

// @exp
1148: Base Level: %d (%.3f%%) | Job Level: %d (%.3f%%)

// @broadcast
1149: Please enter a message (usage: @broadcast <message>).

// @localbroadcast
1150: Please enter a message (usage: @localbroadcast <message>).

// @email
1151: Please enter 2 emails (usage: @email <actual@email> <new@email>).

// @effect
1152: Please enter an effect number (usage: @effect <effect number>).

// @npcmove
1153: Usage: @npcmove <X> <Y> <npc_name>
1154: NPC is not on this map.
1155: NPC moved.

// @addwarp
1156: Usage: @addwarp <mapname> <X> <Y> <npc name>
1157: Unknown map '%s'.
1158: New warp NPC '%s' created.

// @follow
1159: Follow mode OFF.
1160: Follow mode ON.

// @storeall
1161: You currently cannot open your storage.
1162: All items stored.

// @skillid
1163: Please enter a skill name to look up (usage: @skillid <skill name>).
1164: skill %d: %s (%s)

// @useskill
1165: Usage: @useskill <skill ID> <skill level> <char name>

// @displayskill
1166: Usage: @displayskill <skill ID> {<skill level> <type>}

// @skilltree
1167: Usage: @skilltree <skill ID> <char name>
1168: Player is using %s skill tree (%d basic points).
1169: The player cannot use that skill.
1170: Player requires level %d of skill %s.
1171: The player meets all the requirements for that skill.

// @marry
1172: Usage: @marry <char name>
1173: They are married... wish them well.
1174: The two cannot wed because one is either a baby or already married.

// @divorce
1175: '%s' is not married.
1176: '%s' and his/her partner are now divorced.

// @changelook
1177: Usage: @changelook {<position>} <view id>
1178: Position: 1-Top 2-Middle 3-Bottom 4-Weapon 5-Shield 6-Shoes 7-Robe 8-Body

// @autotrade
1179: Autotrade is not allowed on this map.
1180: You cannot autotrade when dead.

// @changegm
1181: You need to be a Guild Master to use this command.
1182: You cannot change guild leaders on this map.
1183: Usage: @changegm <guild_member_name>
1184: Target character must be online and be a guild member.

// @changeleader
1185: Usage: @changeleader <party_member_name>

// @partyoption
1186: Usage: @partyoption <pickup share: yes/no> <item distribution: yes/no>

// @autoloot
1187: Autolooting items with drop rates of %0.02f%% and below.
1188: Autoloot is now off.

// @autolootitem
1189: Item not found.
1190: You're already autolooting this item.
1191: Your autolootitem list is full. Remove some items first with @autolootid -<item name or ID>.
1192: Autolooting item: '%s'/'%s' {%u}
1193: You're currently not autolooting this item.
1194: Removed item: '%s'/'%s' {%u} from your autolootitem list.
1195: You can have %d items on your autolootitem list.
1196: To add an item to the list, use "@alootid +<item name or ID>". To remove an item, use "@alootid -<item name or ID>".
1197: "@alootid reset" will clear your autolootitem list.
1198: Your autolootitem list is empty.
1199: Items on your autolootitem list:
1200: Your autolootitem list has been reset.

// @rain
//1201: The rain has stopped.
//1202: It has started to rain.

// @snow
1203: Snow has stopped falling.
1204: It has started to snow.

// @sakura
1205: Cherry tree leaves no longer fall.
1206: Cherry tree leaves have begun to fall.

// @clouds
1207: The clouds has disappear.
1208: Clouds appear.

// @clouds2
1209: The alternative clouds disappear.
1210: Alternative clouds appear.

// @fog
1211: The fog has gone.
1212: Fog hangs over.

// @leaves
1213: Leaves no longer fall.
1214: Fallen leaves fall.

// @fireworks
1215: Fireworks have ended.
1216: Fireworks are launched.

// @sound
1217: Please enter a sound filename (usage: @sound <filename>).

// @mobsearch
1218: Please enter a monster name (usage: @mobsearch <monster name>).
1219: Invalid mob ID %s!
1220: Mob Search... %s %s

// @cleanmap
1221: All dropped items have been cleaned up.

// @npctalk
1222: Please enter the correct parameters (usage: @npctalk <npc name>, <message>).
1223: Please enter the correct parameters (usage: @npctalkc <color> <npc name>, <message>).

// @pettalk
1224: Please enter a message (usage: @pettalk <message>).

// @summon
1225: Please enter a monster name (usage: @summon <monster name> {duration}).

// @adjgroup
1226: Usage: @adjgroup <group_id>
1227: Specified group does not exist.
1228: Group changed successfully.
1229: Your group has changed.

// @trade
1230: Please enter a player name (usage: @trade <char name>).

// @setbattleflag
1231: Usage: @setbattleflag <flag> <value> {<reload>}
1232: Unknown battle_config flag.
1233: Set battle_config as requested.

// @unmute
1234: Please enter a player name (usage: @unmute <char name>).
1235: Player is not muted.
1236: Player unmuted.

// @mute
1237: Usage: @mute <time> <char name>

// @identify
1238: There are no items to appraise.

// @mobinfo
1239: Please enter a monster name/ID (usage: @mobinfo <monster_name_or_monster_ID>).
1240: MVP Monster: '%s'/'%s'/'%s' (%d)
1241: Monster: '%s'/'%s'/'%s' (%d)
1242:  Lv:%d  HP:%d  Base EXP:%llu  Job EXP:%llu  HIT:%d  FLEE:%d
1243:  DEF:%d  MDEF:%d  STR:%d  AGI:%d  VIT:%d  INT:%d  DEX:%d  LUK:%d
1244:  ATK:%d~%d  Range:%d~%d~%d  Size:%s  Race: %s  Element: %s (Lv:%d)
1245:  Drops:
1246: This monster has no drops.
1247:  MVP Bonus EXP:%llu
1248:  MVP drops:
1249: This monster has no MVP drops.

// @showmobs
1250: Invalid mob id %s!
1251: Can't show boss mobs!
1252: Mob Search... %s %s

// @homlevel
1253: Please enter a level adjustment (usage: @homlevel <number of levels>).

// @homlevel / @homevolve / @homfriendly / @homhungry / @homtalk / @hominfo / @homstats
1254: You do not have a homunculus.

// @homevolve
1255: Your homunculus doesn't evolve.

// @makehomun
1256: Please enter a homunculus ID (usage: @makehomun <homunculus id>).
1257: Invalid Homunculus ID.

// @homfriendly
1258: Please enter a friendly value (usage: @homfriendly <friendly value [0-1000]>).

// @homhungry
1259: Please enter a hunger value (usage: @homhungry <hunger value [0-100]>).

// @homtalk
1260: Please enter a message (usage: @homtalk <message>).

// @hominfo
1261: Homunculus stats:
1262: HP: %d/%d - SP: %d/%d
1263: ATK: %d - MATK: %d~%d
1264: Hungry: %d - Intimacy: %u
1265: Stats: Str %d / Agi %d / Vit %d / Int %d / Dex %d / Luk %d

// @homstats
1266: Homunculus growth stats (Lv %d %s):
1267: Max HP: %d (%d~%d)
1268: Max SP: %d (%d~%d)
1269: Str: %d (%d~%d)
1270: Agi: %d (%d~%d)
1271: Vit: %d (%d~%d)
1272: Int: %d (%d~%d)
1273: Dex: %d (%d~%d)
1274: Luk: %d (%d~%d)

// @homshuffle
1275: Homunculus stats altered.

// @iteminfo
1276: Please enter an item name/ID (usage: @ii/@iteminfo <item name/ID>).
1277: Item: '%s'/'%s' (%u) Type: %s | Extra Effect: %s
1278: None
1279: With script
1280: NPC Buy:%dz, Sell:%dz | Weight: %.1f
1281:  - Available in the shops only.
1282:  - Maximal monsters drop chance: %02.02f%%
1283:  - Monsters don't drop this item.

// @whodrops
1284: Please enter item name/ID (usage: @whodrops <item name/ID>).
1285: Item: '%s' (ID: %u)
1286:  - Item is not dropped by mobs.
1287:  - Common mobs with highest drop chance (only max %d are listed):

// @whereis
1288: Please enter a monster name/ID (usage: @whereis <monster_name_or_monster_ID>).
1289: %s spawns in:
1290: This monster does not spawn normally.

// @adopt
1291: Usage: @adopt <father>,<mother>,<child>
1292: Adopting: --%s--%s--%s--\n
1293: Cannot find player %s online.
1294: They are family... wish them luck.

// @version
1295: rAthena Version %s %s
1296: Cannot determine SVN/Git version.

// @mutearea
1297: Please enter a time in minutes (usage: @mutearea/@stfu <time in minutes>).

// @rates
1298: Experience rates: Base %.2fx / Job %.2fx
1299: Normal Drop Rates: Common %.2fx / Healing %.2fx / Usable %.2fx / Equipment %.2fx / Card %.2fx
1300: Boss Drop Rates: Common %.2fx / Healing %.2fx / Usable %.2fx / Equipment %.2fx / Card %.2fx
1301: Other Drop Rates: MvP %.2fx / Card-Based %.2fx / Treasure %.2fx

// @me
1302: Please enter a message (usage: @me <message>).

// @size / @sizeall / @sizeguild
1303: Size change applied.

// @sizeguild
1304: Please enter guild name/ID (usage: @sizeguild <size> <guild name/ID>).

// @monsterignore
1305: You are now immune to attacks.
1306: Returned to normal state.

// @fakename
1307: Returned to real name.
1308: You must enter a name.
1309: Fake name must be at least two characters.
1310: Fake name enabled.

// @mapflag
1311: Enabled Mapflags in this map:
1312: Usage: "@mapflag monster_noteleport 1" (0=Off | 1=On)
1313: Type "@mapflag available" to list the available mapflags.
1314: Invalid flag name or flag.
1315: Available Flags:

// @showexp
1316: Gained/lost exp will not be shown.
1317: Gained/lost exp is now shown.

// @showzeny
1318: Gained zeny will not be shown.
1319: Gained zeny is now shown.

// @showdelay
1320: Skill delay failures will not be shown.
1321: Skill delay failures are now shown.

// @cash
1322: Please enter an amount.

// @clone
1323: You must enter a player name or ID.

// @feelreset
1324: Reset 'Feeling' maps.

// @noks
1325: [ K.S Protection Inactive ]
1326: [ K.S Protection Active - Option: Party ]
1327: [ K.S Protection Active - Option: Self ]
1328: [ K.S Protection Active - Option: Guild ]
1329: Usage: @noks <self|party|guild>

// @allowks
1330: [ Map K.S Protection Active ]
1331: [ Map K.S Protection Inactive ]

// @itemlist
1332: ------ %s items list of '%s' ------
1333:  | Equipped:
1334: Garment,
1335: Left Accessory,
1336: Body/Armor,
1337: Right Hand,
1338: Left Hand,
1339: Both Hands,
1340: Feet,
1341: Right Accessory,
1342: Lower Head,
1343: Top Head,
1344: Top/Lower Head,
1345: Mid Head,
1346: Mid/Lower Head,
1347: Top/Mid/Lower Head,
1348:  -> (pet egg, pet id: %u, named)
1349:  -> (pet egg, pet id: %u, unnamed)
1350:  -> (crafted item, creator id: %u, star crumbs %d, element %d)
1351:  -> (produced item, creator id: %u)
1352:  -> (card(s):
1353: No item found in this player's %s.
1354: %d item(s) found in %d %s slots.

// @delitem
1355: Please enter an item name/ID, a quantity, and a player name (usage: #delitem <player> <item_name_or_ID> <quantity>).

// @font
1356: Returning to normal font.
1357: Use @font <1-9> to change your message font.
1358: Use 0 or no parameter to return to normal font.
1359: Invalid font. Use a value from 0 to 9.
1360: Font changed.
1361: Already using this font.

// @mount2
1362: NOTICE: If you crash with mount your LUA is outdated.
1363: You have mounted.
1364: You have released your mount.

// @accinfo
1365: Usage: @accinfo/@accountinfo <account_id/char name>
1366: You may search partial name by making use of '%' in the search, ex. "@accinfo %Mario%" lists all characters whose name contains "Mario".

// @set
1367: Usage: @set <variable name> <value>
1368: Usage: ex. "@set PoringCharVar 50"
1369: Usage: ex. "@set PoringCharVarSTR$ Super Duper String"
1370: Usage: ex. "@set PoringCharVarSTR$" outputs its value, Super Duper String.
1371: NPC variables may not be used with @set.
1372: Instance variables may not be used with @set.
1373: %s value is now: %lld
1374: %s value is now: %s
1375: %s is blank.

// @cash/@points
1376: Please close the cashshop before using this command.

// @reloadquestdb
1377: Quest database has been reloaded.

// @addperm
1378: Usage: %s <permission_name>
1379: -- Permission List
1380: '%s' is unknown permission.
1381: User '%s' already possesses the '%s' permission.
1382: User '%s' doesn't possess the '%s' permission.
1383: -- User '%s' Permissions
1384: User '%s' permissions updated successfully. The changes are temporary.

// @unloadnpcfile
1385: Usage: @unloadnpcfile <file name>
1386: File unloaded. Be aware that mapflags and monsters spawned directly are not removed.
1387: File not found.

// General command messages
1388: Charcommand failed (usage: %c<command> <char name> <parameters>).
1389: %s failed. Player not found.

// @cart
1390: Unknown Cart (usage: %s <0-%d>).
1391: You do not possess a cart to be removed
1392: Cart Added.

// atcommand.cpp::is_atcommand
1393: You can't use commands while dead.

// @clearstorage
1394: Your storage was cleaned.
1395: Your guild storage was cleaned.

// @clearcart
1396: You do not have a cart to be cleaned.
1397: Your cart was cleaned.

// @skillid (extension)
1398: -- Displaying first %d partial matches:

// @channel
1399: Unknown channel (usage: %s <#channel_name>).
1400: Unknown channel '%s' (usage: %s <#channel_name>).
1401: Channel '%s' is password-protected (usage: %s <#channel_name> <password>).
1402: You're not in that channel (use '@join <#channel_name>').
1403: You're now in the '%s' channel.
1404: %s failed.
1405: Channel name must start with '#'.
1406: Channel length must be between 3 and %d.
1407: Channel '%s' is not available.
1408: Channel password may not contain spaces.
1409: - #%s (%d users)
1410: ---- Public Channels ----
1411: Unknown color '%s'.
1412: You're not the owner of channel '%s'.
1413: '%s' channel color updated to '%s'.
1414: ---- Available options:
1415: * %s create <#channel_name> <channel_password>
1416: -- Creates a new channel.
1417: * %s list
1418: -- Lists all public channels.
1419: * %s list colors
1420: -- Lists all available colors for custom channels.
1421: * %s setcolor <#channel_name> <color_name>
1422: -- Changes channel text to the specified color (channel owners only).
1423: * %s leave <#channel_name>
1424: -- Leaves the specified channel.
1425: You're not part of the '%s' channel.
1426: You've left the '%s' channel.
1427: * %s bindto <#channel_name>
1428: -- Binds your global chat to the specified channel, sending all global messages to that channel.
1429: * %s unbind
1430: -- Unbinds your global chat from the attached channel, if any.
1431: Your global chat is now binded to the '%s' channel.
1432: Your global chat is not binded to any channel.
1433: Your global chat is now unbinded from the '#%s' channel.
1434: You're already in the '%s' channel.
1435: You're now in the '#%s' channel for '%s'.
1436: Channel password can't be over %d characters.
1437: Player '%s' is banned from the '%s' channel.
1438: You're currently banned from the '%s' channel.
1439: Channel '%s' contains no banned players.
1440: Player '%s' is not banned from this channel.
1441: Player '%s' is unbanned from the '%s' channel.
1442: Cleared all bans from the '%s' channel.
1443: ---- '#%s' Ban List:
1444: ---- Available Colors ----
1445: - %s
1446: You need to input an option.
1447: Unknown channel option '%s'.
1448: Channel '%s' deleted.
1449: Option '%s' is already enabled (use '@channel setopt %s 0' to disable).
1450: Option '%s' is enabled for channel '#%s'.
1451: Value '%d' for option '%s' is out of range (limit 0-10).
1452: Option '%s' is enabled for channel '#%s' at %d seconds.
1453: Option '%s' is disabled for channel '#%s'.
1454: Color set to '%s'.
1455: You're talking too fast!
1456: * %s ban <#channel_name> <player>
1457: -- Bans the specified player from the channel.
1458: * %s banlist <#channel_name>
1459: -- Lists all players banned from the specified channel.
1460: * %s unban <#channel_name> <player>
1461: -- Unbans the specified player from the channel.
1462: * %s setopt <#channel_name> <option> <value>
1463: -- Sets an option and value for the specified channel.
1464: Ban failed for player '%s'.
1465: Player '%s' is already banned from this channel.
1466: Input the number of seconds (0-10) for the '%s' option.
1467: * %s unbanall <#channel_name>
1468: -- Clears all bans from the specified channel.
1469: * %s delete <#channel_name>
1470: -- Destroys the specified channel.
1471: * %s list mine
1472: -- Lists all channels you have joined.
1473: * %s join <#channel_name> <channel_password>
1474: -- Joins the specified channel.
1475: ---- My Channels ----
1476: You have not joined any channels.

// @effect
1477: Please enter a valid effect id in the range from %d to %d.

// @partysharelvl
1478: Party share level range has been changed successfully.
1479: Failed to update configuration. Character server is offline.

// @autoloottype
1480: Item type not found.
1481: You're already autolooting this item type.
1482: Your autoloottype list has all item types. You can remove some items with @autoloottype -<type name or ID>.
1483: Autolooting item type: '%s' {%d}
1484: You're currently not autolooting this item type.
1485: Removed item type: '%s' {%d} from your autoloottype list.
1486: To add an item type to the list, use "@aloottype +<type name or ID>". To remove an item type, use "@aloottype -<type name or ID>".
1487: Type List: healing = 0, usable = 2, etc = 3, armor = 4, weapon = 5, card = 6, petegg = 7, petarmor = 8, ammo = 10
1488: "@aloottype reset" will clear your autoloottype list.
1489: Your autoloottype list is empty.
1490: Item types on your autoloottype list:
1491: Your autoloottype list has been reset.

// @dropall
1492: Usage: @dropall {<type>}
1493: Type List: (default) all = -1, healing = 0, usable = 2, etc = 3, armor = 4, weapon = 5, card = 6, petegg = 7, petarmor = 8, ammo = 10
1494: %d items are dropped (%d skipped)!

// Banking
1495: You can't withdraw that much money
1496: Banking is disabled

// Roulette
1497: Roulette is disabled

// @guild
1498: You cannot create a guild because you are in a clan.

// @clanspy
1499: Please enter a clan name/ID (usage: @clanspy <clan_name/ID>).
1500: No longer spying on the %s clan.
1501: Spying on the %s clan.
1502: Incorrect clan name/ID.

// PK Mode msgs
1503: You've entered a PK Zone.
1504: You've entered a PK Zone (safe until level %d).

// @setquest, @erasequest, @completequest
1505: Usage: %s <quest ID>
1506: Quest %d not found in DB.
1507: Character already has quest %d.
1508: Character doesn't have quest %d.

// @checkquest
1509: Checkquest value for quest %d
1510: >    HAVEQUEST : %d
1511: >    HUNTING   : %d
1512: >    PLAYTIME  : %d

// @changegm
1513: Currently in WoE hours, unable to delegate Guild leader
1514: You have to wait for a while before delegating a new Guild leader

// @hatereset
1515: Reset 'Hatred' monsters.

// @addfame
1516: Usage: %s <fame points>.
1517: Cannot add fame to class '%s'.
1518: %d points were added to '%s'.

// @grade
1519: Please enter a position and an amount (usage: @grade <equip position> <+/- amount>).
1520: %d items have been graded.

// @refine
1521: %d: Shadow Armor
1522: %d: Shadow Weapon
1523: %d: Shadow Shield
1524: %d: Shadow Shoes
1525: %d: Shadow Right Accessory
1526: %d: Shadow Left Accessory

// @setcard
1527: Please enter the position, the slot number and the card ID (usage: @setcard <equip position> <slot> <card_id>).
1528: You are not wearing any equipment in this position.
1529: The item type must be a card or enchant.
1530: Invalid card ID.
1531: Invalid position.
1532: Invalid slot number.

//@stockall
1533: You do not have a cart.
1534: Usage: @stockall {<type>}
1535: %d items are transferred (%d skipped)!

1536: Log configuration has been reloaded.
1537: Found skill '%s', unblocking...

//@macrochecker
1538: Macro detection has been started on %d players.

//Custom translations
import: conf/msg_conf/import/map_msg_eng_conf.txt
