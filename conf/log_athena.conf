//--------------------------------------------------------------
// rAthena Log Configuration File
//--------------------------------------------------------------
// Note 1: Value is a config switch (on/off, yes/no or 1/0)
// Note 2: Value is in percents (100 means 100%)
// Note 3: Value is a bit field. If no description is given,
//         assume unit types (1: Pc, 2: Mob, 4: Pet, 8: Homun, 16: Mercenary, 128: NPC, 512: Elemental)
//--------------------------------------------------------------

// Enable Logs? (Note 3)
// 0x00000000 - Don't log at all
// 0x00000001 - (T) Log trades
// 0x00000002 - (V) Log vending transactions
// 0x00000004 - (P) Log items drop/picked by players
// 0x00000008 - (L) Log items drop/looted by monsters
// 0x00000010 - (S) Log NPC transactions (buy/sell)
// 0x00000020 - (N) Log Script transactions (items deleted/acquired through quests)
// 0x00000040 - (D) Log items stolen from mobs (Steal/Gank)
// 0x00000080 - (C) Log player-used items (consumables/pet&hom&merc food/items used for skills&attacks)
// 0x00000100 - (O) Log produced/ingredient items
// 0x00000200 - (U) Log MVP prize items
// 0x00000400 - (A) Log player created/deleted items (through @/# commands)
// 0x00000800 - (R) Log items placed/retrieved from storage.
// 0x00001000 - (G) Log items placed/retrieved from guild storage.
// 0x00002000 - (E) Log mail system transactions.
// 0x00004000 - (I) Log auction system transactions.
// 0x00008000 - (B) Log buying store transactions
// 0x00010000 - (X) Log all other transactions (rentals expiring/inserting cards/items removed by item_check/
//           rings deleted by divorce/pet egg (un)hatching/pet armor (un)equipping/Weapon Refine skill/Remove Trap skill/Stylist)
// 0x00020000 - ($) Log cash transactions
// 0x00040000 - (K) Log account bank transactions
// 0x00080000 - (F) Removed bound items when guild/party is broken
// 0x00100000 - (Y) Log Roulette Lottery
// 0x00200000 - (Z) Merged items from item mergers process.
// 0x00400000 - (Q) Log items given from quest-granted drops.
// 0x00800000 - (H) Log items consumed by Private Airship system
// 0x01000000 - (J) Log Barter Shop transactions
// 0x02000000 - (W) Log Laphine system transactions
// 0x04000000 - (0) Enchantgrade UI
// 0x08000000 - (1) Reform UI
// 0x10000000 - (2) Enchant UI
// Example: Log trades+vending+script items+created items: 1+2+32+1024 = 1059
// Please note that moving items from inventory to cart and back is not logged by design.
enable_logs: 0xFFFFFFFF

// Use MySQL Logs? (Note 1)
sql_logs: yes

// LOGGING FILTERS
// =============================================================
// if any condition is true then the item will be logged
// 0 = Don't log at all
// 1 = Log any item
// Advanced Filter Bits by item type: ||
// 0002 - Healing items (0)
// 0004 - Etc Items(3) + Arrows (10)
// 0008 - Usable Items(2) + Lures,Scrolls(11) + Usable Cash Items(18)
// 0016 - Weapon(4)
// 0032 - Shields,Armor,Headgears,Accessories,etc(5)
// 0064 - Cards(6)
// 0128 - Pet Accessories(8) + Eggs(7) (well, monsters don't drop 'em but we'll use the same system for ALL logs)
// 0256 - Log expensive items ( >= price_items_log)
// 0512 - Log big amount of items ( >= amount_items_log)
// 1024 - Log refined items (if their refine >= refine_items_log )
// 2048 - Log rare items (if their drop chance <= rare_items_log )

// Examples: (log filters)
// log_filter: 1    = logs ANY items
// log_filter: 2    = logs only HEALING items
// log_filter: 4    = logs only Etc Items and Arrows
// log_filter: 64   = logs only Cards
// log_filter: 322  = logs only Healing items, Cards and those items which price is >= price_items_log
// log_filter: 4080 = logs all items (including all rare, big amount) exept healing, etc, arrows and useble ones
log_filter: 1

// Log Items which Refine >= refine_items_log
refine_items_log: 5
// Log Items whith min drop rate <= rare_items_log
// 1 = 0.01%, 100 = 1% drop chance, etc
rare_items_log: 100
// don't log it if the current item buy price < price_items_log
price_items_log: 1000
// don't log it if the current item amount < amount_items_log
amount_items_log: 100
//=============================================================

// Log Dead Branch Usage (Note 1)
log_branch: no

// Track Zeny Changes
// Filter settings
// 0 - don't log; 1 - log any zeny changes; 2.....1000000 - minimal absolute logging zeny value
log_zeny: 0

// Track Cash Changes (Note 1)
log_cash: yes

// Log MVP Monster Drops (Note 1)
// Outdated. Use Pick_Log instead. But this log could be useful to keep track slayed MVPs
log_mvpdrop: no

// Log AtCommands & Charcommands (Note 1)
// Only commands issued by player groups ('conf/groups.conf') with
// 'log_commands' setting set to 'true' will be logged.
log_commands: yes

// Log NPC 'logmes' commands (Note 1)
log_npc: yes

// Log CHAT (Global, Whisper, Party, Guild, Main chat, Clan) (Note 3)
// LOGGING FILTERS
// =============================================================
// 0 = Don't log at all
// 1 - Log Global messages
// 2 - Log Whisper messages
// 4 - Log Party messages
// 8 - Log Guild messages
// 16 - Log Main chat messages
// 32 - Log Clan messages
// Example:
// log_chat: 5 = logs both Whisper & Party messages
// log_chat: 8 = logs only Guild messages
// log_chat: 63 = logs everything
log_chat: 0

// Disable chat logging when WoE is running? (Note 1)
log_chat_woe_disable: no

// Log feeding
// Should pet or homunculus feeding be logged? (Note 3)
// 0: Disabled
// 1: Pet
// 2: Homunculus
log_feeding: 3

//Time-stamp format which will be printed for log file.
//Can at most be 20 characters long.
//Common formats:
// %I:%M:%S %p (hour:minute:second 12 hour, AM/PM format)
// %H:%M:%S (hour:minute:second, 24 hour format)
// %d/%b/%Y (day/Month/year)
//For full format information, consult the strftime() manual.
log_timestamp_format: %m/%d/%Y %H:%M:%S

// Logging files/tables
// Following settings specify where to log to. If 'sql_logs' is
// enabled, SQL tables are assumed, otherwise flat files.

// log_gm_db: log/atcommandlog.log
// log_branch_db: log/branchlog.log
// log_chat_db: log/chatlog.log
// log_mvpdrop_db: log/mvplog.log
// log_npc_db: log/npclog.log
// log_pick_db: log/picklog.log
// log_zeny_db: log/zenylog.log
// log_cash_db: log/cashlog.log
// log_feeding_db: log/feedinglog.log

log_gm_db: atcommandlog
log_branch_db: branchlog
log_chat_db: chatlog
log_mvpdrop_db: mvplog
log_npc_db: npclog
log_pick_db: picklog
log_zeny_db: zenylog
log_cash_db: cashlog
log_feeding_db: feedinglog

import: conf/import/log_conf.txt
