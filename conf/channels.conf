// Channel System Configuration File
channel_config: {

	/* Colors available */
	colors: {
		Default: "0xffffff" /* Custom channels will use the first in the list unless a color is selected through @channel. */
		Red: "0xff0000"
		Blue: "0x83cfe9"
		Orange: "0xe57c00"
		Cyan: "0x00b89d"
		Yellow: "0xffff90"
		Green: "0x28bf00"
		White: "0xFFFFFF"
		Purple: "0xD67FFF"
		LightGreen: "0xB6FF00"
		Normal: "0x00ff00"
		/* Add as many colors as you'd like. */
	}

	/**
	 * Private channel config
	 * - Always CHAN_TYPE_PUBLIC
	 * - Always displayed in chat log as "#channel_name: <name>: <chat>"
	 * - ID of private channels start at 1000
	 **/
	private_channel: {
		allow: true			  // (bool)	Allow player to create their own channel?
		color: "Default"	  // (string)	Default color, see colors
		delay: 1000			  // (int)	Chat delay for each member
		max_member: 1000	  // (int)	Max members
		self_notif: true	  // (bool)	Show message when player enters or leaves the channel
		join_notif: false	  // (bool)	Show message when player joined the channel
		leave_notif: false	  // (bool)	Show message when player leaves the channel
		/* Moderation feature for channel owner, allowed to: */
		ban: true			  // (bool)	Ban players
		kick: true			  // (bool)	Kick players
		color_override: false // (bool)	Allow players to change the private channel color to their own
		change_delay: false	  // (bool)	Allow players to change the private channel delay to their own
	}

	/**
	 * Default server channels
	 **/
	channels: (
	/**
	 * Structure
	{
		name: "#channel"		 // (string)	Channel name
		password: ""			 // (string)	Channel password
		alias: "[Channel]"		 // (string)	Message from this that channel will be displayed instead the channel name
		color: "Default"		 // (string)	Channel color
		type: "CHAN_TYPE_PUBLIC" // (string)	Channel type: CHAN_TYPE_PUBLIC, CHAN_TYPE_ALLY, CHAN_TYPE_MAP
		autojoin: false			 // (bool)	Players will auto join channel
		delay: 1000				 // (int)	Chat delay for each player
		leave: true				 // (bool)	Player is allowed to leave the channel
		chat: true				 // (bool)	Player is allowed to chat on this channel
		color_override: false	 // (bool)	Allow players to change the private channel color to their own
		self_notif: true		 // (bool)	Show message when player enters or leaves the channel
		join_notif: false		 // (bool)	Show message when player joined the channel
		leave_notif: false		 // (bool)	Show message when player leaves the channel
		groupid: (0,..,99)		 // (list,int)	Only players with valid group IDs are allowed to join. Group with 'channel_admin' can always enter the channel.
		/// All values above are default settings
	},							 // Use comma if followed by other channel
	 **/
	{
		name: "#global"
		alias: "[Global]"
		color: "White"
		type: "CHAN_TYPE_PUBLIC"
		delay: 1000
		autojoin: false
		leave: false
	},
	{
		name: "#support"
		alias: "[Support]"
		color: "Blue"
		type: "CHAN_TYPE_PUBLIC"
		delay: 1000
		autojoin: false
	},
	{
		name: "#trade"
		alias: "[Trade]"
		color: "LightGreen"
		type: "CHAN_TYPE_PUBLIC"
		delay: 1000
		autojoin: false
	}
	)

	/**
	 * Channel config for guild alliance
	 * For the structure, see the 'channels' above
	 **/
	ally: {
		name: "#ally"
		alias: "[Ally]"
		color: "Green"
		type: "CHAN_TYPE_ALLY" // DO NOT CHANGE THIS VALUE
		delay: 1000
		autojoin: false
		leave: true
		chat: true
	}

	/**
	 * Channel config for map channel
	 * For the structure, see the 'channels' above
	 **/
	map: {
		name: "#map"
		alias: "[Map]"
		color: "Yellow"
		type: "CHAN_TYPE_MAP" // DO NOT CHANGE THIS VALUE
		delay: 1000
		autojoin: false
		leave: true
		chat: true
	}
}
