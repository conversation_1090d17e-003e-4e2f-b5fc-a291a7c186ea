// Athena sockets Configuration file
// translated (davidsiaw)


// Display debug reports (When something goes wrong during the report, the report is saved.)
debug: no

// Linux/Epoll: Maximum Events per cycle
// Default Value:
//	(Maximum Supported Connections)/2
// NOTE: this controls the maximum collected socket-events per-cycle (call to epoll_wait())
//       for example settings this to 32 will allow up to 32 events (incoming data/new connections
//       per server-cycle.
// NOTE: Recommended Settings is at least half the maximum supported connections
//       Settings this to a lower value, may cause lags/delays
//       Depending on available CPU Time
// NOTE: This Setting is only available on Linux when build using EPoll as event dispatcher!
//
//epoll_maxevents: 1024

// How long can a socket stall before closing the connection (in seconds)
stall_time: 60

//----- IP Rules Settings -----

// If IP's are checked when connecting.
// This also enables DDoS protection.
enable_ip_rules: yes

// Order of the checks
//   deny,allow     : Checks deny rules, then allow rules. Allows if no rules match.
//   allow,deny     : Checks allow rules, then deny rules. Allows if no rules match.
//   mutual-failure : Allows only if an allow rule matches and no deny rules match.
// (default is deny,allow)

order: deny,allow
// order: allow,deny
// order: mutual-failure

// IP rules
//   allow : Accepts connections from the ip range (even if flagged as DDoS)
//   deny  : Rejects connections from the ip range
// The rules are processed in order, the first matching rule of each list (allow and deny) is used

// allow: 127.0.0.1
// allow: ***********/16
// allow: 10.0.0.0/*********
// allow: all

// deny: 127.0.0.1


//---- DDoS Protection Settings ----
// If ddos_count connection request are made within ddos_interval msec, it assumes it's a DDoS attack

// Consecutive attempts interval (msec)
// (default is 3000 msecs, 3 seconds)
ddos_interval: 3000

// Consecutive attempts trigger
// (default is 5 attempts)
ddos_count: 5

// The time interval after which the threat of DDoS is assumed to be gone. (msec)
// After this amount of time, the DDoS restrictions are lifted.
// (default is 600000 msecs, 10 minutes)
ddos_autoreset: 600000


import: conf/import/packet_conf.txt
