//--------------------------------------------------------------
// rAthena Battle Configuration File
// Originally Translated by <PERSON> <<EMAIL>>
// Made in to plainer English by <PERSON><PERSON>ker
//--------------------------------------------------------------
// Note 1: Value is a config switch (on/off, yes/no or 1/0)
// Note 2: Value is in percents (100 means 100%)
// Note 3: Value is a bit field. If no description is given,
//         assume unit types (1: Pc, 2: Mob, 4: Pet, 8: Homun, 16: Mercenary, 128: NPC, 512: Elemental)
//--------------------------------------------------------------

// Homunculus setting (Note 3)
// Activates various 'quirks' that makes them behave unlike normal characters.
// 0x01: Can't be targetted by support skills (except for their master)
// 0x04: Mobs will always go after them instead of players until attacked
// 0x08: Copy their master's speed on spawn/map-change
// 0x10: They display luk/3+1 instead of their actual critical in the
//       stat window (by default they don't crit)
// 0x20: Their Min-Matk is always the same as their max
hom_setting: 0x3D

// Should the cooldown or aftercast delay of homunculi skills be reset when
// the homunculus is vaporized?
// Default on official servers: yes for Pre-renewal, no for Renewal
//hom_delay_reset_vaporize: no

// Should the cooldown or aftercast delay of homunculi skills be reset when
// warping or changing maps?
// Default on official servers: yes for Pre-renewal, no for Renewal
//hom_delay_reset_warp: no

// The rate a homunculus will get friendly by feeding it. (Note 2)
homunculus_friendly_rate: 100

// Can you name a homunculus more then once? (Note 1)
hom_rename: no

// Minimum intimacy to evo the homunculus
homunculus_evo_intimacy_need: 91100

// Reset intimacy after evolution to:
homunculus_evo_intimacy_reset: 1000

// Intimacy needed to use Evolved Vanilmirth's Bio Explosion
hvan_explosion_intimate: 45000

// Show stat growth to the owner when an Homunculus levels up
homunculus_show_growth: yes

// Does autoloot work, when a monster is killed by homunculus only?
homunculus_autoloot: no

// Should homunculi Vaporize when Master dies? (Note 2)
// A homunculus will not Vaporize if their HP is below 80%.
homunculus_auto_vapor: 80

// Max level for regular Homunculus
homunculus_max_level: 99

// Max level for Homunculus S
homunculus_S_max_level: 250

// Growth level for Homunculus S
// This is the level at which homunculus S can use their growth tables
// Without this, a shuffle causes all levels of a Homunculus S to use their
// growth tables, causing imbalanced stats
homunculus_S_growth_level: 99

// Send auto-feed notice even if OFF (Note 1) 
// Official: yes
homunculus_autofeed_always: yes

// Is getting exp/item from the homunculus disabled when their master's idle?
// Set to no, or the amount of seconds (NOT milliseconds) that need to pass before considering
// a character idle.
// Characters in a chat/vending are always considered idle.
// A character's idle status is reset upon item use/skill use/attack (auto attack counts too)/movement.
// Their master will only receive items if 'homunculus_autoloot' is activated,
// otherwise they will be dropped on the ground as usual.
// NOTE: This option uses a special timer to track idle time, separated from the normal idle timer.
hom_idle_no_share: no

// How the server should measure the homunculus master's idle time? (for homunculus exp share and autoloot ONLY) (Note 3)
// (This will only work if 'hom_idle_no_share' is enabled).
// 0x001 - Walk Request
// 0x002 - UseSkillToID Request (Targetted skill use attempt)
// 0x004 - UseSkillToPos Request (AoE skill use attempt)
// 0x008 - UseItem Request (Including equip/unequip)
// 0x010 - Attack Request
// 0x020 - Chat Request (Whisper, Party, Guild, Battlegrounds, etc)
// 0x040 - Sit/Standup Request
// 0x080 - Emotion Request
// 0x100 - DropItem Request
// 0x200 - @/#Command Request
// Please note that at least 1 option has to be enabled.
// Be mindful that the more options used, the easier it becomes to cheat this features.
// Default: walk (0x1) + useskilltoid (0x2) + useskilltopos (0x4) + useitem (0x8) + attack (0x10) = 0x1F
// NOTE: This allows you to configure different settings for homunculus, separated from normal idle timer and 'idletime_option'.
// It will only apply to homunculus-only kills and it will not affect normal autoloot and party share options.
idletime_hom_option: 0x1F

// The rate at which homunculus gain experience from kills. (Note 2)
// Only applies to renewal mode.
// Official: 10%
homunculus_exp_gain: 10

// The hunger rate, less than or equal to, at which a homunculus' hunger delay changes. (Note 2)
// See 'homunculus_starving_delay' for the delay value.
// Set to 0 to disable.
// Official: 10
homunculus_starving_rate: 10

// Homunculi normally lose hunger every 60 seconds but when the 'homunculus_starving_rate' is met the hunger delay drops to 20 seconds (in milliseconds).
// Official: 20 seconds
homunculus_starving_delay: 20000
