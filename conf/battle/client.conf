//--------------------------------------------------------------
// rAthena Battle Configuration File
// Originally Translated by <PERSON> <<EMAIL>>
// Made in to plainer English by <PERSON><PERSON><PERSON>
//--------------------------------------------------------------
// Note 1: Value is a config switch (on/off, yes/no or 1/0)
// Note 2: Value is in percents (100 means 100%)
// Note 3: Value is a bit field. If no description is given,
//         assume unit types (1: Pc, 2: Mob, 4: Pet, 8: Homun, 16: Mercenary)
//--------------------------------------------------------------

// Minimum delay between whisper/global/party/guild messages (in ms)
// Messages that break this threshold are silently omitted. 
min_chat_delay: 0

// Valid range of dyes and styles on the client.
min_hair_style: 0
max_hair_style: 42
min_hair_color: 0
max_hair_color: 8
min_cloth_color: 0 
max_cloth_color: 7
min_body_style: 0
max_body_style: 1

// When set to yes, the damage field in packets sent from woe maps will be set
// to -1, making it impossible for GMs, Bots and Hexed clients to know the
// actual damage caused by attacks. (Note 1)
hide_woe_damage: no

// "hair style" number that identifies pet.
// NOTE: The client uses the "hair style" field in the mob packet to tell them apart from mobs.
// This value is always higher than the max hair-style available in said client.
// Known values to work (all 2005 clients):
// older sakexes: 20
// sakexe 0614: 24
// sakexe 0628 (and later): 100
pet_hair_style: 100

// Visible area size (how many squares away from a player they can see)
area_size: 14

// Maximum walk path (how many cells a player can walk going to cursor)
max_walk_path: 17

// Maximum allowed 'level' value that can be sent in unit packets.
// Use together with the aura_lv setting to tell when exactly to show the aura.
// NOTE: You also need to adjust the client if you want this to work.
// NOTE: Default is 99. Values above 127 will probably behave incorrectly.
// NOTE: If you don't know what this does, don't change it!!!
max_lv: 99

// Level required to display an aura.
// NOTE: This assumes that sending max_lv to the client will display the aura.
// NOTE: aura_lv must not be less than max_lv.
// Example: If max_lv is 99 and aura_lv is 150, characters with level 99~149
//          will be sent as being all level 98, and only characters with level
//          150 or more will be reported as having level 99 and show an aura.
aura_lv: 99

// Units types affected by max_lv and aura_lv settings. (Note 3)
// Note: If an unit type, which normally does not show an aura, is
//       set it will obtain an aura when it meets the level requirement.
// Default: 0 (none)
client_limit_unit_lv: 0

// Will tuxedo and wedding dresses be shown when worn? (Note 1)
wedding_modifydisplay: no

// Save Clothes color. (This will degrade performance) (Note 1)
save_clothcolor: yes

// Save body styles. (Note 1)
save_body_style: yes

// Do not display cloth colors for the wedding class?
// Note: Both save_clothcolor and wedding_modifydisplay have to be enabled
// for this option to take effect. Set this to yes if your cloth palettes 
// pack doesn't has wedding palettes (or has less than the other jobs)
wedding_ignorepalette: no

// Do not display cloth colors for the Xmas costume?
// Set this to yes if your cloth palettes pack doesn't have Xmas palettes (or has less than the other jobs)
xmas_ignorepalette: no

// Do not display cloth colors for the Summer costume?
// Set this to yes if your cloth palettes pack doesn't have Summer palettes (or has less than the other jobs)
summer_ignorepalette: no

// Do not display cloth colors for the Hanbok costume?
// Set this to yes if your cloth palettes pack doesn't have Hanbok palettes (or has less than the other jobs)
hanbok_ignorepalette: no

// Do not display cloth colors for the Oktoberfest costume?
// Set this to yes if your cloth palettes pack doesn't have Oktoberfest palettes (or has less than the other jobs)
oktoberfest_ignorepalette: no

// Set this to 1 if your clients have langtype problems and can't display motd properly
motd_type: 0

// Show rAthena version to users when the login?
display_version: yes

// When affected with the "Hallucination" status effect, send the effect to client? (Note 1)
// NOTE: Set to 'no' if the client lags due to the "Wavy" screen effect.
display_hallucination: yes

// Set this to 1 if your client supports status change timers and you want to use them
// Clients from 2009 onward support this
display_status_timers: yes

// Randomizes the dice emoticon server-side, to prevent clients from forging
// packets for the desired number. (Note 1)
client_reshuffle_dice: yes

// Sorts the cart, guild storage, inventory and storage before it is sent to the client. (Note 1)
// Official servers do not sort them.
// NOTE: Enabling this option degrades performance.
client_sort_storage: no

// Update enemy position while in invisible state? (Note 1)
// NOTE: Set to 'no' will make client won't update enemy position unless the players have "Intravision" effect.
//       So that will help client handling WPE - Maya Purple Hack stuff.
//       But it will screw 'the game animation display' while players in invisible state.
update_enemy_position: yes

// When a player teleports, changes maps, or logs in, will they face the direction they were facing before warped?
// Official: Disabled, players always face North.
spawn_direction: no

// Show the MVP EXP reward message for clients 2013-12-23cRagexe and newer? (Note 1)
// kRO removed the packet and this re-enables the message.
// Official: Disabled.
mvp_exp_reward_message: no

// Send ping timer
// Interval in seconds for each timer invoke.
ping_timer_inverval: 30

// Send packets timeout in seconds before ping packet can be sent.
ping_time: 20

// Show skill scale for clients 2015-12-23 and newer? (Note 1)
// Official: yes
show_skill_scale: yes

// Should the connection be dropped on server side after a player disconnection request was received? (Note 1)
// Note: Enabling this is known to cause problems on clients that make use of REST API calls.
// Official: no
drop_connection_on_quit: no

// Macro Detector retries
// Number of times someone can fail the macro detection before being banned.
// Official: 3 (minimum: 1)
macro_detection_retry: 3

// Macro Detector timeout
// Amount of time in milliseconds before the macro detection will fail and the user will be banned.
// Official: 60000
macro_detection_timeout: 60000

// Macro Detector punishment type
// 0 - Ban
// 1 - Jail
// Official: 0
macro_detection_punishment: 0

// Macro Detector punishment duration
// Amount of time in minutes that the punishment type is active for. Use 0 for infinite.
// Official: 0
macro_detection_punishment_time: 0

// Macrochecker delay (per map)
// Set to 0 to disable
macrochecker_delay: 600000
