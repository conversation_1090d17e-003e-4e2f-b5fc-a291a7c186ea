//--------------------------------------------------------------
// rAthena Battle Configuration File
// Originally Translated by <PERSON> <<EMAIL>>
// Made in to plainer English by <PERSON><PERSON><PERSON>
//--------------------------------------------------------------
// Note 1: Value is a config switch (on/off, yes/no or 1/0)
// Note 2: Value is in percents (100 means 100%)
// Note 3: Value is a bit field. If no description is given,
//         assume unit types (1: Pc, 2: Mob, 4: Pet, 8: Homun, 16: Mercenary)
//--------------------------------------------------------------

// The rate of time it takes to cast a spell (Note 2, 0 = No casting time)
casting_rate: 100

// Delay time after casting (Note 2)
delay_rate: 100

// Does the delay time depend on the caster's DEX and/or AGI? (Note 1)
// Note: On Official servers, neither Dex nor Agi affect delay time
delay_dependon_dex: no
delay_dependon_agi: no

// Minimum allowed delay for ANY skills after castbegin (in milliseconds) (Note 1)
// Note: Setting this to anything above 0 can stop speedhacks.
min_skill_delay_limit: 100

// Should attack motion be applied as minimum skill delay at castbegin? (Note 1)
// The client usually doesn't send skill commands faster than attack motion.
// However, there are a few tricks to make the client send commands faster.
// For some unit types like mercenaries and homunculus, there is no adequate
// server-sided delay in some situations, so it's possible to use skills faster
// than attack motion using these tricks.
// Set this to "yes" if you want to prevent these tricks and ensure that the
// delay between using skills at castbegin is at least attack motion.
amotion_min_skill_delay: no

// This delay is the min 'can't walk delay' of all skills.
// NOTE: Do not set this too low, if a character starts moving too soon after 
// doing a skill, the client will not update this, and the player will appear
// to "teleport" afterwards. Monsters use AttackMotion instead.
default_walk_delay: 300

// Completely disable skill delay of the following types (Note 3)
// NOTE: By default mobs don't have the skill delay as specified in the skill
//  database, but follow their own 'reuse' skill delay which is specified on
//  the mob skill db. When set, the delay for all skills become
//  min_skill_delay_limit.
no_skill_delay: 2

// At what dex does the cast time become zero (instacast)?
castrate_dex_scale: 150

// How much (dex*2+int) does variable cast turns zero?
vcast_stat_scale: 530

// On official servers, amotion delay is applied at castbegin. There is no amotion delay applied
// at castend. Set this to anything above 0 to also apply amotion delay at castend. (Note 2)
// NOTE: Setting this will break chaining of skills with cast time but no aftercast delay.
// The client-sided delays are different from skill to skill and usually range from 140 to 180.
// If you want to be secure, a value between 90 and 140 is recommended.
skill_amotion_leniency: 0

// Will normal attacks be able to ignore the delay after skills? (Note 1)
skill_delay_attack_enable: yes

// Range added to skills after their cast time finishes.
// Decides how far away the target can walk away after the skill began casting before the skill fails.
// 0 disables this range checking (default)
skill_add_range: 0

// If the target moves out of range while casting, do we take the items and SP for the skill anyway? (Note 1)
skill_out_range_consume: no

// Does the distance between caster and target define if the skill is a ranged skill? (Note 3)
// If set, when the distance between caster and target is greater than 3 the skill is considered long-range, otherwise it's a melee range.
// If not set, then the range is determined by the skill's range (if it is above 5, the skill is ranged).
// Default 14 (mobs + pets + homun)
skillrange_by_distance: 14

// Should the equipped weapon's range override the skill's range defined in the skill_db for most weapon-based skills? (Note 3)
// NOTE: Skills affected by this option are those whose range in the skill_db are negative. By default always the skill range is used.
// Note that if you want all monster skills to have a range of 9 you need to set monster_ai&0x400.
skillrange_from_weapon: 0

// Should a check on the caster's status be performed in all skill attacks?
// When set to yes, meteors, storm gust and any other ground skills will have 
// no effect while the caster is unable to fight (eg: stunned).
skill_caster_check: yes

// Should ground placed skills be removed as soon as the caster dies? (Note 3)
clear_skills_on_death: 0

// Should ground placed skills be removed when the caster changes maps? (Note 3)
clear_skills_on_warp: 15

//Setting this to YES will override the target mode of ground-based skills with the flag 0x01 to "No Enemies"
//The two skills affected by default are Pneuma and Safety Wall (if set to yes, those two skills will not protect everyone, but only allies)
//See db/(pre-)re/skill_db.yml for more info.
defunit_not_enemy: no

// Should skills always do at least 'hits' damage when they don't miss/are blocked?
// Many skills will have their damage multiplied by their number of hits (see skill_db), these will always deal 1 HP
// damage per hit, even against plants. But some skills are actually a single hit that is just displayed as multiple
// hits. For these skills, damage gets divided by number of hits and rounded down. That means that plants won't take
// any damage from them. Examples: Sonic Blow, Lord of Vermillion
// With this setting, you can change the official behavior and make these skills deal at least 1 HP damage per hit.
// Values: 1 for weapon-based attacks, 2 for magic attacks, 4 for misc attacks.
skill_min_damage: 0

// The delay rate of monk's combo (Note 2)
combo_delay_rate: 100

// Use alternate auto Counter Attack Skill Type? (Note 3)
// For those characters on which it is set, 100% Critical,
// Otherwise it disregard DEF and HIT+20, CRI*2
auto_counter_type: 15

// Can ground skills be placed on top of each other? (Note 3)
// By default, skills with UF_NOREITERATION set cannot be stacked on top of 
// other skills, this setting will override that.
skill_reiteration: 0

// Can ground skills NOT be placed underneath/near players/monsters? (Note 3)
// If set, only skills with UF_NOFOOTSET set will be affected.
skill_nofootset: 1

// Should traps (hunter traps + quagmire) change their target to "all" inside gvg/pvp grounds? (Note 3)
// Default on official servers: 1 (for players)
gvg_traps_target_all: 1

// Traps visibility setting (trap with UF_HIDDEN_TRAP flag):
// 0 = Always visible
// 1 = Enable invisibility in versus maps (GVG/PVP/BG)
// 2 = Enable invisibility in all maps
// Default on official servers: 0 for Pre-renewal, 2 for Renewal
//traps_setting: 0

// Restrictions applied to the Alchemist's Summon skills (add as necessary)
// 1: Enable players to damage the floras outside of versus grounds.
// 2: Enable players to damage marine spheres outside of versus grounds.
// 4: Disable having different types out at the same time
//    (eg: forbid summoning anything except hydras when there's already 
//     one hydra out)
// 8: Enable Marine Spheres to damage own Homunculus and summons outside PVP
// Default on official servers: 15 for Pre-renewal, 12 for Renewal
//alchemist_summon_setting: 15

// Whether placed down skills will check walls (Note 1)
// (ex. Storm Gust cast against a wall will not hit the other side.)
// Skills that don't have the "PathCheck" unit flag ignore this setting.
skill_wall_check: yes

// When cloaking, Whether the wall is checked or not. (Note 1)
// Note: When the skill does not checks for walls, you will always be considered
//  as if you had a wall-next to you (you always get the wall-based speed). 
//  Add the settings as required, being hit always uncloaks you.
// 
// 0 = doesn't check for walls
// 1 = Check for walls
// 2 = Cloaking is not cancelled when attacking.
// 4 = Cloaking is not cancelled when using skills
player_cloak_check_type: 1
monster_cloak_check_type: 4

// Can't place unlimited land skills at the same time (Note 3)
land_skill_limit: 9

//Determines which kind of skill-failed messages should be sent:
// 1 - Disable all skill-failed messages.
// 2 - Disable skill-failed messages due to can-act delays.
// 4 - Disable failed message from Snatcher
// 8 - Disable failed message from Envenom
display_skill_fail: 2

// Can a player in chat room (in-game), be warped by a warp portal? (Note 1)
chat_warpportal: no

// What should the wizard's "Sense" skill display on the defense fields?
// 0: Do not show defense
// 1: Base defense [RE default]
// 2: Vit/Int defense
// 3: Both (the addition of both) 
sense_type: 1

// Which finger offensive style will be used?
// 0 = Aegis style (single multi-hit attack)
// 1 = Athena style (multiple consecutive attacks)
finger_offensive_type: 0

// Grandcross Settings (Don't mess with these)
// Officially, Grand Cross has four damage waves, but each cell will stop
// dealing damage on the next wave after it already dealt at least 3 hits.
// A moving monster can take up to 4 hits, while a stack of monsters may only
// take 1-3 hits.
// Set this to yes if you want all waves to deal damage to all targets.
// Hint: If you want to reduce the number of waves, you need to reduce the
// duration of the skill (e.g. to 800ms for three damage waves).
gx_allhit: no

// Grandcross display type (Default 1)
// 0: Yellow character
// 1: White character
gx_disptype: 1

// Max Level Difference for Devotion
devotion_level_difference: 10

// Using 'old' behavior for devotion vs reflect damage? (Note 2)
// Default is 0 (official). If 'devotion_rdamage' is > 0 (chance to devote the reflected damage),
// when player with devotion attacks player with reflect damage ability (item bonus or skill),
// the damage will be taken by the person who provides devotion instead the attacker.
devotion_rdamage: 0

// Officially, reflecting shield (SC_REFLECTDAMAGE) reflects physical damage by skill or normal attack.
// But if the target is being devoted, it ONLY reflects the damage for melee skill. (Note 1)
devotion_rdamage_skill_only: yes

// On AEGIS there is a bug when the player who is under devotion is sitting and getting hit.
// The player stands up on client side, but will still remain sitting on server side.
// Because of this the player will not be able to walk anymore, until the player sat down/stood up again or used @refresh.
// You can read more about it on https://github.com/rathena/rathena/issues/1927
// Default: yes (because it is a recommended bug fix from our side)
// Official: no
devotion_standup_fix: yes

// If no than you can use the ensemble skills alone. (Note 1)
player_skill_partner_check: yes

// Remove trap type
// 0 = Aegis system : Returns 1 'Trap' item
// 1 = Athena system : Returns all items used to deploy the trap
skill_removetrap_type: 0

// Does using bow to do a backstab give a 50% damage penalty? (Note 1)
backstab_bow_penalty: yes

// How many times you could try to steal from a mob.
// Note: It helps to avoid stealing exploit on monsters with few rare items
// Use 0 to disable (max allowed value is 255)
skill_steal_max_tries: 0

// Should random options be applied to stolen items? (Note 1)
// Official: no
skill_steal_random_options: no

// Level and Strength of "MVP heal". When someone casts a heal of this level or
// above, the heal formula is bypassed and this value is used instead.
max_heal: 9999
max_heal_lv: 11

// Emergency Recall Guild Skill setting (add as appropriate).
// Note that for the skill to be usable at all, 
// you need at least one of 1/2 and 4/8
// 1: Skill is usable outside of woe.
// 2: Skill is usable during woe.
// 4: Skill is usable outside of GvG grounds
// 8: Skill is usable on GvG grounds
//16: Disable skill from "nowarpto" maps
//    (it will work on GVG castles even if they are set to nowarpto, though)
emergency_call: 11

// Guild Aura Skills setting (add as appropriate).
// (This affects GD_LEADERSHIP, GD_GLORYWOUNDS, GD_SOULCOLD and GD_HAWKEYES)
// Note that for the skill to be usable at all, 
// you need at least one of 1/2 and 4/8
// 1: Skill works outside of woe.
// 2: Skill works during woe.
// 4: Skill works outside of GvG grounds
// 8: Skill works on GvG grounds
//16: Disable skill from affecting Guild Master
guild_aura: 31

// Max Possible Level of Monster skills
// Note: If your MVPs are too tough, reduce it to 10.
mob_max_skilllvl: 100

// Allows players to skip menu when casting Teleport level 1
// Menu contains two options. "Random" and "Cancel"
skip_teleport_lv1_menu: no

// Allow use of SG skills without proper day (Sun/Moon/Star) ?
allow_skill_without_day: no

// Allow use of ES-type magic on players?
allow_es_magic_player: no

// Miracle of the Sun, Moon and Stars skill ratio
// Valid values range from 1 (0.005% per hit) to 20000 (100% per hit)
// This chance is further reduced if AGI is above 46 (92 = halved chance)
sg_miracle_skill_ratio: 1

// Miracle of the Sun, Moon and Stars skill duration in milliseconds
sg_miracle_skill_duration: 3600000

// Angel of the Sun, Moon and Stars skill ratio (100% = 10000)
sg_angel_skill_ratio: 10

// Skills that bHealPower has effect on
// 1: Heal, 2: Sanctuary, 4: Potion Pitcher, 8: Slim Pitcher, 16: Apple of Idun,
// 32: Coluceo Heal, 64: Highness Heal, 128: Mediale Votum, 256: Dilectio Heal
skill_add_heal_rate: 487

// Whether the damage of EarthQuake with a single target on screen is able to be reflected.
// Note: On official servers, EQ is reflectable when there is only one target on the screen, 
//	 which might be an exploit to hunt the MVPs.
eq_single_target_reflectable: yes

// On official server, you will receive damage from Reflection and some Tarot Card even in invincible status.
// When this setting is enabled, it allows you to immune to all kinds of damage, including those stated previous.
// (The number will show but no actual damage will be done)
invincible.nodamage: no

// Dancing Weapon Switch
// On official servers, a fix is in place that prevents the switching of weapons to cancel songs.
// Default: yes
dancing_weaponswitch_fix: yes

// Skill Trap Type
// On official servers if a unit is completely immune to knockback, it will still walk to the last target tile before
// stopping when inflicted by a stopping status effect (including traps like Ankle Snare and Spiderweb). All traps on
// the way will be activated.
// This does NOT include being immune to knock back from equip. This bonus only helps against knockback skills.
// 0: (official)
// 1: Stop effects in GvG/WoE make units stop immediately.
// 2: Stop effects make monsters immune to knockback / bosses stop immediately.
// 3: 1+2
skill_trap_type: 0

// Trap Multi Trigger (Note 1)
// Until episode 11.2, traps would trigger for each unit on them within the same interval
// Enable this if you want to restore this old behavior
// This setting is only recommended for pre-renewal as traps deal a lot more damage in renewal
multi_trigger_trap: no

// Area of Bowling Bash chain reaction (pre-renewal only)
// 0: Use official gutter line system
// 1: Gutter line system without demi gutter bug
// 2-20: Area around caster (2 = 5x5, 3 = 7x7, 4 = 9x9, ..., 20 = 41x41)
// Note: If you knock the target out of the area it will only be hit once and won't do splash damage
bowling_bash_area: 0

// Pushback behavior (Note 1)
// On official servers, hitting a wall will always cause the unit to stop moving.
// If "no", the unit will continue moving when approaching walls diagonally (old Athena behavior).
path_blown_halt: yes

// Taekwon Mission mob name check
// iRO Wiki States: If your target is Goblin, any monster called "Goblin" will count toward the mission.
// 0: Off (default)
// 1: All 5 of the Goblin monsters will count, regardless of Mob ID (Mob ID: 1122-1126) - iRO default
// 2: Any monster with the same exact name will count, regardless of Mob ID - Comparison based off of jName
taekwon_mission_mobname: 0

// Can a player Teleport on top of a Map Warp Portal? (Note 1)
// On official servers players have been unable to do so.
teleport_on_portal: no

// Is the knockback direction for Cart Revolution always West? (Note 1)
// On official servers it will knock the target always to the West. If disabled it will knock the target backwards.
cart_revo_knockback: yes

// On official servers, Arrow Shower blow direction always rely on skill placed location to target instead of caster to target
arrow_shower_knockback: yes

// On official servers, Storm Gust consists of 81 units that all deal 3x3 splash damage "away from center". Due to
// south-western cells being processed first, this usually leads to a knockback to the northeast. Knockback at the
// edges will be away from SG. Knockback direction can also be influenced by Ganbantein and Land Protector. If you
// punch a hole into SG it will for example create a "suck in" effect.
// If you disable this setting, the knockback direction will be completely random (eAthena style).
stormgust_knockback: yes

// For RENEWAL_CAST (Note 2)
// By default skill that has '-1' value for Fixed Casting Time will use 20% of cast time
// as Fixed Casting Time, and the rest (80%) as Variable Casting Time.
// Put it 0 to disable default Fixed Casting Time (just like 0 in the skill_db.yml).
default_fixed_castrate: 20

// On official servers, skills that hit all targets on a path (e.g. Focused Arrow Strike and First Wind) first
// calculate one of the eight directions and then apply an AoE based on that direction. This means there can be
// areas that such skills can't hit. If you target a monster in such an area, only this monster will be hit.
// The 3rd job skills Flame Launcher and Cannon Spear can completely miss.
// Set this to "no" to calculate a path from the caster to the target instead and hit everything near that path.
// You can adjust splash and maxcount in the skill_db to adjust the width and length of these skills.
// Note: Brandish Spear will always use this algorithm due to its special damage behavior.
skill_eightpath_algorithm: yes

// Should skills that use skill_eightpath_algorithm include targets in the caster's cell?
// Official: yes
skill_eightpath_same_cell: yes

// Can damage skill units like icewall and traps (Note 3)
// On official servers, players can damage icewalls and some traps with skills. When monsters use skills, damage
// will show on the icewalls and traps, but it is not actually substracted from the durability.
// The official setting makes it quite easy to trap MVPs, set this to 31 if you want all units to be able to
// damage skills (previous behavior).
can_damage_skill: 1

// Land Protector behavior (Note 1)
// On official servers, players standing on the border (outer cell) of the Land Protector can still be affected/hit
// by AoE skills (if the skill has a splash effect, such as Storm Gust). The Athena behavior ignores AoE affects/hits
// while players are standing on the border.
// Official: 0
// Legacy Athena: 1
land_protector_behavior: 0

// NPC EMOTION behavior (Note 1)
// On official servers, certain mobs cast NPC EMOTION skill which displays an emoticon and change their mode from
// Aggressive to Passive for a certain time. The Athena behavior does not change their mode to Passive.
// Official: 0
// Legacy Athena: 1
npc_emotion_behavior: 0

// Should Tarot Card of Fate have the same chance for each card to occur? (Note 1)
// Official chances: 15%: LOVERS | 10%: FOOL, MAGICIAN, HIGH PRIESTESS, STRENGTH, SUN | 8%: TEMPERANCE
// 7%: CHARIOT | 6%: THE HANGED MAN | 5%: DEATH, STAR | 2%: TOWER | 1%: WHEEL OF FORTUNE, DEVIL
// If you set this to "yes", the chance for each card becomes 1/14.
tarotcard_equal_chance: no

// Should Dispel work on songs when the target is not in the song area? (Note 1)
// On official servers, it's impossible to dispel songs.
// Hint: Also affects the Rebellion skill "Vanishing Buster".
dispel_song: no

// Should song buff durations be refreshed when re-entering the song area? (Note 1)
// Officially, once you leave a song area, buff duration becomes finite and cannot be refreshed until it ends.
refresh_song: no

// Should the song buff icon show the real duration? (Note 1)
// If no, it will always show as infinite. 
// If yes, whenever the buff switches from infinite to finite or vice-versa, the icon will be updated.
// Please note that on renewal clients, refreshing the icon also shows the buff animation again.
refresh_song_icon: no

// Banana Bomb from Genetic's Make Bomb skill sitting duration.
// Official duration is 1000ms * Thrower's Job Level / 4.
// 0: Uses the official duration
// X: Enter a custom duration in milliseconds.
banana_bomb_duration: 0

// Should items that you try to create be dropped if you have no space left in your inventory? (Note 1)
// Official: no
// Legacy rAthena logic: yes
skill_drop_items_full: no

// EDP setting: (Note 3)
// When switching/unequipping a right hand weapon, should EDP be removed? EDP can't be used with bare hand.
// 0: Disabled (rAthena legacy and pre-renewal behavior).
// 1: Enabled on pre-renewal.
// 2: Enabled on renewal.
// 3: 1+2
switch_remove_edp: 2

// Max Level Difference when casting Meister's Attack Machine on other party members.
// Default: 15
attack_machine_level_difference: 15
