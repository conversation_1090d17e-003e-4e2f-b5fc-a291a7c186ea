//--------------------------------------------------------------
// rAthena Battle Configuration File
// Originally Translated by <PERSON> <<EMAIL>>
// Made in to plainer English by <PERSON><PERSON><PERSON>
//--------------------------------------------------------------
// Note 1: Value is a config switch (on/off, yes/no or 1/0)
// Note 2: Value is in percents (100 means 100%)
// Note 3: Value is a bit field. If no description is given,
//         assume unit types (1: Pc, 2: Mob, 4: Pet, 8: Homun, 16: Mercenary, 128: NPC, 512: Elemental)
//--------------------------------------------------------------

// The HP rate of MVPs. (Note 2)
mvp_hp_rate: 100

// The HP rate of normal monsters (that is monsters that are not <PERSON>'s) (Note 2)
monster_hp_rate: 100

// Defines various mob AI related settings. (Note 3)
// 0x0001: When enabled, mobs will update their target cell every x cells moved.
//         (normally they never update their target cell until they are one cell
//         before the end of their walkpath. x = monster_chase_refresh, see below)
// 0x0002: Makes mob use their "rude attack" skill (usually warping away) if they
//         are attacked and they can't attack back regardless of how they were
//         attacked (eg: GrimTooth), otherwise, their "rude attack" is only activated
//         if they can't melee reach the target (eg: sniping)
// 0x0004: If not set, mobs that can change target only do so when attacked within a
//         distance of [attack range+1], otherwise mobs may change target and chase 
//         ranged attackers. This flag also overrides the 'provoke' target.
// 0x0008: When set, mobs scatter as soon as they lose their target. Use this mode
//         to make it much harder to mob-train by hiding and collecting them on a
//         single spot (ie: GrimTooth training)
// 0x0010: If set, mob skills defined for friends will also trigger on themselves.
// 0x0020: When set, the monster ai is executed for all monsters in maps that 
//         have players on them, instead of only for mobs who are in the vicinity
//         of players.
// 0x0040: When set, when the mob's target changes map or is out of sight, the mob
//         will walk towards npc-warps and/or priest warps in its sight of view.
//         It will only walk to warps it can use and only to warps that bring it back
//         in sight of the target (use with mob_warp below).
// 0x0080: If not set, mobs on attack state will only change targets when attacked
//         by normal attacks. Set this if you want mobs to also switch targets when
//         hit by skills.
// 0x0100: When set, a mob will pick a random skill from it's list and start from
//         that instead of checking skills in orders (when unset, if a mob has too
//         many skills, the ones near the end will rarely get selected)
// 0x0200: When set, a mob's skill re-use delay will not be applied to all entries of
//         the same skill, instead, only to that particular entry (eg: Mob has heal
//         on six lines in the mob_skill_db, only the entry that is actually used
//         will receive the delay). This will make monsters harder, especially MvPs.
// 0x0400: Set this to make mobs have a range of 9 for all skills. Otherwise, they 
//         will obey the normal skill range rules.
// 0x0800: When set, monsters that are provoked will always change target to the
//         provoking person, even if they would usually not change target on attack.
// 0x1000: When set, when a monster picks a skill but can't use it because there is
//         no valid target in range, it will look for another skill it can use.
//         This makes e.g. Greatest General use "Earth Spike" at range 8-9 whereas
//         officially it would only use it after already having used "Blind Attack"
//         at range 0-7.
// 0x2000: When set, monsters will move right after they attacked or used a skill.
//         Usually a monster's AI is inactive for its attack motion.
// Example: 0x140 -> Chase players through warps + use skills in random order.
monster_ai: 0

// How often should a monster rethink its chase?
// 0: Every 100ms (MIN_MOBTHINKTIME)
// 1: Every cell moved
// x: Every x cells moved or one cell before the end of the chase path
// 32 (max): One cell before the end of the chase path (official)
// Regardless of this setting, a monster will always check for targets in attack range.
// Decrease this value if you want to make monsters be more reactive while chasing.
// If you want monsters to update their target cell while chasing you also need to enable
// monster_ai 0x0001, see above. Otherwise this only defines the maximum amount of cells
// monsters will move after they lost their target (hide, no line of sight, etc.).
monster_chase_refresh: 32

// Should mobs be able to be warped (add as needed)?
// 0: Disable.
// 1: Enable mob-warping when standing on NPC-warps
// 2: Enable mob-warping when standing on Priest Warp Portals
// 4: Disable warping when the target map is a 'nobranch' map.
// 8: Enable mob-warping when standing on Dimensional Door
mob_warp: 0

// Defines the time (in ms) during which monsters will have their AI active
// after all players have left their vicinity.
// Even after this time they will still walk randomly and use idle skills until
// all players that saw them logged out or until they get teleported, recalled
// or otherwise removed from the map.
mob_active_time: 0
boss_active_time: 0

// Mobs and Pets view-range adjustment (range2 column in the mob_db) (Note 2)
view_range_rate: 100

// Chase Range is the base minimum-chase that a mob gives before giving up
// (as long as the target is outside their field of view). This is the range3
// column in the mob_db. (Note 2)
chase_range_rate: 100

// Which level of of Vulture's Eye and Snake's Eye should monsters have learned?
// Officially monsters don't have these skills learned, so their ranged skills
// only have a range of 9. If you put a number higher than 0, their range will
// be increased by that number.
monster_eye_range_bonus: 0

// Range in which looters search for loot (max 32)
// Official: 12
// Legacy Athena: 10
loot_range: 12

// Range in which assist mobs search for allies to assist (max 32)
// Official: 11
// Legacy Athena: 10
assist_range: 11

// Allow monsters to be aggresive and attack first? (Note 1)
monster_active_enable: yes

// Should the mob_db names override the mob names specified in the spawn files?
// 0: No
// 1: always use the mob_db Name column (english mob name)
// 2: always use the mob_db JName column (original Kro mob name)
override_mob_names: 0

// Monster damage delay rate (Note 2)
// This affects the damage delay that is sent to the client and is the base value for the walk delay.
monster_damage_delay_rate: 100

// Looting monster actions.
// 0 = Monster will consume the item.
// 1 = Monster will not consume the item.
monster_loot_type: 0

// How does monster search floor item to loot?
// 0: Closest (old Athena style)
// 1: Oldest in range (Official)
monster_loot_search_type: 1

// Chance of mob casting a skill (Note 2)
// Higher rates lead to 100% mob skill usage with no/few normal attacks.
// Set to 0 to disable mob skills.
mob_skill_rate: 100

// Mob skill delay adjust (Note 2)
// After a mob has casted a skill, there is a delay before being able to
// re-cast it. Note that skills with a delay of 0 can't be affected by this
// setting.
mob_skill_delay: 100

// Rate of monsters on a map, 200 would be twice as many as normal. (Note 2)
mob_count_rate: 100

// Respawn rate of monsters on a map. 50 would make mobs respawn twice as fast (half delay time) (Note 2)
//Note: This does not affects mobs with immediate respawn (most normal mobs)
mob_spawn_delay: 100
plant_spawn_delay: 100
boss_spawn_delay: 100

// Should the mobs respawn time have variance applied to it? (Note 3)
// On official servers monsters spawned by the boss_monster command (mostly MVPs) have variance, legacy Athena behavior is that variance is applied for all mobs.
// 0: None
// 1: Boss monsters (official)
// 2: Normal monsters
// 3: All monsters (legacy Athena)
mob_spawn_variance: 1

// Should mobs not spawn within the viewing range of players?
// 0 is disabled, otherwise it is the number of retries before giving up 
// and spawning the mob within player-view anyway, unless the max (100) is used,
// in which case the mob will not be spawned, and it'll be retried again in
// 5 seconds.
// NOTE: This has no effect on mobs that always spawn on the very same cell
// (like ant eggs) except if you set it to the max.
no_spawn_on_player: 0

// Should spawn coordinates in the mob-spawn files be ignored? (Note 1)
// If set to yes, all monsters will have a random respawn spot across the whole
// map regardless of what the mob-spawn file says.
force_random_spawn: no

// Should each monster's center cell be randomized? (Note 1)
// Officially, at server start, each monster's center cell is set to a random cell in the spawn area.
// Each time the monster spawns it will spawn in an area around its center cell rather than the
// original center of the spawn definition. This results in a much larger total spawn area and a
// different experience each server start.
// Set this to "no" if you want all monsters of a spawn to spawn around the original center of the
// spawn definition, making the total spawn area much smaller (old eAthena behavior).
randomize_center_cell: yes

// Do summon slaves inherit the passive/aggressive traits of their master?
// 0: No, retain original mode.
// 1: Slaves are always aggressive.
// 2: Slaves are always passive.
// 3: Same as master's aggressive/passive state.
// 4: Mode is overwritten with slave mode (official)
slaves_inherit_mode: 4

// Do summon slaves have the same walking speed as their master?
// Does not apply to Marine Spheres summoned by an Alchemist
// NOTE: The default is 3 for official servers.
// 0: Never.
// 1: If the master can walk
// 2: If the master can't walk (even motionless mobs have a speed
//    entry in their mob_db)
// 3: Always
slaves_inherit_speed: 3

// Should MVP slaves retain their target when summoned back to their master? (Note 1)
mob_slave_keep_target: yes

// Should slaves teleport back to their master if they get too far during chase? (Note 1)
// Officially they can be moved as far away from their master as you want.
slave_stick_with_master: no

// Should slaves always be active when their master is active? (Note 1)
// Officially it can be that the master is active but the slaves are not.
slave_active_with_master: no

// Will summoned monsters (alchemists, or @summon'ed monsters) attack cause a
// chance of triggering the master's autospell cards? (Note 1)
summons_trigger_autospells: yes

// When a mob is attacked by another monster, will the mob retaliate against the master of said mob instead of the mob itself?
// NOTE: Summoned mobs are both those acquired via @summon and summoned by Alchemists
retaliate_to_master: yes

// Whether mobs should change target temporarily when a skill triggers a counter mob skill (Note 1)
// eg: Mob attacks player B, and player A casts a skill C. If set to yes and the
// mob has a skill that is triggered by skill C, then A will be the target of
// the skill, otherwise B will be targetted by the reaction skill.
mob_changetarget_byskill: no

// If monster's class is changed will it fully recover HP? (Note 1)
monster_class_change_full_recover: yes

// Display some mob info next to their name? (add as needed)
// (does not works on guardian or Emperium)
// 1: Display mob HP (Hp/MaxHp format)
// 2: Display mob HP (Percent of full life format)
// 4: Display mob's level
show_mob_info: 0

// Zeny from mobs
zeny_from_mobs: no

// Monsters level up (monster will level up each time a player is killed and they will grow stronger)
// Exp rate is calculated ((monster level-original monster level)*(exp*(mobs_level_up_exp rate/100)))
// NOTE: Does not apply to WoE Guardians.
mobs_level_up: no
mobs_level_up_exp_rate: 1

// Dynamic Mobs Options
// Use dynamic mobs? (recommended for small-medium sized servers)
dynamic_mobs: yes

// Remove Mobs even if they are hurt
mob_remove_damaged: yes

// Delay before removing mobs from empty maps (default 5 min = 300 secs)
mob_remove_delay: 300000

// Defines on who the mob npc_event gets executed when a mob is killed.
// Type 1: On the player that killed the mob (if killed by a non-player, resorts to type 0)
// Type 0: On the player that did the most damage to the mob.
// NOTE: This affects who gains the Castle when the Emperium is broken. 
mob_npc_event_type: 1

// Time in milliseconds to activate protection against Kill Steal
// Set to 0 to disable it.
// If this is activated and a player is using @noks, damage from others players (KS) not in the party
// will be reduced to 0.
ksprotection: 0

// Whether or not to spawn the mvp tomb.
// See http://irowiki.org/wiki/MVP#Gravestone
mvp_tomb_enabled: yes

// Delay before the MVP tomb is spawned.
// Default: 9 seconds
mvp_tomb_delay: 9000

// Whether or not the size of specially summoned mobs influences experience, drop rates,
// and stats. The rates will be doubled for large mobs, and halved for small ones.
// This is only invoked under the 'monster' command, @monsterbig, and @monstersmall. (Note 1)
// Default: no
mob_size_influence: no

// How should a monster be trapped by an icewall casted directly on it?
// On official servers, monsters can only leave an icewall to the west and south. If their target is north or east of
// them they will continously try to chase it but fail doing so. This brings them into a loop during which they will use
// idle and chase skills. Boss monsters on the other hand will behave like a trapped monster, do not move and will use
// idle and rudeattacked skills (when attacked).
// 0: Monster won't be stuck in icewall at all.
// 1: Monster will behave like a trapped monster.
// 2-255: Number of loops a monster will go through the behavior described above before it frees itself from icewall.
// NOTE: On some servers, normal monsters can free themselves after 15-35 second depending on their speed. On other
// servers, they will be stuck inside icewall until it expires. Also, many official servers (e.g. iRO) have casting
// icewall completely blocked on all maps that have boss monsters on them.
// Default (least exploitable): mob - 75, boss - 0
// Default (most official): mob - 220, boss - 1
mob_icewall_walk_block: 75
boss_icewall_walk_block: 0

// Should HP bars be shown for monsters?
// 2012-04-04aRagexeRE or higher client required.
monster_hp_bars_info: yes

// Should a monster respawn and a warning printed to the map server when a monster couldn't move for a long time?
// This can be legit gameplay (e.g. players keeping an MVP stuck inside icewall), but if you want to prevent any
// exploits and be notified about them, you can set this to yes.
monster_stuck_warning: no

// Rate at which monsters use their idle skills when there are no players nearby (Note 2)
// On official servers monsters use their idle skills if they have been spotted once, even if there are no players nearby anymore.
// On small-medium sized servers this can cause all monsters like eggs and Fabre/Pupa to metamorph.
// To switch it off, set it to 0.
mob_nopc_idleskill_rate: 100
boss_nopc_idleskill_rate: 100

// Rate at which monsters move when there are no players nearby (Note 2)
// On official servers monsters always move if they have been spotted once, even if there are no players nearby anymore.
// To switch it off, set it to 0.
mob_nopc_move_rate: 100
boss_nopc_move_rate: 100

// When killing a monster, do AG_BATTLE type achievements trigger for everyone in the same party within the area?
// Area is limited to area_size battle config.
achievement_mob_share: no

// Absolute minimum respawn time in milliseconds of a monster.
// Also used in delaying the spawning of guardians when a guild is not loaded.
// Default (Official): 1000
mob_respawn_time: 1000

// Defines the time (in ms) after which a monster unlocks its target when trapped.
// During this time monsters will still be in idle mode and use idle skills on random
// targets, but they continue chasing their original target when no longer trapped.
mob_unlock_time: 2000
