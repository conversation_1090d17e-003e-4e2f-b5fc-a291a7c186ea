//--------------------------------------------------------------
// rAthena Battle Configuration File
// Originally Translated by <PERSON> <<EMAIL>>
// Made in to plainer English by <PERSON><PERSON>ker
//--------------------------------------------------------------
// Note 1: Value is a config switch (on/off, yes/no or 1/0)
// Note 2: Value is in percents (100 means 100%)
//--------------------------------------------------------------

// The symbol that will be used to recognize commands.
// You can set any one character except:
// - control-characters (0x00-0x1f),
// - '%' (party chat symbol)
// - '$' (guild chat symbol)
// - '/' (client commands symbol)
// atcommand_symbol represents @commands used locally.
// charcommand_symbol represents #commands used on other players.
atcommand_symbol: @
charcommand_symbol: #

// The maximum quantity of monsters that can be summoned per GM command (0 denotes an unlimited quantity)
atcommand_spawn_quantity_limit: 100

// Maximum number of slave-clones that can be have by using the @slaveclone at command. (0 denotes unlimited quantity)
atcommand_slave_clone_limit: 25

// If 'no', commands require exact player name. If 'yes', entering a partial 
// name will work, as long as there's only one match from all players in the 
// current map server.
// Some critical atcommands like jail, ban and a few others will still require you to enter the full name.
// It will always work for charcommands when the setting is enabled.
partial_name_scan: yes

// Ban people that try trade dupe.
// Duration of the ban, in minutes (default: 5). To disable the ban, set 0.
ban_hack_trade: 5

// requires RENEWAL_EXP or RENEWAL_DROP to be enabled (src/config/renewal.hpp)
// modifies @mobinfo to display the users' real drop rate as per renewal_drop formula
// modifies @iteminfo to not display the minimum item drop rate (since it can't tell the mob level)
// modifies @whodrops to display the users' real drop rate as per renewal_drop formula
atcommand_mobinfo_type: 1

// Should atcommands trigger level up events for NPCs? (Note 1)
// This option is for @baselevelup and @joblevelup
// Default: no
atcommand_levelup_events: no

// Disable atcommands while a player is attached to a npc? (Note 1)
// This can be changed by script commands 'enable_command' and 'disable_command'.
// Anyone with the 'command_enable' permission in the 'conf/group.conf' can bypass this.
// Default: yes
atcommand_disable_npc: yes
