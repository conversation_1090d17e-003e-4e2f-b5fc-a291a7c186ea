//--------------------------------------------------------------
// rAthena Battle Configuration File
// Originally Translated by <PERSON> <<EMAIL>>
// Made in to plainer English by <PERSON><PERSON><PERSON>
//--------------------------------------------------------------
// Note 1: Value is a config switch (on/off, yes/no or 1/0)
// Note 2: Value is in percents (100 means 100%)
// Note 3: Value is a bit field. If no description is given,
//         assume unit types (1: Pc, 2: Mob, 4: Pet, 8: Homun, 16: Mercenary, 128: NPC, 512: Elemental)
//--------------------------------------------------------------

// Should skill casting be cancelled when inflicted by curse/stun/sleep/etc (includes silence) (Note 3)?
status_cast_cancel: 0

// Will certain skill status-changes be removed on logout?
// 0 = (Default) Only remove status that has the SCF_NO_SAVE flag.
// 1 = Remove negative buffs (status that are flagged as debuff)
// 2 = Remove positive buffs.
// 3 = Remove all.
debuff_on_logout: 0

// Adjustment for the natural rate of resistance from status changes.
// If 50, status defense is halved, and you need twice as much stats to block
// them (eg: 200 vit to completely block stun)
pc_status_def_rate: 100
mob_status_def_rate: 100

// Maximum resistance to status changes. (100 = 100%)
// NOTE: Cards and equipment can go over this limit, so it only applies to natural resist.
pc_max_status_def: 100
mob_max_status_def: 100
