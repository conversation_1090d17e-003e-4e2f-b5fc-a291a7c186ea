//--------------------------------------------------------------
// rAthena Battle Configuration File
// Originally Translated by <PERSON> <<EMAIL>>
// Made in to plainer English by <PERSON><PERSON><PERSON>
//--------------------------------------------------------------
// Note 1: Value is a config switch (on/off, yes/no or 1/0)
// Note 2: Value is in percents (100 means 100%)
// Note 3: Value is a bit field. If no description is given,
//         assume unit types (1: Pc, 2: Mob, 4: Pet, 8: Homun, 16: Mercenary, 128: NPC, 512: Elemental)
//--------------------------------------------------------------

// PK Server Mode.  Turns entire server pvp(excluding towns). Experience loss is doubled if killed by another player.
// When players hunt monsters over 20 levels higher, they will receive 15% additional exp., and 25% chance of receiving more items. 
// There is a nopvp.txt for setting up maps not to have pk on in this mode.  Novices cannot be attacked and cannot attack.
// Normal pvp counter and rank display are disabled as well.
// Note: If pk_mode is set to 2 instead of 1 (yes), players will receive a 
//   manner penalty of 5 each time they kill another player (see manner_system 
//   config to adjust how this will affect players)
pk_mode: 0

// Displays a message when the player enters a pk zone.
// Only during pk_mode (Note 1)
pk_mode_mes: yes

// Manner/karma system configuration. Specifies how does negative manner
// (red no chat bubble) affects players (add as needed):
//  0: No penalties.
//  1: Disables chatting (includes whispers, party/guild msgs, etc)
//  2: Disables skill usage
//  4: Disables commands usage
//  8: Disables item usage/picking/dropping
// 16: Disables room creation (chatrooms and vending shops)
manner_system: 15

// For PK Server Mode. Change this to define the minimum level players can start PK-ing
pk_min_level: 55

// For PK Server Mode. It specifies the maximum level difference between
// players to let them attack each other. 0 disables said limit.
pk_level_range: 0

// For PK servers. Damage adjustment settings, these follow the same logic
// as their WoE counterparts (see guild.conf)  
pk_short_attack_damage_rate: 80
pk_long_attack_damage_rate: 70
pk_weapon_attack_damage_rate: 60
pk_magic_attack_damage_rate: 60
pk_misc_attack_damage_rate: 60

// Display skill usage in console? (for debug only) (default: off) (Note 3)
skill_log: off

// Display battle log? (for debug only) (default: off) (Note 1)
battle_log: off

// Display other stuff? (for debug only) (default: off) (Note 1)
etc_log: off

// Do you want to debug warp points? If set to yes, warp points will appear as flags.(Note 1)
// It will also run on start-up a warp-check to print out which warp points lead directly on 
// top of on-touch npcs (which can lead to infinite loopback warping situations)
warp_point_debug: no

// Choose if server begin with night (yes) or day (no)
night_at_start: no

// Define duration in msec of the day (default: 7200000 = 2 hours)
// Set to 0 to disable day cycle (but not @day GM command).
// Except 0, minimum is 60000 (1 minute)
day_duration: 0

// Define duration in msec of the night (default: 1800000 = 30 min)
// Set to 0 to disable night cycle (but not @night GM command).
// Except 0, minimum is 60000 (1 minute)
night_duration: 0

// Using duel on pvp-maps
duel_allow_pvp: no

// Using duel on gvg-maps
duel_allow_gvg: no

// Allow using teleport/warp when dueling
duel_allow_teleport: no

// Autoleave duel when die
duel_autoleave_when_die: yes

// Delay between using @duel in minutes
duel_time_interval: 60

// Restrict duel usage to same map
duel_only_on_same_map: no

// Determines max number of characters that can stack within a single cell. 
// Official - Only affects the walking routines of characters, including monsters.
// If a unit stops walking and is on a cell with more than stack limit
// characters on it, it will walk to the closest free cell.
// Set to 0 for no cell stacking checks and free movement.
// Custom - This variation will make every full cell to be considered a wall.
// NOTE: For the custom setting to take effect you have to use a server compiled
// with Cell Stack Limit support (see src/map/map.hpp)
official_cell_stack_limit: 1
custom_cell_stack_limit: 1

// Allow autotrade only in maps with autotrade flag?
// Set this to "no" to allow autotrade where no "autotrade" mapflag is set.
// Set this to "yes" to only allow autotrade on maps with "autotrade" mapflag.
at_mapflag: no

// Set this to the amount of minutes autotrade chars will be kicked from the server.
at_timeout: 0

// Makes player cannot be attacked when autotrade? (turns player's state.monster_ignore) (Note 1)
at_monsterignore: no

// Should autotrade trigger OnPCLogout script events? (Note 1)
at_logout_event: yes

// Auction system, fee per hour. Default is 12000
auction_feeperhour: 12000

// Auction maximum sell price
auction_maximumprice: 500000000

// Minimum delay between each store search query in seconds.
searchstore_querydelay: 10

// Maximum amount of results a store search query may yield, before
// it is canceled.
searchstore_maxresults: 30

// Whether or not gaining and loosing of cash points is displayed (Note 1).
// Default: no
cashshop_show_points: no

// Whether or not mail box status is displayed upon login.
// Default: 0
// 0 = No
// 1 = Yes
// 2 = Yes, when there are unread mails
mail_show_status: 0

// Amount of mails a user can send a day.
// Default: 100
// 0 = Unlimited
mail_daily_count: 100

// Fee for transferring zeny via mail (Note 2)
// NOTE: this rate is hardcoded in the client, you need to diff your client accordingly if you change this value.
// Default: 2(2%)
// 0 = No fee
mail_zeny_fee: 2

// Amount of zeny to pay for each attached item
// NOTE: this fee is hardcoded in the client, you need to diff your client accordingly if you change this value.
// Default: 2500
// 0 = No fee
mail_attachment_price: 2500

// Maximum weight in total that can be attached to a mail
// NOTE: this limit is hardcoded in the client, you need to diff your client accordingly if you change this value.
// Default: 2000
// 0 = No weight limit
mail_attachment_weight: 2000

// Is monster transformation disabled during Guild Wars?
// If set to yes, monster transforming is automatically removed/disabled when enterting castles during WoE times
mon_trans_disable_in_gvg: no

// Is item's price at item/point shop discountable?
// 0 = No (Default)
// 1 = Item shops
// 2 = Point shops
// 3 = Item & point shops
discount_item_point_shop: 0

// Don't display message "login-serv has been asked to %s the player '%.*s'." (Note 1)
disp_servervip_msg: no

// Delay to allow user resend new mail (default & minimum is 1000)
mail_delay: 1000

// Hides items from the player's favorite tab from being sold to a NPC. (Note 1)
hide_fav_sell: no

// When searching for a random position on the map, how much of the border of the map shall not be considered?
// Officially the 15 tiles from the edge of the map on each side are not considered as target cells.
// On some maps like in Pyramids this causes there to be very few monsters in the outer areas. This also
// affects teleportation. Set this to 1 if you want it to be closer to the old emulator behavior.
// Valid values: 1-40
map_edge_size: 15

// When a player drops items, can they stack on the same cell? (Note 1)
// Officially there's no limit on how many items you can drop on the same cell.
// If you set this to "no", when you drop an item, it will only drop on a cell that has no item on it yet.
// A free cell will be searched for in eight directions. If no free cell could be found in those eight tries,
// then dropping the item will fail (the item stays in the player's inventory).
item_stacking: yes
