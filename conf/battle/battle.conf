﻿//--------------------------------------------------------------
// rAthena Battle Configuration File
// Originally Translated by <PERSON> <<EMAIL>>
// Made in to plainer English by <PERSON><PERSON><PERSON>
//--------------------------------------------------------------
// Note 1: Value is a config switch (on/off, yes/no or 1/0)
// Note 2: Value is in percents (100 means 100%)
// Note 3: Value is a bit field. If no description is given,
//         assume unit types (1: Pc, 2: Mob, 4: Pet, 8: Homun, 16: Mercenary, 128: NPC, 512: Elemental)
//--------------------------------------------------------------

// Who should have a baseatk value (makes str affect damage)? (Note 3)
enable_baseatk: 0x9
enable_baseatk_renewal: 0x29F

// Who can have perfect flee? (Note 3)
enable_perfect_flee: 1

// Who can have critical attacks? (Note 3)
// (Note that there are some skills that always do critical hit regardless of this)
enable_critical: 17

// Critical adjustment rate for non-players (Note 2)
mob_critical_rate: 100
critical_rate: 100

// Which unit types should get a walk delay from normal attacks? (Note 3)
// The delay is equal to the 'attack animation' (amotion). The client already blocks
// sending movement requests during this time, so for client-controlled characters 
// this setting is mainly a safety mechanism against client edits.
// Monsters are usually unaffected by this as their AI is inactive during their attack
// animation unless you customize their AI (see monster_ai 0x2000 in monster.conf)
attack_walk_delay: 0

// Use legacy walk delay when damaged system? (Note 3)
// Officially, when getting hit, you just get stopped on the nearest cell center.
// The walk delay only affects the time a monster reacts to the damage in this case.
// The legacy system prevents move commands for the walk delay duration completely, but it
// only applies if the target was previously able to move for at least the delay duration.
damage_walk_delay: 0

// Move-delay adjustment after being hit. (Note 2)
// The 'can't walk' delay after being hit is calculated as a percentage of the damage animation duration.
// For monsters, this delay also determines how long it takes until they react to an attacker.
pc_damage_walk_delay_rate: 20
damage_walk_delay_rate: 100

// Move-delay adjustment for multi-hitting attacks.
// When hit by a multi-hitting skill like Lord of Vermillion or Jupitel Thunder, characters will be 
// unable to move for an additional "(number of hits -1) * multihit_delay" milliseconds.
// Please note that skills that deal more than 10 hits are only considered as 2-hit-skills.
// This delay is also added to the time a monster waits until it reacts to an attacker.
// Official: 200
// Legacy Athena: 80
multihit_delay: 200

// Damaged delay rate for players (Note 2)
// This affects the damage delay that is sent to the client and is the base value for the walk delay.
player_damage_delay_rate: 100

// Always have endure? (Note 3)
// The unit types defined here cannot be stopped by damage.
// Please note that criticals can't be displayed on targets with endure due to a client limitation.
// That means this setting makes it impossible to display critical hits on these unit types.
infinite_endure: 0

// Should race or element be used to consider someone undead?
// 0 = element undead
// 1 = race undead
// 2 = both (either one works)
undead_detect_type: 0

// Does HP recover if hit by an attribute that's same as your own? (Note 1)
attribute_recover: no

// What is the minimum and maximum hitrate of normal attacks? 
min_hitrate: 5
max_hitrate: 100

// Type of penalty that is applied to FLEE when more than agi_penalty_count monsters are targetting player
// 0 = no penalty is applied
// 1 = agi_penalty_num is reduced from FLEE as a %
// 2 = agi_penalty_num is reduced from FLEE as an exact amount
agi_penalty_type: 1

// When agi penalty is enabled, to whom it should apply to? (Note 3)
// By default, only players get the penalty.
agi_penalty_target: 1

// Amount of enemies required to be targetting player before FLEE begins to be penalized
agi_penalty_count: 3

// Amount of FLEE penalized per each attacking monster more than agi_penalty_count
agi_penalty_num: 10

// Type of penalty that is applied to both equipment and vit DEF when more than vit_penalty_count monsters are targetting player
// 0 = no penalty is applied
// 1 = vit_penalty_num is reduced from DEF as a %
// 2 = vit_penalty_num is reduced from DEF as an exact amount
vit_penalty_type: 1

// When vit penalty is enabled, to whom it should apply to? (Note 3)
// By default, only players get the penalty.
vit_penalty_target: 1

// Amount of enemies required to be targetting player before defense begins to be penalized
vit_penalty_count: 3

// Amount of VIT defense penalized per each attacking monster more than vit_penalty_count
vit_penalty_num: 5

// Use alternate method of DEF calculation for physical attacks.
// With 0, disabled (use normal def% reduction with further def2 reduction)
// At 1 or more defense is subtraction of (DEF * value).
// eg: 10 + 50 def becomes 0 + (10*type + 50)
weapon_defense_type: 0

// MDEF‚ same as above. (MDEF * value)
magic_defense_type: 0

// Change attacker's direction to face opponent on every attack? (Note 3)
// NOTE: On official servers knockback of some skills like Firewall is always based on the
// last direction walked. Even when attacking in a completely different direction, the
// knockback direction won't change, so e.g. if you walk north and then attack an enemy to
// the south you will still be knocked back to the south by Firewall. Immobile monsters
// will always be knocked back to the south as their default direction is north.
attack_direction_change: 0

// For those who is set, their innate attack element is "not elemental"
// (100% versus on all defense-elements) (Note 3)
// NOTE: This is the setting that makes it so non-players can hit for full
// damage against Ghost-type targets with normal attacks (eg: vs. Ghostring).
attack_attr_none: 14

// Rate at which equipment can break (base rate before it's modified by any skills)
// 1 = 0.01% chance. Default for official servers: 0
equip_natural_break_rate: 0

// Overall rate of which your own equipment can break. (Note 2)
// This rate affects penalty breaking rate of skills such as power-thrust and your natural breaking rate 
// (from equip_natural_break_rate). If a Sage's endow skill fails and this is above 0, the selected char's
// weapon will be broken.
equip_self_break_rate: 100

// Overall rate at which you can break target's equipment. (Note 2)
// This affects the behaviour of skills like acid terror and meltdown
equip_skill_break_rate: 100

// Should damage have a delay before it is applied? (Note 1)
// Some skills might not have a delay by default regardless of this setting.
// The official setting is yes, even thought it degrades performance a bit.
delay_battle_damage: yes

// Should the damage timing be synchronized between the client and server? (Note 1)
// This is not official behavior, but it should remove the position lag after being hit by a monster.
// This setting only affects normal monster attacks and takes priority over "delay_battle_damage".
// Many skills show their damage immediately, so setting "delay_battle_damage" to "no" at the same
// time might improve the experience further, but will not work for all skills.
// Tired of Dark Illusion hitting you 5 seconds too late? Then turn this on.
synchronize_damage: no

// Are arrows/ammo consumed when used on a bow/gun?
// 0 = No
// 1 = Yes
// 2 = Yes even for skills that do not specify arrow consumption when said 
//     skill is weapon-based and used with ranged weapons (auto-guesses which 
//     skills should consume ammo when it's acquired via a card or plagiarize)
arrow_decrement: 1

// Should ammo be unequipped when unequipping a weapon?
// Official behavior is "yes".
ammo_unequip: yes

// Should a suitable weapon be equipped when equipping ammo?
// Official behavior is "yes".
ammo_check_weapon: yes

// Should the item script bonus 'Autospell' check for range/obstacles before casting?
// Official behavior is "no", setting this to "yes" will make skills use their defined
// range. For example, Sonic Blow requires a 2 cell distance before autocasting is allowed.
// This setting also affects autospellwhenhit.
autospell_check_range: no

// If both the attacker and the target are on the same tile, should the target be knocked back to the left?
// Official behavior is "yes", setting this to "no" will knock the target back behind the attacker.
knockback_left: yes

// Can players use Falcons and Wargs at the same time? (Note 1)
// This is not allowed on official servers.
warg_can_falcon: no

// Should the target be able of dodging damage by snapping away to the edge of the screen?
// Official behavior is "no"
snap_dodge: no

// Grant player skills/items the ability to "break" non-player equipment. (Note 1)
// This will effectively apply the strip equip effect to the non-player target.
// NOTE: WS_MELTDOWN is exempt from this check when disabled.
// Official: no
break_mob_equip: no
