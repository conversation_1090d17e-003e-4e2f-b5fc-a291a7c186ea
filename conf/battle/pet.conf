//--------------------------------------------------------------
// rAthena Battle Configuration File
// Originally Translated by <PERSON> <<EMAIL>>
// Made in to plainer English by <PERSON><PERSON><PERSON>
//--------------------------------------------------------------
// Note 1: Value is a config switch (on/off, yes/no or 1/0)
// Note 2: Value is in percents (100 means 100%)
// Note 3: Value is a bit field. If no description is given,
//         assume unit types (1: Pc, 2: Mob, 4: Pet, 8: Homun, 16: Mercenary, 128: NPC, 512: Elemental)
//--------------------------------------------------------------

// Should the legacy formula for pet catch rate calculation be used? (Note 1) 
// Legacy Athena formula:
//		( Base rate + ( player level - monster level ) * 30 + player luk * 20 ) * ( 200 - current monster hp percentage ) / 100
// Official formula: (Default)
//		Base rate + ( 100 - current monster hp percentage ) * base rate / 100
pet_legacy_formula: no

// Rate for catching pets (Note 2)
pet_catch_rate: 100

// How many cells away can the player be from the monster they are trying to catch?
// The client automatically walks the player into range when trying to catch a monster.
// Default: 5
// Set to 0 to disable the check
pet_distance_check: 5

// On official servers players are unable to catch monsters if they are hiding. (Note 1)
pet_hide_check: yes

// Can you name a pet more then once? (Note 1)
pet_rename: no

// The rate a pet will get friendly by feeding it. (Note 2)
pet_friendly_rate: 100

// The rate at which a pet will become hungry. (Note 2)
pet_hungry_delay_rate: 100

// Does the pet need its equipment before it does its skill? (Note 1)
// These bonuses are unofficial and found in the import/pet_db.yml
pet_equip_required: yes

// Should the pet equipment be destroyed if the owner doesn't have enough space in their inventory? (Note 1)
// Official behavior is "yes", setting this to "no" will leave the item equipped. 
pet_unequip_destroy: yes

// When the master attacks a monster, whether or not the pet will also attack. (Note 1)
pet_attack_support: no

// When the master receives damage from the monster, whether or not the pet attacks back. (Note 1)
pet_damage_support: no

// Minimum intimacy necessary for a pet to support their master. Default is 900 
// (intimacy goes from 0 to 1000). At this minimum, support rate is 50% of pet's normal value.
// At max (1000) support rate is 150%.
pet_support_min_friendly: 900

// Whether or not the pet's will use skills. (Note 1)
// Note: Offensive pet skills need at least pet_attack_support or 
// pet_damage_support to work (they trigger while the pet is attacking).
pet_status_support: no

// Rate at which a pet will support it's owner in battle. (Note 2)
// Affects pet_attack_support & pet_damage_support.
pet_support_rate: 100

// Does the pets owner receive exp from the pets damage?
pet_attack_exp_to_master: no

// The rate exp. is gained from the pet attacking monsters
pet_attack_exp_rate: 100

// Pet leveling system. Use 0 to disable (default).
// When enabled, a pet's level is a fixed % of the master's. (Note 2)
// If 200%, pet has double level, if 50% pet has half your level, etc.
pet_lv_rate: 0

// When pet leveling is enabled, what is the max stats for pets?
pet_max_stats: 99

// When pet leveling is enabled, these are the imposed caps on
// min/max damage. Note that these only cap atk1 and atk2, if you 
// enable pet_str, their max damage is then their base_atk + pet_max_atk2
pet_max_atk1: 500
pet_max_atk2: 1000

// Are pets disabled during Guild Wars?
// If set to yes, pets are automatically returned to egg when entering castles during WoE times
// and hatching is forbidden within as well.
pet_disable_in_gvg: no

// Will does petskillattack2 fixed damage ignore plant infnite defense? (Note 1)
pet_ignore_infinite_def: yes

// Whether or not the pet will continue to attack when the master is dead. (Note 1)
pet_master_dead: no

// Send auto-feed notice even if the client setting is OFF (Note 1) 
// Official: yes
pet_autofeed_always: yes

// Pet walk speed.
// 1: Master's walk speed (official)
// 2: DEFAULT_WALK_SPEED value
// 3: Mob database walk speed
pet_walk_speed: 1
