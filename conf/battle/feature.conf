//--------------------------------------------------------------
// rAthena Feature Configuration File
//--------------------------------------------------------------
// Note 1: Value is a config switch (on/off, yes/no or 1/0)
// Note 2: Value is in percents (100 means 100%)
// Note 3: Value is a bit field. If no description is given,
//         assume unit types (1: Pc, 2: Mob, 4: Pet, 8: Ho<PERSON>n, 16: Mercenary, 128: NPC, 512: Elemental)
//--------------------------------------------------------------

// Buying store (Note 1)
// Requires: 2010-04-27aR<PERSON>xeRE or later
feature.buying_store: on

// Search stores (Note 1)
// Requires: 2010-08-03aRagexeRE or later
feature.search_stores: on

// Atcommand suggestions (Note 1)
// If one type incomplete atcommand, it will suggest the complete ones.
feature.atcommand_suggestions: off

// Auction (Note 1)
// Feature became unstable on clients 2012 onwards (exact date not known),
// it has been fixed on clients 2013-05-15 onwards however.
// Feature was removed again on clients 2014-11-12 and later.
feature.auction: off

// Warp suggestions (Note 1)
// Show suggestions when attempting to @warp to a non-existent map?
feature.warp_suggestions: off

// Banking (Note 1)
// Requires: 2013-07-24aRagexe or later
feature.banking: on

// Should Banking strictly checks the UI state on zeny deposit and withdrawal? (Note 1)
// Note: Disabled by default because existing official clients do not report the banking UI state to the server.
// But this config could be useful in case of clients that actually report the UI state or custom clients.
feature.banking_state_enforce: no

// Autotrade persistency (Note 1)
// Should vendors that used @autotrade be restored after a restart?
feature.autotrade: on

// In which direction should respawned autotraders look?
// Possible values are from 0-7
// Default: 4(South)
// -1 = Last player's direction
feature.autotrade_direction: 4

// Change player's head direction?
// -1 = Last condition
//  0 = Forward
//  1 = Right
//  2 = Left
feature.autotrade_head_direction: 0

// Do you want your autotraders to sit?
// -1 = Last player's condition, sitting or standing
//  0 = Standing
//  1 = Sitting
feature.autotrade_sit: 1

// Delay in milliseconds to open vending/buyingsotre after player logged in.
feature.autotrade_open_delay: 5000

// Battlegrounds queue interface. Makes it possible to queue for a battleground anywhere using the battle menu.
// Requires: 2012-04-10aRagexe or later
feature.bgqueue: on

// Roulette (Note 1)
// Requires: 2014-10-22bRagexe or later
feature.roulette: on

// Roulette bonus reward
// Multiply amount by 2 if the reward item ID is the same as bonus item ID
feature.roulette_bonus_reward: on

// Achievement (Note 1)
// Requires: 2015-05-13aRagexe or later
feature.achievement: on

// Refine UI (Note 1)
// Requires: 2016-10-12aRagexeRE or later
feature.refineui: on

// Equipment Switch (Note 1)
// Requires: 2017-02-08bRagexeRE or later
feature.equipswitch: on

// Pet evolution (Note 1)
// Requires: 2014-10-08aRagexe or later
feature.petevolution: on

// Automatic Pet Feeding (Note 1)
// Requires: 2014-10-08aRagexe or later
feature.petautofeed: on

// At which hunger rate should pet autofeeding trigger? (Note 2)
// Default: 89
feature.pet_autofeed_rate: 89

// Homunculues Autofeeding (Note 1)
// Requires: 2017-09-20bRagexeRE or later
feature.homunculus_autofeed: on

// At which rate should homunculus autofeeding trigger? (Note 2)
// Default: 30
//
// NOTE:
// This setting only applies, if your client side LUAs are bugged.
// By default the client triggers the feeding packet itself once
// it reaches the limit that is hardcoded in the client.
feature.homunculus_autofeed_rate: 30

// Attendance System (Note 1)
// Requires: 2018-03-07bRagexeRE or later
feature.attendance: on

// Private Airship System (Note 1)
// Requires: 2018-03-21aRagexeRE or later
feature.privateairship: on

// Barter Shop System (Note 1)
// Requires: 2019-01-16RagexeRE or later
feature.barter: on

// Extended Barter Shop System (Note 1)
// Requires: 2019-11-06RagexeRE or later
feature.barter_extended: on

// The timeout in milliseconds when a dynamic NPC will be despawned if not used.
// Default: 60000 (60s)
feature.dynamicnpc_timeout: 60000

// The x range in which the dynamic NPC will be spawned relative to the player.
// Default: 2
feature.dynamicnpc_rangex: 2

// The y range in which the dynamic NPC will be spawned relative to the player.
// Default: 2
feature.dynamicnpc_rangey: 2

// Should the dynamic NPCs look into the direction of the player? (Note 1)
// Default: no
feature.dynamicnpc_direction: no

// Itemlink System on informational related commands (Note 1)
// Generates an itemlink string for an item and can be used for npctalk, message,
// dispbottom, and broadcast commands. The result is clickable-item name just
// like from SHIFT+Click from player's inventory/cart/equipment window.
// Requires: 2015-11-04Ragexe or later
feature.itemlink: on

// Itemlink System on NPC messages (Note 1)
// Generates an itemlink string for an item and can be used for NPC's mes command.
// Requires: 2010-01-01 or later
feature.mesitemlink: on

// Force all mesitemlinks to be wrapped in brackets (Note 1)
// Default: no
feature.mesitemlink_brackets: no

// Force all mesitemlinks to use the database name (Note 1)
// Default: no
feature.mesitemlink_dbname: no

// Itemlink Icons on NPC messages (Note 1)
// Generates an itemicon string for an item and can be used for NPC's mes command.
// Requires: 2023-03-02 or later
feature.mesitemicon: on

// Force all mesitemicons to use the database name, if mesitemicon feature is disabled (Note 1)
// Default: no
feature.mesitemicon_dbname: no

// Stylist UI (Note 1)
// Requires: 2015-11-04 or later
feature.stylist: on

// Are players allowed to reconnect into instances? (Note 1)
// This is enabled by default in Renewal mode and
// disabled by default in Pre-Renewal mode.
// If this is allowed the "nosave" mapflag is still being respected
// and may prevent players from warping back into the instance.
//feature.instance_allow_reconnect: yes
