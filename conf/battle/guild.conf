//--------------------------------------------------------------
// rAthena Battle Configuration File
// Originally Translated by <PERSON> <<EMAIL>>
// Made in to plainer English by <PERSON><PERSON>ker
//--------------------------------------------------------------
// Note 1: Value is a config switch (on/off, yes/no or 1/0)
// Note 2: Value is in percents (100 means 100%)
//--------------------------------------------------------------

// When making a guild, an Emperium is consumed? (Note 1)
guild_emperium_check: yes

// Maximum tax limit on a guild member.
guild_exp_limit: 50

// Maximum castles one guild can own (0 = unlimited)
guild_max_castles: 0

// Activate guild skills delay by relog?
// 0 - Save cooldown and resume on relog.
// 1 - Don't save cooldown and restart the timer on relog.
// Default on official servers: 1 for Pre-renewal, 0 for Renewal
//guild_skill_relog_type: 0

// Delay in milliseconds that are applied to guild skills.
// Official setting is 5 minutes (300000 ms).
// Note: This was changed in renewal in favor of individual skill cooldown.
guild_skill_relog_delay: 300000

// Melee damage adjustments (non skills) for WoE battles (Guild Vs Guild) (Note 2)
gvg_short_attack_damage_rate: 80

// Ranged damage adjustments (non skills) for WoE battles (Guild Vs Guild) (Note 2)
gvg_long_attack_damage_rate: 80

// Weapon skills damage adjustments for WoE battles (Guild Vs Guild) (Note 2)
gvg_weapon_attack_damage_rate: 60

// Magic skills damage adjustments for WoE battles (Guild Vs Guild) (Note 2)
gvg_magic_attack_damage_rate: 60

// Misc skills damage adjustments for WoE battles (Guild Vs Guild) (Note 2)
gvg_misc_attack_damage_rate: 60

// Flee penalty on gvg grounds. Official value is 20 (Note 2)
// NOTE: It's %, not absolute, so 20 is -20% of your total flee
gvg_flee_penalty: 20

// Can the 'Glory of Guild' skill be learnt in the Guild window,
// and does changing emblems require it? (Note 1)
// P.S: This skill is not implemented on official servers
require_glory_guild: no

// Limit Guild alliances. Value is 0 to 3.
// If you want to change this value, clear the guild alliance table.
// Default is 3
max_guild_alliance: 3

// When to re-display the guild notice
// Upon teleporting (regardless of changing maps): 2 (official)
// Upon changing maps: 1
// Do not re-display: 0 (disabled)
guild_notice_changemap: 2

// Should maprespawnguildid kill clones too?
// Default: no
guild_maprespawn_clones: no

// How long (in minutes) should a guild have to wait between guild master changes?
// Default: 1440 (1 day)
// Use 0 minutes to disable the delay.
guild_leaderchange_delay: 1440

// Is changing the guild leader allowed during WoE?
// Default: no
guild_leaderchange_woe: no

// Only guild master can accept alliance?
// Default: no
guild_alliance_onlygm: no
