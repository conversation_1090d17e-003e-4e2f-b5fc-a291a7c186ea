//--------------------------------------------------------------
// rAthena Battle Configuration File
// Originally Translated by <PERSON> <<EMAIL>>
// Made in to plainer English by <PERSON><PERSON><PERSON>
//--------------------------------------------------------------
// Note 1: Value is a config switch (on/off, yes/no or 1/0)
// Note 2: Value is in percents (100 means 100%)
// Note 3: Value is a bit field. If no description is given,
//         assume unit types (1: Pc, 2: Mob, 4: Pet, 8: Homun, 16: Mercenary, 128: NPC, 512: Elemental)
//--------------------------------------------------------------

// Players' maximum HP rate? (Default is 100)
hp_rate: 100

// Players' maximum SP rate? (Default is 100)
sp_rate: 100

// Whether or not cards and attributes of the left hand are applied to the right hand attack (Note 1)
// (It is 'yes' on official servers)
left_cardfix_to_right: yes

// The amount of HP a player will respawn with, 0 is default.
// (Unit is in percentage of total HP, 100 is full heal of HP, 0 is respawn with 1HP total.)
restart_hp_rate: 0

// The amount of SP a player will respawn with, 0 is default.
// (Unit is in percentage of total SP, 100 is full heal of SP, 0 is respawn with 1SP total.)
restart_sp_rate: 0

// Can a normal player by-pass the skill tree? (Note 1)
player_skillfree: no

// When set to yes, forces skill points gained from 1st class to be put into 1st class
// skills, and forces novice skill points to be put into the basic skill. (Note 1)
player_skillup_limit: yes

// Quest skills can be learned? (Note 1)
// Setting this to yes can open an exploit on your server!
quest_skill_learn: no

// When skills are reset, quest skills are reset as well? (Note 1)
// Setting this to yes can open an exploit on your server!
// NOTE: If you have quest_skill_learn set to yes, quest skills are always reset.
quest_skill_reset: no

// You must have basic skills to be able to sit, trade, form a party or create a chatroom? (Note 1)
basic_skill_check: yes

// When teleporting, or spawning to a map, how long before a monster sees you if you don't move? (time is in milliseconds)
// That is, when you go to a map and don't move, how long before the monsters will notice you.
// If you attack a monster, it will attack you back regardless of this setting.
player_invincible_time: 5000

// The time interval for HP to restore naturally. (in milliseconds)
natural_healhp_interval: 6000

// The time interval for SP to restore naturally. (in milliseconds)
natural_healsp_interval: 8000

// Automatic healing skill's time interval. (in milliseconds)
natural_heal_skill_interval: 10000

// The maximum weight for a character to carry before it stops healing naturally. (Note 2)
// Depending on this configuration you may need to change the corresponding TGA image file
// in your data in order to show the proper percentage in the status icon.
// Default on official servers: 50 for Pre-renewal, 70 for Renewal
//natural_heal_weight_rate: 70

// The maximum weight for a character to get an item from item boxes. (Note 2)
// Default on official servers: 70 for Pre-renewal, 90 for Renewal
//open_box_weight_rate: 90

// The maximum weight for a character to carry before entering the major overweight state. (Note 2)
// In this state the character cannot heal naturally, attack or use skills.
major_overweight_rate: 90

// Maximum atk speed. (Default 190, Highest allowed 199)
max_aspd: 190

// Same as max_aspd, but for 3rd classes. (Default 193, Highest allowed 199)
max_third_aspd: 193

// Max ASPD for extended class (Kagerou/Oboro and Rebellion). (Default 193, Highest allowed 199)
max_extended_aspd: 193

// Max ASPD for Summoner Class (Doram). (Default 193, Highest allowed 199)
max_summoner_aspd: 193

// Maximum walk speed rate (200 would be capped to twice the normal speed)
max_walk_speed: 300

// Maximum HPs depending on base level. Default values are:
// Lv 99:  330000
// Lv150:  660000
// Lv175: 1100000
max_hp_lv99: 330000
max_hp_lv150: 660000
max_hp: 1100000

// Maximum SP. (Default is 1000000)
max_sp: 1000000

// Maximum params/stats for each class. (str, agi, vit, int, dex, and luk)
// 'max_parameter' for novice, non-trans, non-baby, and non-3rd classes
// 'max_trans_parameter' for trans classes (non-3rd trans classes)
// 'max_third_parameter' for 3rd classes (regular/non-trans) except baby 3rd clasess
// 'max_third_trans_parameter' for 3rd trans classes
// 'max_baby_parameter' for baby classes except baby 3rd classes
// 'max_baby_third_parameter' for baby 3rd classes only
// 'max_extended_parameter' for extended 2nd class (Kagerou/Oboro and Rebellion)
// For mor specific/advanced option, see 'db/[pre-]re/job_params_db.txt'
max_parameter: 99
max_trans_parameter: 99
max_third_parameter: 130
max_third_trans_parameter: 130
max_baby_parameter: 80
max_baby_third_parameter: 117
max_extended_parameter: 130
max_summoner_parameter: 130
max_fourth_parameter: 130

// Status points bonus for transcendent class
transcendent_status_points: 52

// Max armor def/mdef
// NOTE: This setting have no effect if server is run on Renewal Mode (RENEWAL) 
// NOTE: does not affects skills and status effects like Mental Strength
// If weapon_defense_type is non-zero, it won't apply to max def.
// If magic_defense_type is non-zero, it won't apply to max mdef.
max_def: 99

// Def to Def2 conversion bonus. If the armor def/mdef exceeds max_def,
// the remaining is converted to vit def/int mdef using this multiplier
// (eg: if set to 10, every armor point above the max becomes 10 vit defense points)
over_def_bonus: 0

// Max weight carts can hold.
max_cart_weight: 8000

// Prevent logout of players after being hit for how long (in ms, 0 disables)?
prevent_logout: 10000

// When should the server prevent a player from logging out? Have no effect if prevent_logout is disabled. (Note 3)
// Official servers prevent players from logging out after attacking, casting skills, and taking damage.
// 0 = Players can always logout
// 1 = Prevent logout on login
// 2 = Prevent logout after attacking
// 4 = Prevent logout after casting skill
// 8 = Prevent logout after being hit
prevent_logout_trigger: 14

// Display the drained hp/sp values from normal attacks? (Ie: Hunter Fly card)
show_hp_sp_drain: no

// Display the gained hp/sp values from killing mobs? (Ie: Sky Deleter Card)
show_hp_sp_gain: yes

// If set, when A accepts B as a friend, B will also be added to A's friend 
// list, otherwise, only A appears in B's friend list.
// NOTE: this setting enables friend auto-adding and auto-deletion.
friend_auto_add: yes

// Are simultaneous trade/party/guild invite requests automatically rejected?
invite_request_check: yes

// Players' will drop a 'Skull' when killed?
// Note: The 'Skull' item (ID 7420) is trade restricted by default. You need
// to remove the "NoTrade" flag on the item for this feature to work.
// 0 = Disabled
// 1 = Dropped only in PvP maps
// 2 = Dropped in all situations
bone_drop: 0

// Do mounted (on Peco) characters increase their size?
// 0 = no
// 1 = only Normal Classes on Peco have Big Size
// 2 = only Baby Classes on Peco have Medium Size
// 3 = both Normal Classes on Peco have Big Size
//	and Baby Classes on Peco have Medium Size
character_size: 0

// Idle characters can receive autoloot?
// Set to the time in seconds where an idle character will stop receiving
// items from Autoloot (0: disabled).
idle_no_autoloot: 0

// Minimum distance a vending/chat room must be from a NPC in order to be placed.
// Default: 3 (0: disabled).
min_npc_vendchat_distance: 3
 
// How much should rental mounts increase a player's movement speed?
// Default is 25. 100 = 100% Increase.
rental_mount_speed_boost: 25

//===================================
// VIP system
//===================================
// Storage slot increase. Setting to 0 will disable.
// Give more storage slots above the MIN_STORAGE limit.
// Note: MIN_STORAGE + vip_storage_increase cannot exceed MAX_STORAGE.
// Default: 300
vip_storage_increase: 300

// Base experience rate increase. Setting to 0 will disable. (Note 2)
// Default: 50
vip_base_exp_increase: 50

// Job experience rate increase. Setting to 0 will disable. (Note 2)
// Default: 50
vip_job_exp_increase: 50

// Experience penalty rate multiplier for VIP accounts.
// Default: 100 (100 = 1% penalty)
vip_exp_penalty_base: 100
vip_exp_penalty_job: 100

// Zeny penalty for VIP accounts.
// Zeny loss only happens if the player dies from another player.
// Default: 0 (100 = 1% penalty)
vip_zeny_penalty: 0

// Battle Manual experience increase. Setting to 0 will disable.
// - Regular/Thick Battle Manual: 50+(50/X) = 75%
// - HE Battle Manual: 100+(100/X) = 150%
// - Battle Manual x3: 200+(200/X) = 300%
// Note: X is the config value.
// Default: 2
vip_bm_increase: 2

// Item drop increase. Setting to 0 will disable.
// Note: 50 = 50% item_drop increase.
// For item_rate = 200: 200 * 50 / 100 = 100 bonus rate added to the 200 base giving total rate of 300.
// Default: 50
vip_drop_increase: 50

// Gemstone requirement.
// Can the VIP Group ignore Gemstone requirement for skills?
// 0 = Disable
// 1 = Behave like Mistress Card
// 2 = Remove all gemstone requirements (default)
vip_gemstone: 2

// Will display rate information (EXP, Drop, and Death penalty message)? (Note 1)
vip_disp_rate: yes

// Revive dead player while warping? (Note 1)
revive_onwarp: yes

// Minimum base level to receives Taekwon Ranker Bonus
// - 3x Maximum HP and SP
// - All Taekwon skills
taekwon_ranker_min_lv: 90

// Fame points gained
// Taekwon Mission completed
fame_taekwon_mission: 1
// Refined own forged weapon to +10
fame_refine_lv1: 1
fame_refine_lv2: 25
fame_refine_lv3: 1000
// Success to forge a lv3 weapon with 3 additional ingredients
fame_forge: 10
// Refine threshold for giving point for refining forged weapon to +10
blacksmith_fame_refine_threshold: 10
// Success to prepare 'n' Condensed Potions in a row
fame_pharmacy_3: 1
fame_pharmacy_5: 3
fame_pharmacy_7: 10
fame_pharmacy_10: 50

// How the server should measure the character's idle time? (Note 3)
// 0x0001 - Walk Request
// 0x0002 - UseSkillToID Request (Targetted skill use attempt)
// 0x0004 - UseSkillToPos Request (AoE skill use attempt)
// 0x0008 - UseItem Request (Including equip/unequip)
// 0x0010 - Attack Request
// 0x0020 - Chat Request (Whisper, Party, Guild, Battlegrounds, etc)
// 0x0040 - Sit/Standup Request
// 0x0080 - Emotion Request
// 0x0100 - DropItem Request
// 0x0200 - @/#Command Request
// 0x0400 - Closing a NPC window
// 0x0800 - Providing input to a NPC
// 0x1000 - Choosing a NPC menu option
// 0x2000 - Clicking the next button of a NPC
// 0x4000 - Finishing for a NPC progress bar
// Please note that at least 1 option has to be enabled.
// Be mindful that the more options used, the easier it becomes to cheat features that rely on idletime (e.g. checkidle()).
// Default: walk (0x1) + useskilltoid (0x2) + useskilltopos (0x4) + useitem (0x8) + attack (0x10) + any npc interaction(0x400,0x800,0x1000,0x2000,0x4000) = 0x7C1F
idletime_option: 0x7C1F

// Adjust the summoner class' special traits.
// - Summoners belong to brute race category. They have their own race RC_PLAYER_DORAM (11) to be differentiated from monster race RC_BRUTE (2).
// - Summoners are small size (0) instead of medium (1)
summoner_race: 11
summoner_size: 0

//================================
// 4th Job Systems
//================================
// How many trait points do players get when changing to a 4th job?
// Default: 7
trait_points_job_change: 7

// Max trait stats cap.
// Trait Stats: POW, STA, WIS, SPL, CON, CRT
// Official is 110.
max_trait_parameter: 110

// Max percent of RES/MRES that can be ignored by item bonus/skill.
// Default: 50
max_res_mres_ignored: 50

// Maximum AP
// Default: 1000
max_ap: 1000

// Players' maximum AP rate? (Default is 100)
ap_rate: 100

// The amount of AP a player will respawn with, 0 is default.
// (Unit is in percentage of total AP, 100 is full heal of AP, 0 is respawn with 0 AP total.)
restart_ap_rate: 0

// Is AP lost when the player dies?
// Default: yes
loose_ap_on_death: yes

// Is AP lost when the player enters a PVP/GVG/WoE/Battleground maps?
// Default: yes
loose_ap_on_map: yes

// Do player's keep their AP when logging out?
// Default: yes
keep_ap_on_logout: yes
