//--------------------------------------------------------------
// rAthena Battle Configuration File
// Originally Translated by <PERSON> <<EMAIL>>
// Made in to plainer English by <PERSON><PERSON><PERSON>
//--------------------------------------------------------------
// Note 1: Value is a config switch (on/off, yes/no or 1/0)
// Note 2: Value is in percents (100 means 100%)
// Note 3: Value is a bit field. If no description is given,
//         assume unit types (1: Pc, 2: Mob, 4: Pet, 8: Homun, 16: Mercenary, 128: NPC, 512: Elemental)
//--------------------------------------------------------------

// The highest value at which an item can be sold via the merchant vend skill. (in zeny)
vending_max_value: *********0

// Whether to allow placing items on a vending store when the player's zeny plus the total price
// of the items exceeds the maximum zeny allowed. (Note 1)
// If set to "yes", the items will be placed in the store but other players will not be able to buy them.
// Official behavior is "yes", but on some official servers the client doesn't allow this.
vending_over_max: yes

// Tax to apply to all vending transactions (eg: 10000 = 100%, 50 = 0.50%)
// When a tax is applied, the item's full price is charged to the buyer, but
// the vender will not get the whole price paid (they get 100% - this tax).
vending_tax: 500

// Minimum total of purchase until taxes are applied.
// Officially there is no tax for anything less than 100 million zeny.
// 0 will apply taxes to all transactions.
vending_tax_min: *********

// Show the buyer's name when successfully vended an item
buyer_name: yes

// Forging success rate. (Note 2)
weapon_produce_rate: 100

// Prepare Potion success rate. (Note 2)
potion_produce_rate: 100

// Do produced items have the maker's name on them? (Note 3)
// 0x01: Produced Weapons
// 0x02: Produced Potions
// 0x04: Produced Arrows
// 0x08: Produced Holy Water/Ancilla
// 0x10: Produced Deadly Potions
// 0x80: Other produced items.
produce_item_name_input: 0x03

// Is a monster summoned via dead branch aggressive? (Note 1)
dead_branch_active: yes

// Should summoned monsters check the player's base level? (dead branches) (Note 1)
// On officials this is no - monsters summoned from dead/bloody branches can be ANY level.
// Change to 'yes' to only summon monsters less than or equal to the player's base level.
random_monster_checklv: no

// On map change it will check for items not tagged as "available" and
// auto-delete them from inventory/cart/storage.
// NOTE: An item is not available if it was not loaded from the item_db.
// 0x1: Inventory
// 0x2: Cart
// 0x4: Storage
item_check: 0x0

// How much time must pass between item uses?
// Only affects the delay between using items, prevents healing item abuse. Recommended ~500 ms
// On officials this is 0, but it's set to 100ms as a measure against bots/macros.
item_use_interval: 100

// How much time must pass between cash food uses? Default: 60000 (1 min)
cashfood_use_interval: 60000

// Required level of bNoMagicDamage before Status Changes are blocked (Golden Thief Bug card).
// For example, if left at 50. An item can give bNoMagicDamage,40;
// which reduces magic damage by 40%, but does not blocks status changes.
gtb_sc_immunity: 50

// Enable autospell card effects to stack?
// NOTE: Different cards that grant the same skill will both 
// always work independently of each other regardless of setting.
autospell_stacking: no

// Allow the consumption of usable items that are disabled by item_noequip.txt? (Note 1)
// no = can't be consumed
// yes = consumed with no effect
allow_consume_restricted_item: no

// Allow equipping items that are disabled by item_noequip.txt? (Note 1)
// no = can't be equipped and will be unequipped when entering the map
// yes = can be equipped but gives no effect
// If the equip is compounded with restricted card(s), it ignores this check but still gives no effect.
allow_equip_restricted_item: yes

// Allow changing of equipment while interacting with NPCs? (Note 1)
// Default on official servers: yes for Pre-renewal, no for Renewal
//item_enabled_npc: yes

// Allow map_addflooritem to check if item is droppable? (Note 1)
// If yes, undroppable items will be destroyed instead of appearing on the map when a player's inventory is full.
// Default: yes
item_flooritem_check: yes

// Set default bound type when item with BindOnEquip flag is equipped (see db/[pre-]re/item_db.yml)
// 0 - None
// 1 - Account
// 2 - Guild
// 3 - Party
// 4 - Character
default_bind_on_equip: 4

// Allow selling of bound/sell restricted items as Itemshop currency? (Note 3)
// 0x0 = Bound/sell restricted items are unable to be sold to Itemshops/Shops
// 0x1 = Bound items are able to be sold to Itemshops
// 0x2 = Sell restricted items are able to be sold to Itemshops
// 0x4 = Bound items are able to be sold to Shops,
//       because most of trade restricted items are still able to be sold to Shops
// 0x8 = Only Guild Leader can sell BOUND_GUILD items to Shops or Itemshops (if 0x1 or 0x4 set)
allow_bound_sell: 0x0

// Hide n last characters of player's name with asterisk (*) when the player
// obtained an item with special broadcast flag or refined an item at a level with broadcast flag.
// Note: Players with short names can be fully converted to asterisks if this
// config value is set high.
broadcast_hide_name: 2

// Enable to sell rental item to NPC shop? (Note 1)
rental_transaction: yes

// Sell rental item for 0 to NPC shop regardless of the item value in item_db? (Note 1)
rental_item_novalue: no

// Minimum purchase price of items at a normal Shop
// Officially items cannot be purchased for less than 1 Zeny
min_shop_buy: 1

// Minimum sell price of items at a normal shop
// Officially items can be sold for 0 Zeny
min_shop_sell: 0

// Should items that reduce damage from element/race count all monster damage as physical? (Note 1)
// Officially "Asprika" (god item) reduces all monsters damage rather than just physical damage
// Shaman Hat on the other hand doesn't reduce monster damage at all (reduces magical damage in PVP)
// This only affects items with bonus3 bSubEle and bonus3 bSubRace.
cardfix_monster_physical: yes

// Determines whether stackable items should be counted separately for inventory space when trading,
// even if the other player already has that item.
// (It is 'yes' on official servers)
trade_count_stackable: yes
