//--------------------------------------------------------------
// rAthena Battle Configuration File
// Originally Translated by <PERSON> <<EMAIL>>
// Made in to plainer English by <PERSON><PERSON>ker
//--------------------------------------------------------------
// Note 1: Value is a config switch (on/off, yes/no or 1/0)
// Note 2: Value is in percents (100 means 100%)
//--------------------------------------------------------------

// If someone steals (gank/steal skills), show name in party? (Note 1)
show_steal_in_same_party: no

// Interval before updating the party-member map mini-dots (milliseconds)
party_update_interval: 1000

// Method used to update party-mate hp-bars:
// 0: Aegis - bar is updated every time HP changes (bandwidth intensive)
// 1: rAthena - bar is updated with the party map dots (up to 1 second delay)
party_hp_mode: 0

// NOTE:
// The level range for sharing within a party is set in conf/inter_athena.conf
// under the value "party_share_level".

// When 'Party Share' item sharing is enabled in a party, 
// announce in the party which party-member received the item and what's he received? (Note 1)
show_party_share_picker: yes

// What types of items are going to be announced when 'show_party_share_picker' is active?
// 1:   IT_HEALING,  2:   IT_UNKNOWN,  4:    IT_USABLE, 8:    IT_ETC,
// 16:  IT_WEAPON,   32:  IT_ARMOR,    64:   IT_CARD,   128:  IT_PETEGG,
// 256: IT_PETARMOR, 512: IT_UNKNOWN2, 1024: IT_AMMO,   2048: IT_DELAYCONSUME
// 262144: IT_CASH
show_picker.item_type: 112

// Method of distribution when item party share is enabled in a party:
// 
// 0: Normal (item goes to a random party member)
// 1: Item Share is disabled for non-mob drops (player/pet drops)
// 2: Round Robin (items are distributed evenly and in order among members)
// 3: 1+2
party_item_share_type: 0

// Is exp/item sharing disabled for idle members in the party?
// Set to no, or the amount of seconds (NOT milliseconds) that need to pass before considering
// a character idle.
// Characters in a chat/vending are always considered idle.
// A character's idle status is reset upon item use/skill use/attack (auto attack counts too)/movement.
idle_no_share: no

// Give additional experience bonus per party-member involved on even-share parties (excluding yourself)?
// ex. If set to 10, an even-share party of 5 people will receive +40% exp (4 members * 10% exp):
//     140% party experience in total, so each member receives 140%/5 = 28% exp (instead of 20%).
party_even_share_bonus: 0

// Display party name regardless if player is in a guild.
// Official servers display party name even if the user is not in a guild. (Note 1)
display_party_name: yes

// Prevent multiple characters of the same account to join the same party. (Note 1)
block_account_in_same_party: yes

// Prevent changing the party leader if the specified player is not on the same map (Note 1)
change_party_leader_samemap: yes
