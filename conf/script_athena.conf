//--------------------------------------------------------
// rA<PERSON><PERSON>t Configuration File
//--------------------------------------------------------


warn_func_mismatch_paramnum: yes

check_cmdcount: 655360

check_gotocount: 2048

// Default value of the 'min' argument of the script command 'input'.
// When the 'min' argument isn't provided, this value is used instead.
// Defaults to 0.
//input_min_value: 0

// Default value of the 'max' argument of the script command 'input'.
// When the 'max' argument isn't provided, this value is used instead.
// Defaults to INT_MAX.
//input_max_value: 2147483647
input_max_value: 10000000

// Specifies whether or not each built-in function's arguments are checked for
// correct type. When a function is given an argument different from what it
// expects, a warning is thrown before the function is ran anyway.
// Default: yes
warn_func_mismatch_argtypes: yes

import: conf/import/script_conf.txt
