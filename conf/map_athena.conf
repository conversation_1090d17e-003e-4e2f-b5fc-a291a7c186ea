//--------------------------------------------------------------
//rAthena Map-Server Configuration File
//--------------------------------------------------------------

// Note: "Comments" are all text on the right side of a double slash "//"
// Whatever text is commented will not be parsed by the servers, and serves
// only as information/reference.

//--------------------------------------------------------------
//                     Configuration Info
//--------------------------------------------------------------
// Interserver communication passwords, set in account.txt (or equiv.)
userid: s1
passwd: p1

// Character Server IP
// The map server connects to the character server using this IP address.
// NOTE: This is useful when you are running behind a firewall or are on
// a machine with multiple interfaces.
//char_ip: 127.0.0.1

// The map server listens on the interface with this IP address.
// NOTE: This allows you to run multiple servers on multiple interfaces
// while using the same ports for each server.
//bind_ip: 127.0.0.1

// Character Server Port
char_port: 6121

// Map Server IP
// The IP address which clients will use to connect.
// Set this to what your server's public IP address is.
//map_ip: 127.0.0.1

// Map Server Port
map_port: 5121

//Time-stamp format which will be printed before all messages.
//Can at most be 20 characters long.
//Common formats:
// %I:%M:%S %p (hour:minute:second 12 hour, AM/PM format)
// %H:%M:%S (hour:minute:second, 24 hour format)
// %d/%b/%Y (day/Month/year)
//For full format information, consult the strftime() manual.
//timestamp_format: [%d/%b %H:%M]

//If redirected output contains escape sequences (color codes)
stdout_with_ansisequence: no

//Makes server log selected message types to a file in the /log/ folder
//1: Log Warning Messages
//2: Log Error and SQL Error messages.
//4: Log Debug Messages
//Example: "console_msg_log: 7" logs all 3 kinds
//Messages logged by this overrides console_silent setting
console_msg_log: 0

// File path to store the console messages above
console_log_filepath: ./log/map-msg_log.log

//Makes server output more silent by omitting certain types of messages:
//1: Hide Information messages
//2: Hide Status messages
//4: Hide Notice Messages
//8: Hide Warning Messages
//16: Hide Error and SQL Error messages.
//32: Hide Debug Messages
//Example: "console_silent: 7" Hides information, status and notice messages (1+2+4)
console_silent: 0

//Where should all database data be read from?
db_path: db

// Enable the @guildspy and @partyspy at commands?
// Note that enabling them decreases packet sending performance.
enable_spy: no

// Read map data from GATs and RSWs in GRF files or a data directory
// as referenced by grf-files.txt rather than from the mapcache?
use_grf: no

// Console Commands
// Allow for console commands to be used on/off
// This prevents usage of >& log.file
console: off

// Database autosave time
// All characters are saved on this time in seconds (example:
// autosave of 60 secs with 60 characters online -> one char is saved every 
// second)
autosave_time: 300

// Min database save intervals (in ms)
// Prevent saving characters faster than at this rate (prevents char-server 
// save-load getting too high as character-count increases)
minsave_time: 100

// Apart from the autosave_time, players will also get saved when involved
// in the following (add as needed):
// 1: after every successful trade
// 2: after opening vending/every vending transaction
// 4: after closing storage/guild storage.
// 8: After hatching/returning to egg a pet.
// 16: After successfully sending a mail with attachment
// 32: After successfully submitting an item for auction
// 64: After successfully get/delete/complete a quest
// 128: After every bank transaction (deposit/withdraw)
// 256: After every attendance reward
// 4095: Always
// NOTE: These settings decrease the chance of dupes/lost items when there's a
// server crash at the expense of increasing the map/char server lag. If your 
// server rarely crashes, but experiences interserver lag, you may want to set
// these off.
save_settings: 4095

// Message of the day file, when a character logs on, this message is displayed.
motd_txt: conf/motd.txt

// When @help or @h is typed when you are a gm, this is displayed for helping new gms understand gm commands.
charhelp_txt: conf/charhelp.txt

// Load channel config from
channel_conf: conf/channels.conf

// Maps:
import: conf/maps_athena.conf

import: conf/import/map_conf.txt
