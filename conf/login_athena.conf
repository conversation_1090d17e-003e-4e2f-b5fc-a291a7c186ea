// Athena Login Server configuration file.
// Translated by <PERSON> <<EMAIL>>

// Note: "Comments" are all text on the right side of a double slash "//"
// Whatever text is commented will not be parsed by the servers, and serves
// only as information/reference.

// The login server listens on the interface with this IP address.
// NOTE: This allows you to run multiple servers on multiple interfaces
// while using the same ports for each server.
//bind_ip: 127.0.0.1
 
// Login Server Port
login_port: 6900

//Time-stamp format which will be printed before all messages.
//Can at most be 20 characters long.
//Common formats:
// %I:%M:%S %p (hour:minute:second 12 hour, AM/PM format)
// %H:%M:%S (hour:minute:second, 24 hour format)
// %d/%b/%Y (day/Month/year)
//For full format information, consult the strftime() manual.
//timestamp_format: [%d/%b %H:%M]

//If redirected output contains escape sequences (color codes)
stdout_with_ansisequence: no

//Makes server log selected message types to a file in the /log/ folder
//1: Log Warning Messages
//2: Log Error and SQL Error messages.
//4: Log Debug Messages
//Example: "console_msg_log: 7" logs all 3 kinds
//Messages logged by this overrides console_silent setting
console_msg_log: 0

// File path to store the console messages above
console_log_filepath: ./log/login-msg_log.log

//Makes server output more silent by omitting certain types of messages:
//1: Hide Information messages
//2: Hide Status messages
//4: Hide Notice Messages
//8: Hide Warning Messages
//16: Hide Error and SQL Error messages.
//32: Hide Debug Messages
//Example: "console_silent: 7" Hides information, status and notice messages (1+2+4)
console_silent: 0

// Console Commands
// Allow for console commands to be used on/off
// This prevents usage of >& log.file
console: off

// Can you use _M/_F to make new accounts on the server?
// Note: This only works if client side password encryption is not enabled.
new_account: no

// If new_account is enabled, changes the minimum length for the account name.
// By default is set to '4' or '6' (depending on the new login UI).
// Don't change it unless your client uses the 'Remove 4/6 letter User Name limit' diff patch.
//acc_name_min_length: 6

// If new_account is enabled, changes the minimum length for the password.
// By default is set to '4' or '6' (depending on the new login UI).
// Don't change it unless your client uses the 'Remove 4/6 letter Password limit' diff patch.
//password_min_length: 6

// Account registration flood protection system
// allowed_regs is the number of registrations allowed in time_allowed (in seconds)
allowed_regs: 1
time_allowed: 10

// Log Filename. All operations received by the server are logged in this file. 
login_log_filename: log/login.log

// To log the login server?
// NOTE: The login server needs the login logs to enable dynamic pass failure bans.
log_login: yes

// Indicate how to display date in logs, to players, etc.
date_format: %Y-%m-%d %H:%M:%S

// Required account group id to connect to server.
// -1: disabled
// 0 or more: group id
group_id_to_connect: -1

// Minimum account group id required to connect to server.
// Will not function if group_id_to_connect config is enabled.
// -1: disabled
// 0 or more: group id
min_group_id_to_connect: -1

// Which group (ID) will be denoted as the VIP group?
// Default: 5
vip_group: 5

// How many characters are allowed per account?
// You cannot exceed the limit of MAX_CHARS slots, defined in mmo.hpp, or chars_per_account
// will default to MAX_CHARS.
// 0 will default to the value of MIN_CHARS. (default)
chars_per_account: 0

// Max character limit increase for VIP accounts (0 to disable)
// Increase the value of MAX_CHARS if you want to increase vip_char_increase.
// Note: The amount of VIP characters = MAX_CHARS - chars_per_account.
// Note 2: This setting must be set after chars_per_account.
// -1 will default to MAX_CHAR_VIP (src/config/core.hpp)
vip_char_increase: -1

// Create accounts with limited time?
// -1: new accounts are created with unlimited time (default)
// 0 or more: new accounts automatically expire after the given value, in seconds
start_limited_time: -1

// Store passwords as MD5 hashes instead of plain text?
// NOTE: Will not work with clients that use <passwordencrypt>
use_MD5_passwords: no

// User count colorization on login window (requires PACKETVER >= ********)
// Disable colorization and description in general?
usercount_disable: no
// Amount of users that will display in green
usercount_low: 200
// Amount of users that will display in yellow
usercount_medium: 500
// Amount of users that will display in red
usercount_high: 1000

// Ipban features
ipban_enable: yes
// Dynamic password failure ipban system
// Ban user after a number of failed attempts?
ipban_dynamic_pass_failure_ban: yes
// Interval (in minutes) to calculate how many failed attempts.
ipban_dynamic_pass_failure_ban_interval: 5
// Maximum amount of failed attempts before banning.
ipban_dynamic_pass_failure_ban_limit: 7
// Time (in minutes) for ban duration.
ipban_dynamic_pass_failure_ban_duration: 5
// Interval (in seconds) to clean up expired IP bans. 0 = disabled. default = 60.
// NOTE: Even if this is disabled, expired IP bans will be cleaned up on login server start/stop.
// Players will still be able to login if an ipban entry exists but the expiration time has already passed.
ipban_cleanup_interval: 60

// Interval (in minutes) to execute a DNS/IP update. Disabled by default.
// Enable it if your server uses a dynamic IP which changes with time.
//ip_sync_interval: 10

// DNS Blacklist Blocking
// If enabled, each incoming connection will be tested against the blacklists 
// on the specified dnsbl_servers (comma-separated list)
use_dnsbl: no
dnsbl_servers: bl.blocklist.de, socks.dnsbl.sorbs.net
// Here are some free DNS Blacklist Services: http://en.wikipedia.org/wiki/Comparison_of_DNS_blacklists
//==============================================================================
//   dnsbl_servers                 Description
// - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
// bl.blocklist.de                 IP-Addresses who attack other servers/honeypots over SSH, FTP, IMAP, etc.
// ircbl.ahbl.org                  AHBL (open proxies, compromised machines, comment spammers)
// safe.dnsbl.sorbs.net            All zones in dnsbl.sorbs.net except "recent" and "escalations"
// sbl-xbl.spamhaus.org            Spamhaus blacklist (spammers, open proxies)
// socks.dnsbl.sorbs.net           Open SOCKS proxy servers
// tor.ahbl.org                    Current tor relay and exit nodes

// Client MD5 hash check
// If turned on, the login server will check if the client's hash matches
// the value below, and will not connect tampered clients.
// Note: see 'doc/md5_hashcheck.txt' for more details.
client_hash_check: off

// Enable web authentication token system
// This is required for new clients that get data via an additional API over HTTP
use_web_auth_token: yes

// Delay (in milliseconds) disabling webtoken after character logs off
// There's a race condition when the client logs off. The char-server could revoke the token before
// the client can save the changed configs.
disable_webtoken_delay: 10000

// Client MD5 hashes
// The client with the specified hash can be used to log in by players with
// a group_id equal to or greater than the given value.
// If you specify 'disabled' as hash, players with a group_id greater than or
// equal to the given value will be able to log in regardless of hash (and even
// if their client does not send a hash at all.)
// Format: group_id, hash
// Note: see 'doc/md5_hashcheck.txt' for more details.
//client_hash: 0, 113e195e6c051bb1cfb12a644bb084c5
//client_hash: 10, cb1ea78023d337c38e8ba5124e2338ae
//client_hash: 99, disabled

import: conf/inter_athena.conf
import: conf/import/login_conf.txt
