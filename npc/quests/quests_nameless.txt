//===== rAthena <PERSON> =======================================
//= Nameless Island Quests
//===== Description: =========================================
//= [Official Conversion]
//= Collection of Nameless Island Quests
//= Nameless Island:
//= - Quest to gain access to Nameless Island and dungeon.
//= - Variable in use: aru_monas  (Max: 26)
//= Peace for Arunafeltz:
//- - Help to keep peace between Arunafeltz and Midgard.
//= - Variable in use: aru_em (Max: 23)
//= Mu<PERSON>'s Loan:
//- - Help Mu<PERSON> clear his Debt and get his collateral back.
//= - prerequisite Quest to "Broken Diamond"
//= - Variable in use: diamond_edq (Max: 14)
//= Broken Diamond:
//- - Help <PERSON> retrieve his stolen diamond.
//= - prerequisite Quest to "Unlucky Emerald"
//= - Variable in use: diamond_edq (Max: 26)
//= Z-Gang Quest:
//- - Help to aprehend the infamous Z-Gang.
//= - Variable in use: zdan_edq (Max: 19)
//= Unlucky Emerald:
//- - Help a treasure hunter from Prontera get home, and 
//=   learn the origins of the Unlucky Emerald.
//= - Variable in use: treasure_nd (Max: 12)
//= - Variable in use: jewel_nd (Max: 31)
//= Rogue Guild Investigation Assistance:
//- - Assist the Rogue Guild with their investigations.
//= - Variable in use: rumour_nd (Max: 22)
//===== Changelog: ===========================================
//= 1.0 First version. [L0ne_W0lf]
//= 1.1 Made quest accessable to "Failed" Geoborg quest. [L0ne_W0lf]
//= 1.1a Touch up to other Geoborg fail-points. [L0ne_W0lf]
//= 1.2 Some final tuning to Nameless Island quest. [Koca]
//= 1.3 Should fix any more issues with "Creature" npc. [L0ne_W0lf]
//= 1.4 Changed the way Out_from_Monastery works slightly. [L0ne_W0lf]
//= 1.5 added a next to stop double npc message [Yommy]
//= 1.6 Updated the Nameless Island Access quest
//=     Added Quest: Peace for Arunafeltz
//=     Added Quest: Muff's Loan
//=     Added Quest: Broken Diamond
//=     Added Quest: Z-Gang Quest
//=     Added Quest: Unlucky Emerald
//=     Added Quest: Rogue Guild Investigation
//= 1.7 Fixed a typo in monestary quest. [L0ne_W0lf]
//= 1.8 Renamed temporary global variable. [L0ne_W0lf]
//=     moved OnMyMobDead for access quest to a different NPC.
//= 1.9 Fixed mapwarp warping to disabled map. [L0ne_W0lf]
//= 2.0 Removed duplicate dialog. [L0ne_W0lf]
//= 2.1 Fixed a race condition blocking progress. [brianluau]
//= 2.2 Added quest log entries for:
//=     - Muff's Loan
//=     - Broken Diamond
//=     - Z-Gang Quest
//= 2.2a Fixed a few job constant typos. [Euphy]
//= 2.3 Updated RE/Pre-RE EXP. [Euphy]
//= 2.4 Moved Dorian to pre-re/re paths. [Euphy]
//= 2.5 Updated Nameless Island quests and added questlog support. [Euphy]
//= 2.6 Commented custom quest log support for the Nameless Island Quest [Everade]
//============================================================

// Nameless Island and Cursed Monestary Access Quest :: aru_monas
//============================================================
airplane_01,90,63,0	script	Event Switch#pc	-1,3,3,{
	end;

OnTouch:
	if ((prt_curse == 36 || prt_curse == 45 || prt_curse == 56 || prt_curse == 61) && (aru_monas < 1)) {
		hideoffnpc "Agent#pc1";
		mes "[????]";
		mes "Are you "+strcharinfo(0)+"?";
		mes "I've been waiting for you.";
		mes "^6B8E23Priest Bamph^000000 is expecting";
		mes "your arrival at ^6B8E23Prontera";
		mes "Church^000000. Please visit him";
		mes "as soon as you can.";
		set aru_monas,1;
		//setquest 17000;
		close2;
		hideonnpc "Agent#pc1";
	}
	end;
}

airplane_01,95,61,3	script	Agent#pc1	899,{
	end;

OnInit:
	hideonnpc "Agent#pc1";
	end;
}

cmd_in02,174,89,7	script	Ordinary Man#pc1	97,{
	if (aru_monas < 2) {
		mes "[Larjes]";
		mes "Damn it, I lost again!";
		mes "This game is rigged!";
		mes "No matter what I try,";
		mes "I can never seem to win!";
		mes "But Comodo Hold 'Em";
		mes "is so... It's so addicting!";
		close;
	}
	else if (aru_monas == 2) {
		mes "[Larjes]";
		mes "Argh, I lost at this game";
		mes "so many times that I've";
		mes "completely lost track!";
		mes "I've got to win sometime...";
		next;
		select("Excuse me.");
		mes "[Larjes]";
		mes "Oh! Uh... Hmm...";
		mes "How may I help you?";
		next;
		select ("^6B8E23Priest Bamph^000000 sent me.");
		mes "[Larjes]";
		mes "Oh, I see. I apologize";
		mes "if my screaming and yelling";
		mes "made you a bit uncomfortable.";
		mes "I'm just here to kill some";
		mes "time... I now see firsthand";
		mes "that gambling is truly evil!";
		next;
		mes "[Larjes]";
		mes "Er, would you come";
		mes "over this way? I don't";
		mes "want anyone else to";
		mes "overhear us talking.";
		close2;
		set aru_monas,3;
		warp "cmd_in02",110,53;
		end;
	}
	else {
		mes "[Larjes]";
		mes "What?! I lost again?";
		mes "How does... How does";
		mes "this game even work?!";
		close;
	}
}

cmd_in02,111,52,3	script	Ordinary Man#pc2	97,{
	if (aru_monas < 3) {
		mes "[Larjes]";
		mes "^666666*Sigh...*^000000";
		mes "This isn't good at all.";
		mes "I don't know what to do...";
		close;
	}
	else if (aru_monas == 3) {
		mes "[Larjes]";
		mes "Good, no one should be";
		mes "able to hear us from here.";
		mes "After I sent a message to";
		mes "the place, I found some";
		mes "new information that should";
		mes "really help my investigation.";
		next;
		mes "[Larjes]";
		mes "I learned that two details";
		mes "about the night when that";
		mes "high ranking man vanished.";
		mes "Firstly, he left the guards for";
		mes "a bit to meet a woman. It's";
		mes "always a woman, isn't it?";
		next;
		mes "[Larjes]";
		mes "Secondly, after he left to";
		mes "meet that woman, some other";
		mes "people came in, and then they";
		mes "carried some really large";
		mes "baggage with them when";
		mes "they left to the west.";
		next;
		mes "[Larjes]";
		mes "Those bags were so big,";
		mes "it's suspicious. It seems";
		mes "like some organization has";
		mes "kidnapped him, and wants to";
		mes "bring him west for some reason.";
		next;
		select("Why to the west?");
		mes "[Larjes]";
		mes "This is just a guess, but";
		mes "I think whoever kidnapped";
		mes "him are from ^2F4F2FArunafeltz^000000.";
		mes "I'm prohibited from entering";
		mes "Arunafeltz so I wasn't able";
		mes "to investigate any further.";
		set aru_monas,4;
		//changequest 17001,17002;
		close;
	}
	else {
		mes "[Larjes]";
		mes "I gambled away";
		mes "this much money?";
		mes "How am I gonna pay";
		mes "off all of this debt?!";
		close;
	}
}

ra_in01,308,59,1	script	Waiter#pc	930,{
	if (aru_monas < 7) {
		mes "[Waiter]";
		mes "Welcome to";
		mes "Rachel's Palate, one";
		mes "of Rachel's finest and";
		mes "most exquisite restraunts.";
		if (aru_monas < 6) {
			close;
		}
		if (aru_monas == 6) {
			next;
			mes "[Waiter]";
			mes "What would you like";
			mes "to order? Or... If I'm";
			mes "not mistaken, you're";
			mes "here for something else?";
			next;
			if (select("I'd like to order.:Yes, I've come for another reason.") == 1) {
				mes "[Waiter]";
				mes "Sure, no problem.";
				mes "Did you come alone?";
				next;
				select("Yes.");
				mes "[Waiter]";
				mes "Right this way.";
				close2;
				set aru_monas,7;
				switch(rand(1,4)) {
					case 1: warp "ra_in01",303,43; end;
					case 2: warp "ra_in01",304,43; end;
					case 3: warp "ra_in01",304,39; end;
					case 4: warp "ra_in01",303,39; end;
				}
			}
			mes "[Waiter]";
			mes "Hm, okay. Well, whatever";
			mes "it is, I hope you enjoy your";
			mes "stay at Rachel's Palate.";
			close;
		}
	}
	mes "[Waiter]";
	mes "The food in this";
	mes "restaurant is the";
	mes "best in Rachel--no,";
	mes "it is the best in all";
	mes "of Arunafeltz!";
	close;
}

ra_in01,301,50,3	script	Normal-Looking Man	927,{
	mes "[Normal-Looking Man]";
	mes "Mmm-Mmm!";
	mes "I love the food in";
	mes "this restaurant!";
	mes "They don't serve";
	mes "anything this this";
	mes "good anywhere else!";
	close;
}

ra_in01,301,52,3	script	Common-Looking Man	927,{
	mes "[Common-Looking Man]";
	mes "The food here isn't bad,";
	mes "but my favorite restaurants";
	mes "are over in Hugel. The food";
	mes "over there is definitely best.";
	close;
}

ra_in01,297,50,7	script	Suspicious-Looking Man	926,{
	mes "[Suspicious-Looking Man]";
	mes "Each dish has its own";
	mes "unique taste that you must";
	mes "learn to relish. Of course,";
	mes "unhealthy junk food is the";
	mes "exception to this rule.";
	close;
}

ra_in01,301,43,0	script	MealConversation Trigger	-1,3,3,{
	end;

OnTouch:
	if (checkweight(907,200) == 0) {
		mes "^3355FFWait a second!";
		mes "Right now, you're carrying";
		mes "too many things with you.";
		mes "Please come back after";
		mes "using the Kafra Service";
		mes "to store some of your items.^000000";
		close;
	}
	if (aru_monas == 7) {
		mes "[Waiter]";
		mes "What would you like to order?";
		mes "Our special today is Fried";
		mes "Veins Stripe Stickleback";
		mes "since we received some";
		mes "high quality fish from Veins";
		mes "just this morning.";
		next;
		select("...Yes, that sounds good.");
		mes "[Waiter]";
		mes "Excellent choice.";
		mes "Just to let you know,";
		mes "that dish is ^9932CD3,000 zeny^000000.";
		mes "I'll be right back.";
		next;
		mes "^3355FFThe waiter took your";
		mes "order, and left to go to";
		mes "the kitchen. You decide to";
		mes "eavesdrop on the people";
		mes "talking at the next table";
		mes "while you're waiting.^000000";
		next;
		mes "[Husky Male Voice]";
		mes "...............................";
		mes "I really saw it, the greatest";
		mes "fish in the world! Last time";
		mes "I went to Veins, I glimpsed";
		mes "the Veins Golden Stripe";
		mes "Stickleback! It's true!";
		next;
		mes "[Gloomy Male Voice]";
		mes "So were you able to taste";
		mes "that Veins Golden Stripe";
		mes "Stickleback juice?";
		next;
		mes "[Average Male Voice]";
		mes "Aw man, if I did, that";
		mes "would've been a once";
		mes "in a lifetime experience!";
		mes "Veins Golden Stripe";
		mes "Sticklebacks are only";
		mes "caught once a decade!";
		next;
		mes "[Husky Male Voice]";
		mes "Oh yeah, that juice is";
		mes "incredible. My voice even";
		mes "changed after I drank it!";
		mes "It was just... So profound.";
		next;
		mes "[Average Male Voice]";
		mes "Oh! So that's why";
		mes "you sound different.";
		next;
		mes "[Gloomy Male Voice]";
		mes "How did you get to drink";
		mes "such rare juice? I mean,";
		mes "there must be tons of people";
		mes "in line with reservations,";
		mes "just waiting to drink it!";
		next;
		mes "[Husky Male Voice]";
		mes "I got damn lucky: I went";
		mes "to this tavern in Veins, and";
		mes "overheard that ^DB7093a group of";
		mes "smugglers were arrested";
		mes "by the guards at the";
		mes "southeast beach^000000.";
		next;
		mes "[Husky Male Voice]";
		mes "Then I started thinking:";
		mes "maybe the smugglers were";
		mes "fishing. So I checked the";
		mes "beach to see if I could";
		mes "find their fishing boat.";
		next;
		mes "[Average Male Voice]";
		mes "Man, you must be obsessed";
		mes "with food. I mean, how else";
		mes "would you figure out that the";
		mes "smugglers were illegally";
		mes "fishing so quickly?";
		next;
		mes "[Husky Male Voice]";
		mes "Ha, I guess you're right!";
		mes "Anyway, there was only one";
		mes "fishing boat, and the guards";
		mes "were inspecting it. I was about";
		mes "to turn back in disappointment";
		mes "but then... I saw a fisherman!";
		next;
		mes "[Husky Male Voice]";
		mes "Out of the corner of my eye,";
		mes "I saw him bringing in his";
		mes "catch for the day, Veins";
		mes "Golden Strip Stickleback!";
		mes "I bought all of his fish";
		mes "right then and there.";
		next;
		mes "[Gloomy Male Voice";
		mes "Boy, were you lucky.";
		mes "And you never hestitate";
		mes "to splurge when it comes";
		mes "to food, do you? Heh heh~";
		next;
		mes "[Husky Male Voice]";
		mes "Oh, that wasn't even a";
		mes "splurge. I'm sure you guys";
		mes "would do the same thing";
		mes "if you were in my situation.";
		mes "I'm just grateful that I was";
		mes "able to taste such rare fish.";
		next;
		mes "^3355FF...............................";
		mes "Then, the men started";
		mes "to compliment each other.^000000";
		next;
		mes "[Waiter]";
		mes "Ah, here's your Fried";
		mes "Veins Stripe Stickleback.";
		mes "Please enjoy your meal.";
		next;
		mes "["+strcharinfo(0)+"]";
		mes "^ff0000Veins^000000, huh? I might want";
		mes "to check that place one";
		mes "of these days to see if";
		mes "I can find more of this fish.";
		next;
		mes "["+strcharinfo(0)+"]";
		mes "Whoa, this fish tastes...";
		mes "I've never tasted anything";
		mes "like it! It's almost like";
		mes "a dessert. God, it's good!";
		set aru_monas,8;
		//changequest 17003,17004;
		if (Zeny > 3000) {
			set Zeny, Zeny-3000;
			getitem 12052,4; //Vit_Dish02
		}
		close;
	}
	end;
}

ve_in,78,314,5	script	Magistrate#Aru	945,{
	if (aru_monas < 8) {
		mes "[Al Hamad]";
		mes "Argh! Why must this";
		mes "happen to me, especially";
		mes "just when I'm about to";
		mes "retire! A-ah! My ulcer...";
		mes "Just all of a sudden, it--!";
		close;
	}
	else if (aru_monas == 8) {
		mes "[Al Hamad]";
		mes "Man, this is probably";
		mes "the biggest hassle of my";
		mes "career. Why does this have";
		mes "to happen now? Retirement";
		mes "is so close, yet so far.";
		next;
		select("Are you in trouble?");
		mes "[Al Hamad]";
		mes "Huh? You don't look like";
		mes "you're from around here.";
		mes "Listen, I appreciate your";
		mes "concern, but I'm really not";
		mes "comfortable talking to complete";
		mes "strangers about my problems.";
		set aru_monas,9;
		//changequest 17004,17005;
		close;
	}
	else if (aru_monas == 9) {
		mes "[Al Hamad]";
		mes "Unless you've got special";
		mes "business here, you're not";
		mes "allowed to enter this place.";
		mes "We're here to keep the public";
		mes "peace, and letting anyone";
		mes "in here will cause trouble.";
		close;
	}
	else if (aru_monas == 10) {
		mes "[Al Hamad]";
		mes "I thought I told you";
		mes "that you weren't allowed";
		mes "to come in here. Why do";
		mes "you keep coming back?";
		next;
		if (select("I'm sorry.:I want to know about the smugglers.") == 1) {
			mes "[Al Hamad]";
			mes "It's fine. Just leave,";
			mes "and don't come back";
			mes "unless you've got a";
			mes "really good reason.";
			close;
		}
		mes "[Al Hamad]";
		mes "Hey, how'd you find out";
		mes "about the smugglers? Did";
		mes "word spread around town?";
		mes "Well, I'm sorry, but I can't";
		mes "disclose any details about";
		mes "an ongoing investigation.";
		next;
		mes "[Al Hamad]";
		mes "^9370DBIf you insist on learning";
		mes "more, then you'd need to";
		mes "bring me a written order";
		mes "from a higher ranking officer.^000000";
		mes "Oh, and you're prohibited";
		mes "asking anyone else, so behave.";
		set aru_monas,11;
		//changequest 17006,17007;
		close;
	}
	else if (aru_monas < 13) {
		mes "[Al Hamad]";
		mes "I don't blame you for";
		mes "being curious, but getting";
		mes "involved when you're not";
		mes "supposed to will just cause";
		mes "trouble. Be careful, got it?";
		close;
	}
	else if (aru_monas == 13) {
		mes "[Al Hamad]";
		mes "Huh, that's weird. You're not";
		mes "even a citizen of Arunafeltz,";
		mes "but somehow you know such";
		mes "a high ranking priest. Alright,";
		mes "I guess I can tell you more";
		mes "about our investigation.";
		next;
		mes "[Al Hamad]";
		mes "I should let you know that";
		mes "what I tell you might not be";
		mes "worth all that trouble. This";
		mes "information probably isn't";
		mes "as valuable as you think.";
		next;
		mes "[Al Hamad]";
		mes "The smugglers we caught are";
		mes "the worst kind of scoundrels:";
		mes "they deal in human trafficking, but we caught them this time when they";
		mes "were crossing the ocean. Their background info is pretty typical.";
		next;
		mes "[Al Hamad]";
		mes "Let's see... They kidnapped";
		mes "some man from Comodo.";
		mes "That's a place in the southwest";
		mes "part of Rune-Midgarts. Anyway,";
		mes "the smugglers thought he'd be";
		mes "wealthy, so they set a trap.";
		next;
		mes "[Al Hamad]";
		mes "They lured him away from his";
		mes "guards by sending a woman,";
		mes "but when they captured him,";
		mes "they found that he was not only";
		mes "rich, he was also a high ranked";
		mes "official of Rune-Midgarts.";
		next;
		mes "[Al Hamad]";
		mes "That's when they got sloppy:";
		mes "they were so scared that the";
		mes "kingdom's troops might pursue";
		mes "them that they crossed the";
		mes "ocean that we patrol. That's";
		mes "where we caught them.";
		next;
		mes "[Al Hamad]";
		mes "What I don't understand is";
		mes "why they were so scared.";
		mes "I mean, they mainly deal";
		mes "in human trafficking so...";
		mes "Kidnapping an official isn't";
		mes "that big a difference, is it?";
		next;
		mes "[Al Hamad]";
		mes "We contacted the temple,";
		mes "and reported the smugglers,";
		mes "our investigation, and the";
		mes "high ranking man from the";
		mes "Rune-Midgarts kingdom.";
		next;
		mes "[Al Hamad]";
		mes "Then, some people from";
		mes "the temple interrupted our";
		mes "investigation, took away the";
		mes "smugglers before we finished";
		mes "our interrogations, and we";
		mes "haven't seen them since.";
		next;
		mes "[Al Hamad]";
		mes "I asked an acquaintance that";
		mes "works at the temple, but he";
		mes "doesn't know what's going";
		mes "on either. The ^32CD32Rune-Midgarts";
		mes "official^000000 has gone missing too.";
		mes "It's all very frustrating.";
		next;
		mes "[Al Hamad]";
		mes "That happens to be all";
		mes "I know. Does this mean";
		mes "we have to capture the";
		mes "smugglers again? I don't";
		mes "know what's going on...";
		set aru_monas,14;
		//changequest 17009,17010;
		next;
		mes "[Al Hamad]";
		mes "Even if I could make headway";
		mes "into the investigation, I've";
		mes "been ordered to stop by upper";
		mes "management. It looks like";
		mes "this case is more trouble";
		mes "than it's worth to them?";
		next;
		mes "[Al Hamad]";
		mes "I don't want to be involved";
		mes "in this anymore. I should be";
		mes "retired already! If you want to";
		mes "learn more, your only choice";
		mes "would be to find out on your";
		mes "own. Fat chance, right?";
		close;
	}
	else {
		mes "[Al Hamad]";
		mes "I've suffered enough";
		mes "already! Why can't this";
		mes "case just be done?!";
		close;
	}
}

ve_in,81,296,3	script	Soldier#Aru	946,{
	if (aru_monas < 9) {
		mes "[Himus]";
		mes "Huh. The magistrate";
		mes "still seems pretty upset";
		mes "over the case that happened";
		mes "at least a few months ago...";
		close;
	}
	else if (aru_monas == 9) {
		mes "[Himus]";
		mes "Hm? Oh, the magistrate";
		mes "gave you the brush-off?";
		mes "Well, he hasn't really been";
		mes "himself ever since we arrested";
		mes "those ^32CD32smugglers coming from";
		mes "the south^000000 a few months ago.";
		next;
		mes "[Himus]";
		mes "Once we reported the arrest";
		mes "to the temple, the pope's";
		mes "soldiers came and took the";
		mes "smugglers away. We have no";
		mes "clue what happened to them";
		mes "or where they even are now.";
		next;
		mes "[Himus]";
		mes "It might not seem like";
		mes "a big deal to you, but";
		mes "the magistrate is pretty";
		mes "meticulous, and he hates";
		mes "it when people interfere";
		mes "with his job, you know?";
		next;
		select("Who are the smugglers?");
		mes "[Himus]";
		mes "Well, I don't really know.";
		mes "Maybe the magistrate would";
		mes "know if he was able to finish";
		mes "interrogating the smugglers.";
		mes "I'm not sure if he did since the smugglers were taken away.";
		set aru_monas,10;
		//changequest 17005,17006;
		close;
	}
	else if (aru_monas == 10) {
		mes "[Himus]";
		mes "The magistrate is about";
		mes "to retire, so I hope he can";
		mes "finally quit work and just";
		mes "forget about this whole";
		mes "business. He does nothing";
		mes "but stress out about it!";
		close;
	}
	else {
		mes "[Himus]";
		mes "Nice day, isn't it?";
		close;
	}
}

ve_in,239,115,0	script	Drunkard#Aru	943,{
	if (aru_monas < 14) {
		mes "[Drunkard]";
		mes "Man, I feel great! Hah hah!";
		mes "Hey, gimme one more drink!";
		close;
	}
	if (aru_monas == 14) {
		if (rachel_camel == 25) {
			mes "[Drunkard]";
			mes "So--*Hic* What I was...";
			mes "Oog, dizzy... I say sayin',";
			mes "that guy that ^32CD32snuck out to";
			mes "sea on a boat^000000? You know";
			mes "*Hic* why he came back";
			mes "so early? You won't believe it!";
			next;
			mes "[Drunkard]";
			mes "Hah hah! He--*Hic*";
			mes "He thought he saw a ^DBDB70ghost^000000!";
			mes "Bwah hah hah! Oooog... Hey...";
			set aru_monas,15;
			//changequest 17010,17011;
			next;
			mes "["+strcharinfo(0)+"]";
			mes "That's right!";
			mes "Maybe I should check";
			mes "on ^32CD32Kurdi's father since";
			mes "he's a fisherman^000000";
			close;
		}
		else {
			mes "[Drunkard]";
			mes "Zzzz...";
			mes "Umm... Zzz...";
			close;
		}
	}
	else {
		mes "[Drunkard]";
		mes "Why?! Why can't I take";
		mes "my boat out to sea? I hafta";
		mes "catch fish to make a living!";
		mes "What, they expect me to starve";
		mes "to death or something?! Huh?!";
		close;
	}
}

ve_in,238,120,4	script	Drunkard#Aru1	940,{
	mes "[Drunkard]";
	mes "Hohohoho~";
	mes "Oh~ Hohohoho~";
	next;
	mes "[" + strcharinfo(0) + "]";
	mes "She's kind of strange...";
	close;
}

ve_fild07,125,128,0	script	Boat#Aru	111,{
	if (aru_monas < 16) {
		mes "^3355FFThis boat seems to ";
		mes "be in decent condition.";
		mes "Who could its owner be?^000000";
		close;
	}
	else if (aru_monas == 16) {
		mes "^3355FFThis must be the boat that";
		mes "Karyn was talking about.";
		mes "It seems to be in pretty";
		mes "good shape. Now, the";
		mes "monastery should be";
		mes "southwest from here...^000000";
		next;
		hideoffnpc "Secret Agent#Aru";
		next;
		mes "[Larjes]";
		mes "Long time no see,";
		mes ""+strcharinfo(0)+".";
		next;
		mes "["+strcharinfo(0)+"]";
		mes "Larjes!";
		next;
		mes "[Larjes]";
		mes "I had a tough time following";
		mes "you, you know? I actually";
		mes "almost lost you once. You";
		mes "must be wondering why";
		mes "I'm here. If you'll listen,";
		mes "I'll explain everything.";
		next;
		mes "[Larjes]";
		mes "According to our investigation, Arunafeltz is definitely involved";
		mes "in this case. We've been watching you since you're somehow related";
		mes "to that high ranked official from Arunafeltz. I apologize for that.";
		next;
		mes "[Larjes]";
		mes "This is an international";
		mes "incident, so to prevent any";
		mes "intel leaks to Arunafeltz,";
		mes "we've had to be much stricter";
		mes "with securing and protecting";
		mes "all information for this case.";
		next;
		mes "[Larjes]";
		mes "Anyway, I've decided to";
		mes "reveal myself to you since";
		mes "I'm the only one that can help";
		mes "you right now. You're thinking";
		mes "of using that boat, aren't you?";
		next;
		mes "[Larjes]";
		mes "If you don't have the skills,";
		mes "knowledge, and preparation";
		mes "to sail those waters ahead,";
		mes "you'll probably wreck the";
		mes "boat. You'll make it if you're lucky but... That's a lot of luck.";
		next;
		mes "[Larjes]";
		mes "I'll help you sail this";
		mes "boat to get where you're";
		mes "going. But if you fail, there's";
		mes "a chance you might have to";
		mes "sail this boat on your own if I'm not here. So, you ready to go?";
		next;
		switch(select("Yes, I'm ready.:Give me more time.")) {
		case 1:
			mes "[Larjes]";
			mes "Alright, let's go.";
			mes "Hold on to something:";
			mes "this will probably be";
			mes "a pretty rocky ride...";
			set aru_monas,17;
			//changequest 17012,17013;
			close2;
			hideonnpc "Secret Agent#Aru";
			warp "nameless_i",257,217;
			end;
		case 2:
			mes "[Larjes]";
			mes "Alright. Just let me know";
			mes "whenever you're ready.";
			close;
		}
	}
	else if (aru_monas < 20) {
		mes "^3355FFThis is the boat that";
		mes "can take you to the";
		mes "monastery.^000000";
		next;
		switch(select("Go to Monastery:Cancel")) {
		case 1:
			mes "^3355FFYou set sail for";
			mes "the monastery...^000000";
			close2;
			warp "nameless_i",257,217;
			end;
		case 2:
			mes "^3355FFYou decide to";
			mes "stay ashore.^000000";
			close;
		}
	}
	else {
		mes "^3355FFThis is the boat that";
		mes "can take you to the";
		mes "monastery.^000000";
		next;
		switch(select("Go to Monastery:Cancel")) {
		case 1:
			mes "^3355FFYou set sail for";
			mes "the monastery...^000000";
			close2;
			warp "nameless_n",257,217;
			end;
		case 2:
			mes "^3355FFYou decide to";
			mes "stay ashore.^000000";
			close;
		}
	}
}

ve_fild07,128,130,1	script	Secret Agent#Aru	97,{
	if (aru_monas == 16) {
		mes "[Larjes]";
		mes "Are you ready to go";
		mes "aboard? I'll help you";
		mes "sail to that monastery,";
		mes "but if you fail while you're";
		mes "there, I might not be here";
		mes "when you come back.";
		next;
		switch(select("Yes, I'm ready.:Give me more time.")) {
		case 1:
			mes "[Larjes]";
			mes "Alright, let's go.";
			mes "Hold on to something:";
			mes "this will probably be";
			mes "a pretty rocky ride...";
			set aru_monas,17;
			//changequest 17012,17013;
			close2;
			hideonnpc "Secret Agent#Aru";
			warp "nameless_i",257,217;
			end;
		case 2:
			mes "[Larjes]";
			mes "Alright. Just let me know";
			mes "whenever you're ready.";
			close;
		}
	}
	end;

OnInit:
	hideonnpc "Secret Agent#Aru";
	end;
}

nameless_i,212,184,0	script	Outside Island#Aru	-1,2,3,{
	end;

OnTouch:
	if (aru_monas == 17) {
		mes "^3355FFThe village is totally";
		mes "silent, as if all life had";
		mes "abandoned it. You look";
		mes "around and see that something";
		mes "was here a few hours ago, but";
		mes "it's somewhere else now.^000000";
		next;
		mes "^3355FFWhatever it was,";
		mes "it definitely wasn't";
		mes "human. What kind of";
		mes "creature could it be?^000000";
		close;
	}
	end;
}

nameless_in,21,176,0	script	Inside Island#Aru	-1,2,2,{
	end;

OnTouch:
	if (aru_monas == 17) {
		mes "^3355FFThere are traces of";
		mes "humans around here,";
		mes "along with some kind";
		mes "of creature that you";
		mes "can't clearly identify.^000000";
		close;
	}
	end;
}

nameless_i,129,218,0	script	Grass Behind#Aru	-1,4,3,{
	end;

OnTouch:
	if (aru_monas == 17) {
		mes "^3355FFA strange scent strikes";
		mes "you as you enter this field of";
		mes "grass. A few ^32CD32animal corpses^3355FF";
		mes "are strewn around the ground.^000000";
		close;
	}
	end;
}

nameless_i,127,207,0	script	Dead Crow#Aru	111,{
	if (aru_monas < 17) { 
		mes "^3355FFThere is a dead crow on the ground.";
		close;
	}
	else if (aru_monas == 17) {
		mes "^3355FFThe sight of this ^32CD32dead";
		mes "crow^3355FF makes you feel";
		mes "uneasy for some reason.^000000";
		next;
		if (select("Ignore:Investigate") == 1) {
			mes "^3355FFYou decide not to touch";
			mes "the animal's carcass.^000000";
			close;
		}
		mes "^3355FFYou notice some grass sap";
		mes "on the crow's beak, so it";
		mes "must have been nibbling on";
		mes "some grass. Some feathers";
		mes "are missing, revealing";
		mes "scaly, snake-like skin.^000000";
		next;
		mes "["+strcharinfo(0)+"]";
		mes "Wait...";
		mes "Did this grass...?";
		next;
		mes "^3355FFThis grass must be the";
		mes "main ingredient of the";
		mes "poison used to kill the";
		mes "Geoborg family princes.^000000";
		next;
		mes "["+strcharinfo(0)+"]";
		mes "Looks like I just hit";
		mes "the jackpot. Huh?";
		mes "Someone behi--";
		next;
		mes "^3355FFA sharp, throbbing pain";
		mes "assails the back of your";
		mes "head as you fall into";
		mes "unconsciousness...^000000";
		set aru_monas,18;
		close2;
		warp "nameless_in",15,60;
		end;
	}
	end;
}

nameless_in,15,61,0	script	Pass Out#Aru	-1,2,3,{
	end;

OnTouch:
	if (aru_monas == 18) {
		disablenpc "Out_from_Monastery";
		sc_start SC_Blind,600000,0,10000;
		mes "^3355FFThe pressure on your";
		mes "stomach and the blood";
		mes "rushing to your head tells";
		mes "you someone is carrying you";
		mes "over his shoulder. He stops,";
		mes "and you hear a faint voice.^000000";
		next;
		mes "^3355FFThe faint voice steadily";
		mes "grows stronger and more";
		mes "distinct--someone's calling";
		mes "your name. You feel cold water";
		mes "trickle through your lips, and";
		mes "then you regain your senses.^000000";
		next;
		sc_end SC_BLIND;
		hideoffnpc "Larjes#Monastery";
		next;
		mes "That was too close...";
		mes "For a second there,";
		mes "I thought I lost you.";
		next;
		mes "["+strcharinfo(0)+"]";
		mes "Larjes...? What...";
		mes "What happened?";
		mes "Ugh, my head...";
		next;
		mes "[Larjes]";
		mes "Try not to move for a while.";
		mes "I had a bad feeling waiting";
		mes "for you on the boat. Lucky";
		mes "thing. When I found you,";
		mes "these strange creatures";
		mes "were savagely attacking you!";
		next;
		mes "[" +strcharinfo(0)+ "]";
		mes "You saved me?";
		mes "Thank you. Do you";
		mes "happen to know what";
		mes "those creatures were?";
		next;
		mes "[Larjes]";
		mes "No clue. They looked";
		mes "like humans, but... They";
		mes "definitely weren't. Once";
		mes "I killed them, they all";
		mes "turned into sand.";
		next;
		hideoffnpc "Creature#Monas";
		emotion ET_HUK, playerattached();
		emotion ET_HUK, getnpcid(0, "Larjes#Monastery");
		mes "[Larjes]";
		mes "?!?!?!!!!!";
		next;
		mes "[" +strcharinfo(0)+ "]";
		mes "!!!!?!?!!!!!!";
		mes "Isn't... Isn't that...?";
		next;
		mes "[Larjes]";
		mes "It seems it's just like the";
		mes "creatures that kidnapped";
		mes "you, but... I wonder why";
		mes "he's not attacking us.";
		next;
		mes "[" +strcharinfo(0)+ "]";
		mes "I guess you'll have to go";
		mes "right up to him and ask.";
		set aru_monas,19;
		close;
	}
}

nameless_in,13,58,6	script	Larjes#Monastery	97,{
	mes "[Larjes]";
	mes "Be careful. That guy";
	mes "looks pretty dangerous.";
	close;

OnInit:
	hideonnpc "Larjes#Monastery";
	end;
}

nameless_in,13,53,1	script	Creature#Monas	1864,3,1,{
	end;

OnInit:
	hideonnpc "Creature#Monas";
	end;

OnTouch:
	if (aru_monas == 19 && mobcount("nameless_in","Creature#Monas::OnMyMobDead") < 1 && !@aru_monas_kill) {
		mes "[???????]";
		mes "Grrr~!!!";
		close2;
		monster "nameless_in",13,53,"Zombie",1864,1,"Creature#Monas::OnMyMobDead";
		hideonnpc "Creature#Monas";
	}
	end;

OnMyMobDead:
	set @aru_monas_kill,1;
	enablenpc "Out_from_Monastery";
	end;
}

nameless_in,12,37,0	script	Out_from_Monastery	45,1,1,{
	end;

OnTouch:
	if (aru_monas == 19) {
		set aru_monas,20;
		warp "nameless_n",168,252;
		end;
	}

OnInit:
	disablenpc "Out_from_Monastery";
	end;
}



nameless_i,168,257,0	script	outtoin_01#mo	45,1,1,{
	end;

OnTouch:
	if (aru_monas == 18 || aru_monas == 19) {
		warp "nameless_in",12,41;
		end;
	}
	mes "^3355FFThe door won't budge:";
	mes "you can't go through.^000000";
	close;
}

nameless_n,169,254,0	script	Night#Aru2	-1,3,3,{
	end;

OnTouch:
	if(aru_monas == 20) {
		hideoffnpc "Larjes#Monastery2";
		mes "[" +strcharinfo(0)+ "]";
		mes "What the hell...!?";
		next;
		mes "[Larjes]";
		mes "So... This is the";
		mes "island's true nature.";
		next;
		mes "[Larjes]";
		mes "Someone like me won't";
		mes "survive long in a place";
		mes "like this. I'm going to wait";
		mes "for you in the boat. Learn";
		mes "what you need to learn, and then come back safe, "+strcharinfo(0)+".";
		set aru_monas,21;
		//changequest 17013,17014;
		close2;
		hideonnpc "Larjes#Monastery2";
	}
	end;
}

nameless_n,166,254,0	script	Larjes#Monastery2	97,{
	end;

OnInit:
	hideonnpc "Larjes#Monastery2";
	end;
}

nameless_i,259,218,3	script	Larjes#Aru	97,{
	if (aru_monas < 18) {
		mes "[Larjes]";
		mes "I don't like this place.";
		mes "You'd be better be careful";
		mes "around here with those weird";
		mes "creatures running around.";
		close;
	}
	else if (aru_monas < 24) {
		mes "[Larjes]";
		mes "Good, you're back";
		mes "I know that there's still";
		mes "things on this island that";
		mes "you want to investigate,";
		mes "but did you want to leave";
		mes "this place for a while?";
		next;
		if(select("Yes:No")==1) {
			mes "[Larjes]";
			mes "Alight, let's go.";
			close2;
			warp "ve_fild07",130,130;
			end;
		}
		mes "[Larjes]";
		mes "Alright, but becareful.";
		mes "Those creatures almost";
		mes "got you once, you know.";
		close;
	}
	else {
		mes "[Larjes]";
		mes "Did you still want to";
		mes "investigate the island?";
		next;
		if (select("No:Yes") == 1) {
			mes "[Larjes]";
			mes "Alright, let's go.";
			close2;
			warp "ve_fild07",130,130;
			end;
		}
		mes "[Larjes]";
		mes "Alright, but be careful.";
		mes "Those creatures almost";
		mes "got you once, you know.";
		close;
	}
}

nameless_n,259,218,3	script	Larjes#Boat1	97,{
	if (aru_monas == 24) {
		if (countitem(7726) < 1) {
			mes "[Larjes]";
			mes "Good, you're back.";
			mes "I know that there's still";
			mes "things on this island that";
			mes "you want to investigate,";
			mes "but did you want to leave";
			mes "this place for a while?";
			next;
			switch(select("Yes:No")) {
			case 1:
				mes "[Larjes]";
				mes "Alright, let's go.";
				close2;
				warp "ve_fild07",130,130;
				end;
			case 2:
				mes "[Larjes]";
				mes "Alright, but becareful.";
				mes "Those creatures almost";
				mes "got you once, you know.";
				close;
			}
		} else {
			mes "[Larjes]";
			mes "I don't believe it...";
			mes "That guy was Tristam III?!";
			mes "This explains a lot, I suppose.";
			mes "I'll report this along with the";
			mes "voucher. So Arunafeltz was";
			mes "behind that poison grass too...";
			next;
			mes "[Larjes]";
			mes "I'm shocked over this";
			mes "whole debacle. I'm sure";
			mes "the Rune-Midgarts royal family";
			mes "will be in an uproar over this.";
			mes "To think that we're involved";
			mes "in something this huge...";
			next;
			mes "[Larjes]";
			mes "Anyway, we better get";
			mes "going... This is huge!";
			delitem 7726,1; //Token_Of_King
			set aru_monas,25;
			//changequest 17016,17017;
			getexp (checkre(3))?100000:1000000,0;
			close;
		}
	}
	mes "[Larjes]";
	mes "Did you still want to";
	mes "investigate the island?";
	next;
	switch(select("No:Yes")) {
	case 1:
		mes "[Larjes]";
		mes "Alright, let's go.";
		close2;
		warp "ve_fild07",130,130;
		end;
	case 2:
		mes "[Larjes]";
		mes "Alright, but becareful.";
		mes "Those creatures almost";
		mes "got you once, you know.";
		close;
	}
}

abbey02,224,70,0	script	Books#Mona1	111,{
	if (checkweight(7766,1) == 0) {
		mes "^3355FFWait a second!";
		mes "Right now, you're carrying";
		mes "too many things with you.";
		mes "Please come back after";
		mes "using the Kafra Service";
		mes "to store some of your items.^000000";
		close;
	}
	if (aru_monas < 21) {
		mes "^3355FFIt's just a bunch";
		mes "of old, moldy books.^000000";
		close;
	}
	else if (aru_monas == 21) {
		mes "^3355FFThere's a book stained";
		mes "with blood amongst all";
		mes "these old, moldy books.^000000";
		next;
		if(select("Examine Book:Ignore")==1) {
			set aru_monas,22;
			//changequest 17014,17015;
			getitem 7755,1; // Reasearch_Note
			readbook 7755,1;
			close;
		}
		mes "^3355FFThat book was probably";
		mes "worthless to you anyway.^000000";
		close;
	}
	else {
		mes "^3355FFThere are old books";
		mes "scattered all over here.^000000";
		close;
	}
}

abbey03,232,233,4	script	Man#King	956,{
	if (aru_monas < 22) {
		mes "^3355FFYou find a man lying on";
		mes "the floor, wearing torn";
		mes "yet luxurious clothing.";
		mes "He doesn't seem to be";
		mes "breathing at all...^000000.";
		next;
		mes "^3355FFHe's dead.^000000";
		close;
	}
	if (aru_monas == 22 || aru_monas == 23) {
		mes "^3355FFYou find a man lying on";
		mes "the floor, wearing torn";
		mes "yet luxurious clothing.";
		mes "He doesn't seem to be";
		mes "breathing at all...^000000.";
		next;
		mes "^3355FFYou bring your ears more";
		mes "closely to his mouth: it";
		mes "seems that he really is";
		mes "still barely alive.^000000";
		next;
		if (select("Touch Him:Ignore Him") == 1) {
			mes "["+strcharinfo(0)+"]";
			mes "This man...";
			mes "He's so familiar";
			mes "for some reason.";
			next;
			mes "^3355FFAs you touch him, the";
			mes "man suddenly jumps";
			mes "up with a crazed yowl.^000000";
			set aru_monas,23;
			monster "abbey03",232,232,"Dead King",1875,1,"Man#King::OnMyMobDead";
			initnpctimer;
			hideonnpc "Man#King";
			close;
		}
		mes "^3355FFYou don't feel";
		mes "comfortable enough";
		mes "to touch this man.^000000";
		close;
	}
	else {
		mes "^3355FFHe's dead... Now...^000000";
		close;
	}

OnMyMobDead:
	stopnpctimer;
	donpcevent "Dead Man#King::OnEnable";
	end;

OnTimer300000:
	stopnpctimer;
	hideonnpc "Dead Man#King";
	hideoffnpc "Man#King";
	end;
}

abbey03,232,232,4	script	Dead Man#King	956,{
	if (checkweight(7766,1) == 0) {
		mes "^3355FFWait a second!";
		mes "Right now, you're carrying";
		mes "too many things with you.";
		mes "Please come back after";
		mes "using the Kafra Service";
		mes "to store some of your items.^000000";
		close;
	}
	if (aru_monas == 23) {
		mes "^3355FFYou have no idea why";
		mes "this dead man is moving";
		mes "around, so you decided to";
		mes "examine him. In his jacket,";
		mes "you find a shining medal...^000000";
		next;
		mes "["+strcharinfo(0)+"]";
		mes "This medal...";
		mes "This means that";
		mes "this man is...!";
		set aru_monas,24;
		getitem 7726,1; //Token_of_King
		//changequest 17015,17016;
		hideonnpc "Dead Man#King";
		hideoffnpc "Man#King";
		stopnpctimer;
		close;
	}
	end;

OnEnable:
	initnpctimer;
	hideoffnpc "Dead Man#King";
	end;

OnInit:
	hideonnpc "Dead Man#King";
	end;

OnTimer150000:
	hideonnpc "Dead Man#King";
	hideoffnpc "Man#King";
	stopnpctimer;
	end;
}

nameless_n,145,162,0	script	AideAmi#Aru	139,2,2,{
	end;
OnTouch_:
	if (aru_monas == 26) {
		mes "[" + strcharinfo(0) + "]";
		mes "There is a low wall here against the other wall, if I climb it I could reach the resting place of Tristan III...";
		next;
		switch(select("Stay here:Climb the wall")) {
		case 1:
			warp "nameless_n",158,169;
			end;
		case 2:
			warp "abbey01",51,15;
			end;
		}
	}
	else {
		warp "nameless_n",158,169;
		end;
	}
}

sec_in02,15,15,4	script	boss::boss_aru_monas	809,{
	callfunc "F_GM_NPC";
	mes "[Patch]";
	mes "Input.";
	next;
	switch(select("Now:How much?:P- How much?:Others")) {
	case 1:
		mes " ";
		mes ""+aru_monas;
		close;
	case 2:
		input .@input,0,1000;
		set aru_monas, .@input;
		mes " ";
		mes ""+aru_monas;
		close;
	case 3:
		input .@input,0,1000;
		set prt_curse, .@input;
		mes " ";
		mes ""+prt_curse;
		close;
	case 4:
		set prt_curse,61;
		set ra_tem_q,12; //TODO: Doesn't match Athena's current script.
		set rachel_camel,25;
		set lost_boy,0;
		close;
	}
}

// Rachel Addition :: aruna_nir
//============================================================
ra_temple,165,57,5	script	Niren#ss	915,{
	if (checkweight(907,200) == 0) {
		mes "^3355FFWait a second!";
		mes "Right now, you're carrying";
		mes "too many things with you.";
		mes "Please come back after";
		mes "using the Kafra Service";
		mes "to store some of your items.^000000";
		close;
	}
	cutin "ra_gwoman",2;
	mes "[High Priestess Niren]";
	mes "So you wanted to talk to me?";
	mes "I'm pretty tired right now, but";
	mes "I can spare a moment or two.";
	mes "What would you like to ask?";
	next;
	switch(select("Ask About Veins Smugglers:Ask About Zhed")) {
	case 1:
		if (aru_monas == 12) {
			mes "[Niren]";
			mes "Veins smugglers...";
			mes "Would you explain";
			mes "exactly what you mean";
			mes "by Veins smugglers?";
			next;
			mes "[Niren]";
			mes "Oh, them. Yes, I know";
			mes "a little bit about them.";
			mes "I'm afraid that I can't really";
			mes "disclose certain information";
			mes "about them since they're";
			mes "a classified case...";
			next;
			mes "[Niren]";
			mes "I'm afraid that I cannot give you information about them easily";
			mes "as they mean a lot to us.";
			next;
			mes "["+strcharinfo(0)+"]";
			mes "Actually, I was curious";
			mes "about who they kidnapped.";
			next;
			mes "[Niren]";
			mes "Oh, well, I suppose";
			mes "I can tell you more";
			mes "about their captive.";
			mes "But first, I'd like to";
			mes "ask you for a favor.";
			next;
			select("Accept:Do Not Refuse");
			mes "[Niren]";
			mes "When we realized the";
			mes "kind of hostage the";
			mes "smugglers captured, we";
			mes "didn't know what to do with";
			mes "him. We've had too many";
			mes "internal problems to handle...";
			next;
			mes "[Niren]";
			mes "Still, we felt that we";
			mes "could use him in our plan,";
			mes "so we held him in a secret";
			mes "place whose location is only";
			mes "known to a few people. But";
			mes "then... We lost all contact.";
			next;
			mes "[Niren]";
			mes "We sent investigators, but";
			mes "we still have no idea what";
			mes "happened. All they found was";
			mes "a message left by one survivor:";
			mes "''They're all demons.'' The";
			mes "hostage is probably dead.";
			next;
			mes "[Niren]";
			mes "He may have been a high";
			mes "ranking official, but we";
			mes "were too preoccupied";
			mes "with other matters. In fact,";
			mes "I haven't gotten the chance";
			mes "to think of what to do with him...";
			next;
			mes "[Niren]";
			mes "I want to ask you to";
			mes "go to that place, and";
			mes "figure out what happened";
			mes "there, and if that high ranking";
			mes "official from Rune-Midgarts is";
			mes "still alive. I doubt it, but...";
			next;
			mes "[Niren]";
			mes "The investigators we employ";
			mes "in our temple usually pale";
			mes "in comparison to you foreign";
			mes "adventurers, so I have faith";
			mes "that you can do it. Still,";
			mes "it'll be quite dangerous.";
			next;
			mes "[Niren]";
			mes "If you can do this, I'll";
			mes "tell you the location of";
			mes "that place: we usually call";
			mes "it the monastery. It used to";
			mes "be an actual monastery, but we";
			mes "use it to hold captives now.";
			next;
			mes "[Niren]";
			mes "I'm sorry, but I can't tell";
			mes "you anything else about that";
			mes "old place. It's located to the";
			mes "southwest of Veins across the";
			mes "sea. You should be able to find";
			mes "a boat at South Veins Beach.";
			next;
			mes "[Niren]";
			mes "However... Never mind.";
			mes "I'd like to help you more,";
			mes "but I'd be compromising my";
			mes "position. I'm sorry. Please";
			mes "come back and tell me if";
			mes "you learn anything there.";
			set aru_monas,13;
			//changequest 17008,17009;
			next;
			mes "[Niren]";
			mes "I'll send a message to the";
			mes "magistrate of Veins. If you";
			mes "talk to him, he'll provide you";
			mes "with some useful information";
			mes "for your journey. Good luck.";
			close2;
		}
		else if (aru_monas < 18) {
			mes "[Niren]";
			mes "Going to the monastery";
			mes "isn't the problem: coming";
			mes "back here alive will probably";
			mes "be the real challenge for you.";
			next;
			mes "[Niren]";
			mes "We can't send any troops";
			mes "there until we understand";
			mes "what they'd be confronting";
			mes "there. That's why we need";
			mes "you to investigate the area.";
			close2;
		}
		else if (aru_monas < 25) {
			mes "[Niren]";
			mes "Still investigating the";
			mes "monastery? Perhaps if";
			mes "you found any records";
			mes "left behind by one of";
			mes "the residents over there...";
			close2;
		}
		else if (aru_monas == 25) {
			if (countitem(7755) < 1) {
				mes "[Niren]";
				mes "Still investigating the";
				mes "monastery? Perhaps if";
				mes "you found any records";
				mes "left behind by one of";
				mes "the residents over there...";
				close2;
			}
			else {
				mes "[Niren]";
				mes "You found this journal";
				mes "in the monastery? Perfect.";
				mes "Let me read it, and see";
				mes "what we can learn...";
				next;
				mes "[Niren]";
				mes "Hmm, this sounds really";
				mes "bad. This is tragic, especially";
				mes "we kept so many important";
				mes "people over there. I'll request";
				mes "to dispatch troops there right";
				mes "away. Something must be done.";
				next;
				mes "[Niren]";
				mes "There are too many problems";
				mes "we have to confront now, but";
				mes "this monastery issue needs";
				mes "top priority. We won't be able";
				mes "to handle it if another threat";
				mes "comes from that place.";
				delitem 7755,1; //Research_Note
				set aru_monas,26;
				//completequest 17017;
				getexp (checkre(3))?50000:500000,0;
				close2;
			}
		}
		else {
			mes "[Niren]";
			mes "I suppose that Arunafeltz";
			mes "and Rune-Midgarts will be too";
			mes "busy with their own internal";
			mes "issues to commit to war.";
			mes "High Priest Zhed will be";
			mes "glad to know about this.";
			close2;
		}
		break;
	case 2:
		if (aru_em < 10) {
			mes "[High Priestess Niren]";
			mes "I'm sorry...";
			mes "I really don't want";
			mes "to think about Zhed";
			mes "right now. He's just...";
			close2;
		}
		else if (aru_em == 10) {
			mes "[High Priestess Niren]";
			mes "You wanted to ask me about";
			mes "Beken--High Priest Zhed?";
			mes "I can't do anything for him.";
			mes "He dug his own grave by";
			mes "allowing you into the";
			mes "holy ground, didn't he?";
			next;
			mes "[High Priestess Niren]";
			mes "I thought I warned you";
			mes "before to stay out of trouble.";
			next;
			mes "["+strcharinfo(0)+"]";
			mes "I'm not here to discuss";
			mes "that. But I happened to";
			mes "hear that you're the only";
			mes "person that can help him now.";
			next;
			mes "[High Priestess Niren]";
			mes "...............................";
			mes "...............................";
			mes "...............................";
			next;
			mes "[High Priestess Niren]";
			mes "It's true that Zhed and";
			mes "I used to work well together,";
			mes "but we no longer share the";
			mes "same dream. You'd better find";
			mes "someone else. Unlike him,";
			mes "I actually want a war.";
			next;
			mes "["+strcharinfo(0)+"]";
			mes "Weren't you guys friends?";
			mes "Besides, you don't seem as";
			mes "aggressive as those other";
			mes "hard liner priests.";
			next;
			mes "[High Priestess Niren]";
			mes "Aggressive? Hmpf. If you";
			mes "take a good look at those";
			mes "moderates, you'll see that";
			mes "they oppose war, but they're";
			mes "also totally corrupt. In fact, they committed worse atrocities!";
			next;
			mes "[High Priestess Niren]";
			mes "The moderates conducted";
			mes "inhumane testing on living";
			mes "creatures, even humans!";
			mes "And Zhed and I... Could do";
			mes "nothing but pray to Freya.";
			mes "Only war can this this chaos!";
			next;
			mes "[High Priestess Niren]";
			mes "We need to start all";
			mes "over again. I can't help";
			mes "Zhed. Please leave...";
			next;
			mes "^3355FFIt doesn't look like";
			mes "you can change High";
			mes "Priestess Niren's mind.";
			mes "For now, you'd better talk";
			mes "to High Priest Zhed.^000000";
			set aru_em,11;
			changequest 2132,2133;
			close2;
		}
		else if ((aru_em == 11) || (aru_em == 12)) {
			mes "[High Priestess Niren]";
			mes "I already told you";
			mes "how I feel about Zhed.";
			mes "We were wrong all along...";
			mes "This time, war is the answer.";
			close2;
		}
		else if (aru_em == 13) {
			mes "[High Priestess Niren]";
			mes "I already told you";
			mes "how I feel about Zhed.";
			mes "We were wrong all along...";
			mes "This time, war is the answer.";
			next;
			mes "["+strcharinfo(0)+"]";
			mes "Fine. If you're so sure";
			mes "of yourself, then you won't";
			mes "mind reading this letter";
			mes "from High Priest Zhed.";
			next;
			mes "^3355FFNiren angrily grabbed the";
			mes "letter, ripped the envelope";
			mes "open, and began to read his";
			mes "message. Her stern face slowly";
			mes "softened as she slowly scanned";
			mes "what Zhed had to tell her.^000000";
			next;
			mes "[High Priestess Niren]";
			mes ".................";
			mes ".................";
			mes ".................";
			next;
			mes "[High Priestess Niren]";
			mes "That Zhed... He always";
			mes "did have a way with words.";
			mes "I suppose the old saying is";
			mes "true: the pen truly is mightier";
			mes "than the sword. *Sigh...*";
			next;
			mes "[Sippie]";
			mes "High Priestess";
			mes "Niren? Are you okay?";
			next;
			mes "[High Priestess Niren]";
			mes "Oh, I'm fine,";
			mes "thank you. I guess";
			mes "I'm just a little tired.";
			next;
			mes "[High Priestess Niren]";
			mes "...............................";
			mes "I have to admit, there's truth";
			mes "in what Zhed's saying. We";
			mes "have a duty to protect our";
			mes "people, the pope, our holy";
			mes "ground. But I need to think...";
			next;
			mes "[High Priestess Niren]";
			mes "Would you give me some";
			mes "time to myself? I'll let you";
			mes "know once I've made a decision.";
			set aru_em,14;
			close2;
		}
		else if (aru_em == 14) {
			if (rand(1,10) == 1) {
				mes "[High Priestess Niren]";
				mes "...................";
				mes "...................";
				mes "...................";
				emotion ET_THINK;
				next;
				mes "[Sippie]";
				mes "High Priestess Niren,";
				mes "you must be really tired.";
				mes "You look pale for some reason.";
				emotion ET_PROFUSELY_SWEAT;
				next;
				mes "[High Priestess Niren]";
				mes "I do feel rather drained.";
				mes "I don't know why I've been";
				mes "holding on to this hatred for";
				mes "so long. I guess I can finally";
				mes "just cast it to the winds...";
				next;
				mes "[High Priestess Niren]";
				mes "Adventurer...";
				mes "What was your name?";
				next;
				mes "["+strcharinfo(0)+"]";
				mes "My name is "+strcharinfo(0)+".";
				next;
				mes "[High Priestess Niren]";
				mes "Adventurer...";
				mes "You had the chance to talk";
				mes "with our pope once, but I doubt";
				mes "you'll be able to do so again";
				mes "^3311FFin private^000000. You'll definitely";
				mes "need our help for that end.";
				next;
				mes "["+strcharinfo(0)+"]";
				mes "Does this mean that";
				mes "you've decided to work";
				mes "with Zhed again?";
				next;
				mes "^3355FFNiren quietly nodded";
				mes "her head, and then";
				mes "resumed talking.^000000";
				next;
				mes "[High Priestess Niren]";
				mes "I'm still not sure if";
				mes "I can join him in the long";
				mes "run. I mean, talking to the";
				mes "pope might not result in";
				mes "anything, actually. But";
				mes "it's still worth a try.";
				next;
				mes "[High Priestess Niren]";
				mes "However, I've never";
				mes "doubted Zhed's dedication";
				mes "to Arunafeltz. He deserves";
				mes "another chance from me.";
				next;
				mes "[High Priestess Niren]";
				mes "Our pope is always surrounded ";
				mes "by priests and followers sent";
				mes "by High Priest Vildt. He always";
				mes "manages to keep his eyes and";
				mes "ears on her. We need to get";
				mes "those spies away somehow...";
				next;
				mes "["+strcharinfo(0)+"]";
				mes "What can we do";
				mes "about them, then?";
				next;
				mes "[High Priestess Niren]";
				mes "For now, just approach";
				mes "the pope as if you just";
				mes "wanted to leisurely chat.";
				mes "While you're doing that,";
				mes "try to gather information";
				mes "from the priests around her.";
				next;
				mes "["+strcharinfo(0)+"]";
				mes "Sure thing. You";
				mes "can count on me!";
				emotion ET_BEST, playerattached();
				set aru_em,15;
				changequest 2134,2135;
				close2;
			}
			else {
				mes "[High Priestess Niren]";
				mes "Ugh, this is giving me";
				mes "a headache. There's";
				mes "too much to consider...";
				mes "Too much at stake here...";
				close2;
			}
		}
		else if (aru_em == 15) {
			mes "[High Priestess Niren]";
			mes "Just approach the pope";
			mes "for a chat, and see what";
			mes "you can learn from the";
			mes "other priests around her.";
			close2;
		}
		else if (aru_em == 16) {
			mes "[High Priestess Niren]";
			mes "How is the pope?";
			mes "I haven't had the chance";
			mes "to meet her recently, so...";
			next;
			mes "["+strcharinfo(0)+"]";
			mes "Well, she doesn't seem";
			mes "very well, just like the";
			mes "last time I saw her. I didn't";
			mes "learn anything about all those";
			mes "priests. Just that they've been";
			mes "working in the temple non-stop.";
			next;
			mes "[High Priestess Niren]";
			mes "Yes, they've been by her";
			mes "side twenty-fours a day.";
			mes "It's been like that for years.";
			next;
			emotion ET_KEK;
			mes "[Sippie]";
			mes "Oh, High Priestess Niren,";
			mes "I love working for you, but";
			mes "sometimes I wish I could leave";
			mes "just to see my family. Even";
			mes "just once a year would be nice.";
			next;
			emotion ET_AHA;
			mes "[High Priestess Niren]";
			mes "...............................";
			mes "You're absolutely right.";
			mes "That gives me an idea.";
			next;
			emotion ET_DELIGHT;
			mes "[Sippie]";
			mes "Huh? What is it?";
			next;
			mes "[High Priestess Niren]";
			mes "You all need a vacation.";
			next;
			mes "[High Priestess Niren]";
			mes ""+strcharinfo(0)+".";
			next;
			mes "["+strcharinfo(0)+"]";
			mes "Yes?";
			next;
			mes "[High Priestess Niren]";
			mes "I should reward everyone";
			mes "in the Sky Garden for their";
			mes "contribution. However,";
			mes "they were hired by Vildt,";
			mes "so I can't directly give them";
			mes "leave for vacation. Hmm...";
			next;
			mes "[High Priestess Niren]";
			mes "Sippie...";
			mes "I'll need a pen";
			mes "and some paper.";
			next;
			mes "[Sippie]";
			mes "Yes, ma'am.";
			mes "There you go!";
			cutin "ra_gwoman",255;
			next;
			mes "^3355FF*Scribble Scribble*^000000";
			mes "^3355FF*Scribble Scribble*^000000";
			mes "^3355FF*Scribble Scribble*^000000";
			mes "^3355FF*Scribble Scribble*^000000";
			next;
			cutin "ra_gwoman",2;
			mes "[High Priestess Niren]";
			mes "I administered a blessing";
			mes "for a child from a native";
			mes "family, and they were very";
			mes "grateful since most of the";
			mes "priests shun the natives.";
			next;
			mes "[High Priestess Niren]";
			mes "It turns out that this";
			mes "child is gifted at forgery.";
			mes "She doesn't like being too";
			mes "close to other people, but";
			mes "that's beside the point.";
			mes "Here, take this letter.";
			next;
			mes "[High Priestess Niren]";
			mes "Ah, and take this file that";
			mes "contains a sample of Vildt's";
			mes "handwriting. Bring them to";
			mes "a girl named Ishmael in a";
			mes "village in North Rachel.";
			mes "I hope she'll help us...";
			set aru_em,17;
			getitem 7343,1; //File02
			changequest 2136,2137;
			close2;
		}
		else if (aru_em == 17) {
			mes "[High Priestess Niren]";
			mes "Please deliver my letter";
			mes "and that file to Ishmael in";
			mes "North Rachel. Hopefully,";
			mes "she can use her talents";
			mes "to help us out.";
			close2;
		}
		else if (aru_em == 19) {
			if (countitem(7343) > 0) {
				mes "[High Priestess Niren]";
				mes "Have you met with Ishmael?";
				next;
				mes "^3355FFYou handed Niren the";
				mes "file forged by Ishmael.^000000";
				next;
				emotion ET_STARE;
				mes "[High Priestess Niren]";
				mes "Perfect! This forgery is";
				mes "so well done, I'm sure";
				mes "that even Vildt won't be";
				mes "know if he wrote it or not.";
				next;
				mes "[High Priestess Niren]";
				mes ""+strcharinfo(0)+"...";
				mes "I'm going to use this to";
				mes "send the priests and followers";
				mes "on vacation. Then me and Zhed";
				mes "can talk to the pope without";
				mes "any unecessary interruptions.";
				next;
				mes "[High Priestess Niren]";
				mes "I really don't want to burden";
				mes "our pope with what Zhed and I";
				mes "have to say... But that's much";
				mes "preferable to doing nothing";
				mes "and experiencing the downfall";
				mes "of our beloved Arunafeltz.";
				next;
				mes "[High Priestess Niren]";
				mes "Sippie, please";
				mes "deliver this vacation";
				mes "approval to the priests and";
				mes "followers in the Sky Garden.";
				next;
				mes "[Sippie]";
				mes "Yes, ma'am!";
				next;
				mes "[High Priestess Niren]";
				mes ""+strcharinfo(0)+",";
				mes "please go back to Zhed,";
				mes "and let him know everything";
				mes "is ready. Then I want you to";
				mes "meet us in the ^3131FFSky Garden^000000.";
				next;
				mes "["+strcharinfo(0)+"]";
				mes "Yes!";
				next;
				mes "[High Priestess Niren]";
				mes "We need to hurry and";
				mes "do this before High";
				mes "Priest Vildt realizes";
				mes "what we are doing.";
				delitem 7343,1; //File02
				set aru_em,20;
				changequest 2139,2140;
				close2;
			}
			else {
				mes "^3131FFYou seem to have";
				mes "misplaced Ishmael's file.^000000";
				close2;
			}
		}
		else if ((aru_em == 20) || (aru_em == 21)) {
			mes "[High Priestess Niren]";
			mes "Please hurry back";
			mes "to High Priest Zhed.";
			close2;
		}
		else if (aru_em == 22) {
			mes "[High Priestess Niren]";
			mes "I'm so proud of our pope!";
			mes "Did you hear her wonderful";
			mes "speech? Well, I should say";
			mes "those are the words of Freya,";
			mes "but... Well, you know. Hohoho!";
			next;
			mes "[High Priestess Niren]";
			mes "Our priests may have acted";
			mes "selfishly, but they are still";
			mes "committed to Arunafeltz's";
			mes "welfare. Things should";
			mes "change for the better now.";
			next;
			mes "[High Priestess Niren]";
			mes "I'm ashamed that I strayed";
			mes "from the path of peace and";
			mes "almost endangered my country.";
			mes "I'm glad Zhed brought me back";
			mes "my senses. From now on, I'll";
			mes "repent for my sins with Zhed.";
			next;
			mes "[High Priestess Niren]";
			mes "I hope the goddess can";
			mes "forgive me. Ah, and you";
			mes "should know that Zhed is";
			mes "waiting for you. Why don't";
			mes "you go see him now?";
			next;
			mes "^3355FFNiren softly smiles";
			mes "at you, and you realize";
			mes "that it's the first time";
			mes "that you've seen that";
			mes "expression on her face.^000000";
			set aru_em,23;
			getexp (checkre(3))?100000:1000000,0;
			changequest 2141,2142;
			close2;
		}
		else if (aru_em > 21) {
			mes "[High Priestess Niren]";
			mes "Thanks to you and";
			mes "Bekento, I think I've";
			mes "finally found my way.";
			close2;
		}
		else {
			mes "[High Priestess Niren]";
			mes "I'm sorry...";
			mes "I really don't want";
			mes "to think about Zhed";
			mes "right now. He's just...";
			close2;
		}
	}
	cutin "ra_gwoman",255;
	end;
}

ra_temple,168,54,3	script	Sippie#ss	917,{
	mes "[Sippie]";
	mes "Now I finally have some";
	mes "time to take a break.";
	mes "Too many followers are";
	mes "crowding High Priestess";
	mes "Niren, and they're giving";
	mes "me so much work to do!";
	if (aru_em < 11) {
		close;
	}
	else if (aru_em == 11) {
		next;
		mes "[Sippie]";
		mes "Excuse me, but are you";
		mes "a friend of our pope?";
		mes "I think I saw you talk";
		mes "to her a while ago...";
		next;
		mes "[Sippie]";
		mes "I don't understand why";
		mes "High Priestess Niren wants";
		mes "to go to war. I actually think";
		mes "that in her heart, she doesn't";
		mes "want it. She's sweet and kind";
		mes "once you get to know her.";
		next;
		mes "[Sippie]";
		mes "She loves children...";
		mes "She's like a mother to";
		mes "all the kids in Arunafeltz.";
		mes "She even brought our pope to";
		mes "Rachel when she was a baby,";
		mes "raising her like her own child.";
		next;
		mes "[Sippie]";
		mes "If you didn't know she";
		mes "was our pope, it'd be easy";
		mes "to mistake her for High";
		mes "Priestess Niren's daughter.";
		close;
	}
	else {
		close;
	}
}

// Peace to the Arunafeltz :: aruna_em
//============================================================
moc_ruins,77,167,4	script	Foreign Merchant#aru1	880,5,5,{
	if (aru_vol == 27) {
		if (aru_em == 0) {
			emotion ET_THINK;
			mes "[Foreign Merchant]";
			mes "I never thought the desert";
			mes "would be this hot! They built";
			mes "a town here?! How can they call";
			mes "this place inhabitable! What's";
			mes "more, the crowds make things";
			mes "around here so much hotter!";
			next;
			emotion ET_THINK;
			mes "[Foreign Merchant]";
			mes "I heard that I could make";
			mes "a killing in Morocc, which is";
			mes "why I moved my business here.";
			mes "But it looks like the weather";
			mes "is going to kill me first!";
			next;
			select("How did you hear of Morocc?");
			emotion ET_THINK;
			mes "[Foreign Merchant]";
			mes "I... I just told you.";
			mes "I heard that Morocc is";
			mes "full customers willing to";
			mes "buy things at premium prices.";
			mes "Ugh, but it's a burning hell!";
			next;
			emotion ET_SWEAT;
			mes "[Foreign Merchant]";
			mes "I don't even care about making";
			mes "money anymore! I just want to";
			mes "be somewhere cool, drinking";
			mes "a nice, frosty drink! You know,";
			mes "I might just leave this town";
			mes "tomorrow. Forget this desert!";
			next;
			emotion ET_CRY;
			emotion ET_SWEAT, playerattached();
			mes "[Foreign Merchant]";
			mes "Ah, that's right. Don't they";
			mes "sell some sweet, heavenly";
			mes "drink around here? You know,";
			mes "near the pond in the middle";
			mes "of Morocc? Before I leave,";
			mes "I want to taste that drink...";
			set aru_em,1;
			setquest 2129;
			close;
		}
		else if ((aru_em > 0) && (aru_em < 7)) {
			mes "[Foreign Merchant]";
			mes "Say, isn't there a wandering";
			mes "drink merchant near the pond";
			mes "in the middle of Morocc. Oh,";
			mes "what I would do to have that";
			mes "delicious drink he's selling...";
			next;
			mes "[Foreign Merchant]";
			mes "Oh, the heat's getting to me...";
			mes "And I'm running out of sweat";
			mes "to sweat... I think I'm going";
			mes "to faint soon! I need a drink!";
			close;
		}
		else if (aru_em == 7) {
			mes "[Foreign Merchant]";
			mes "*Pant Pant*";
			mes "*Sweat*";
			next;
			switch(select("Give Fruit Wine:Quietly Watch Him")) {
			case 1:
				mes "[Foreign Merchant]";
				mes "Oh, thank you! This";
				mes "smells so heavenly...";
				mes "Is this that famous drink";
				mes "that I keep hearing about?";
				next;
				mes "^3355FF*Gulp Gulp Gulp Gulp*^000000";
				mes "^3355FF*Gulp Gulp Gulp Gulp*^000000";
				mes "^3355FF*Gulp Gulp Gulp Gulp*^000000";
				mes "^3355FF*Gulp Gulp Gulp Gulp*^000000";
				mes "^3355FF*Gulp Gulp Gulp Gulp*^000000";
				next;
				emotion ET_THROB;
				mes "[Foreign Merchant]";
				mes "Ahhh! That hit the spot!";
				mes "It's so refreshing and so";
				mes "exotic, like... Like... Those";
				mes "dancers in Comodo! Ahhhh!";
				mes "Thank you so much, friend!";
				next;
				mes "[Foreign Merchant]";
				mes "There must be some";
				mes "way I can repay you.";
				mes "Ah, here we go. Take it.";
				mes "Isn't your name, "+ strcharinfo(0) +"?";
				next;
				mes "["+ strcharinfo(0) +"]";
				mes "What th--?";
				mes "How did you know?";
				next;
				mes "^3355FFThe merchant giggled,";
				mes "note from under his sleeve.";
				mes "You open the note, and";
				mes "read its message.^000000";
				next;
				mes "^666666"+ strcharinfo(0) +".";
				mes " ";
				mes "The time for us to act";
				mes "has come once again.";
				mes "Please come see me";
				mes "once you get the chance.";
				mes " ";
				mes " - Bekento^000000";
				next;
				emotion ET_HUK, playerattached();
				mes "["+ strcharinfo(0) +"]";
				mes "So... This means...";
				mes "You're not really a";
				mes "merchant, are you?";
				next;
				emotion ET_MERONG;
				mes "^3355FFThe merchant quietly";
				mes "looks at you with a";
				mes "mischievous grin.^000000";
				next;
				mes "["+ strcharinfo(0) +"]";
				mes "So you asked me to get";
				mes "you a drink as an excuse";
				mes "just to talk to you?!";
				emotion ET_CRY, playerattached();
				next;
				mes "["+ strcharinfo(0) +"]";
				mes "Oh well, thanks for";
				mes "bringing me the note.";
				mes "But I'm not buying you";
				mes "any more drinks, got it?";
				next;
				mes "[Foreign Merchant]";
				mes "Heh, understood.";
				mes "Now why don't you";
				mes "speak to him now?";
				mes "Good luck!";
				set aru_em,8;
				changequest 2130,2131;
				close;
			case 2:
				mes "[Foreign Merchant]";
				mes "Wha-- Do you just";
				mes "enjoy watching me";
				mes "slowly sweat to death?";
				close;
			}
		}
		else {
			mes "[Foreign Merchant]";
			mes "Well, I'm already here.";
			mes "I guess I may as well go";
			mes "sightseeing around Morocc.";
			mes "*Pant Pant* But it's so hot!";
			close;
		}
	}
	else {
		mes "[Foreign Merchant]";
		mes "I never thought the desert";
		mes "would be this hot! They built";
		mes "a town here?! How can they call";
		mes "this place inhabitable! What's";
		mes "more, the crowds make things";
		mes "around here so much hotter!";
		close;
	}

OnTouch:
	if (aru_vol == 27) {
		if (aru_em == 0) {
			mes "[Foreign Merchant]";
			mes "*Pant Pant*";
			mes "Man, I think I'm going";
			mes "to die from all this heat!";
			close;
		}
	}
	end;
}

moc_ruins,86,149,0	script	#ForeignMerchant1	-1,3,3,{
OnTouch:
	if (aru_em == 4) {
		set aru_em,5;
	}
	end;
}

moc_ruins,88,136,0	script	#ForeignMerchant2	-1,3,3,{
OnTouch:
	if (aru_em == 1) {
		set aru_em,2;
	}
	else if (aru_em == 5) {
		set .@nawara,rand(1,10);
		if (.@nawara < 5) {
			hideoffnpc "Foreign Merchant#aru2";
		}
		else {
			set aru_em,2;
		}
	}
	end;
}

moc_ruins,106,133,0	script	#ForeignMerchant3	-1,3,3,{
OnTouch:
	if (aru_em == 2) {
		set aru_em,3;
	}
	end;
}

moc_ruins,115,147,0	script	#ForeignMerchant4	-1,3,3,{
OnTouch:
	if (aru_em == 3) {
		set aru_em,4;
	}
	end;
}

moc_ruins,101,133,7	script	Foreign Merchant#aru2	929,{
	if ((aru_em > 1) && (aru_em < 7)) {
		mes "["+ strcharinfo(0) +"]";
		mes "Excuse me, but I heard";
		mes "that I could buy some";
		mes "good drinks around here.";
		mes "Would you happen to be";
		mes "the one selling them?";
		next;
		mes "[Foreign Merchant]";
		mes "Oh, you mean my fruit wine?";
		mes "I'm afraid it's not as good";
		mes "as people chalk it up to be.";
		mes "It's just a little something";
		mes "I brew at home, and share";
		mes "with my friends after dinner.";
		next;
		mes "["+ strcharinfo(0) +"]";
		mes "Well, I ran into this";
		mes "merchant that really wants";
		mes "to have a taste of that wine.";
		mes "Do you think that you'd be";
		mes "able to sell me some?";
		next;
		mes "[Foreign Merchant]";
		mes "Oh, I'm sorry, but I'm";
		mes "all sold out for today.";
		next;
		mes "["+ strcharinfo(0) +"]";
		mes "Oh, no...";
		emotion ET_OTL, playerattached();
		next;
		mes "[Foreign Merchant]";
		mes "...............................";
		mes "...............................";
		mes "...............................";
		emotion ET_PROFUSELY_SWEAT;
		next;
		emotion ET_HUK, playerattached();
		mes "[Foreign Merchant]";
		mes "You know, I save a small";
		mes "bottle of wine for myself";
		mes "so I'll just give that to you.";
		mes "I can always brew more, and";
		mes "I'm flattered that your friend";
		mes "is so interested in my wine.";
		next;
		mes "["+ strcharinfo(0) +"]";
		mes "Really? Thank you!";
		mes "I'm sure that he's really";
		mes "appreciate your generosity.";
		next;
		mes "[Foreign Merchant]";
		mes "Oh, don't mention it.";
		mes "Well, I should head back";
		mes "home now. I'll be sure to";
		mes "keep an extra bottle in case";
		mes "you want more next time. Take";
		mes "care, and I'll see you later!";
		emotion ET_FLAG;
		next;
		mes "^3355FFYou received a bottle";
		mes "of the famous fruit wine";
		mes "from the old man. That";
		mes "guy's pretty nice!^000000";
		set aru_em,7;
		hideonnpc "Foreign Merchant#aru2";
		changequest 2129,2130;
		close;
	}
	else {
		mes "[Foreign Merchant]";
		mes "I'm sorry, but I'm all";
		mes "sold out of fruit wine.";
		mes "I hope that's not too";
		mes "much of an inconvenience.";
		close;
	}

OnInit:
	hideonnpc "Foreign Merchant#aru2";
	end;
}

ra_temin,103,151,7	script	Female Follower#em	917,{
	if (aru_em < 9) {
		mes "[Sappie]";
		mes "I work really hard to keep";
		mes "this room clean and cozy so";
		mes "that the High Priestess can";
		mes "rest and refresh herself in";
		mes "comfort. Take a deep breath:";
		mes "smell that relaxing aroma?";
		close;
	}
	else if (aru_em == 9) {
		mes "[Sappie]";
		mes "I work really hard to keep";
		mes "this room clean and cozy so";
		mes "that the High Priestess can";
		mes "rest and refresh herself in";
		mes "comfort. Take a deep breath:";
		mes "smell that relaxing aroma?";
		next;
		mes "["+ strcharinfo(0) +"]";
		mes "Oh, you're right, that";
		mes "is a nice smell! So, is";
		mes "Priestess Niren away";
		mes "from her office today?";
		next;
		mes "[Sappie]";
		mes "Oh, yes, she's out since";
		mes "there are many followers";
		mes "that wish to meet her,";
		mes "even early in the morning.";
		next;
		mes "["+ strcharinfo(0) +"]";
		mes "Do you know where";
		mes "I could find her?";
		next;
		mes "[Sappie]";
		mes "Hm... Sippie mentioned";
		mes "something... Ah, right. Sippie";
		mes "said that High Priestess Niren";
		mes "decided to go to Cheshrumnir";
		mes "Garden. That garden is very";
		mes "large, and quite beautiful.";
		next;
		mes "["+ strcharinfo(0) +"]";
		mes "The High Priestess";
		mes "must be very busy.";
		next;
		mes "[Sappie]";
		mes "Oh, you can't even imagine!";
		mes "She's been passing her wisdom";
		mes "to the people ever since she";
		mes "was a baby, and crowds of";
		mes "people still clamor for";
		mes "her sage teachings.";
		next;
		mes "[Sappie]";
		mes "If you really want";
		mes "to talk to her, it'd";
		mes "be best if you catch her";
		mes "before she's surrounded";
		mes "by Freya's followers.";
		set aru_em,10;
		close;
	}
	else if (aru_em == 10) {
		mes "[Sappie]";
		mes "High Priestess Niren must";
		mes "be in Cheshrumnir Garden.";
		mes "You should try to talk to her";
		mes "before the crowds show up.";
		close;
	}
	else if (aru_em > 21) {
		mes "[Sappie]";
		mes "It's rumored that the";
		mes "Goddess Freya spoke to";
		mes "our pope, giving her an";
		mes "important message. Now";
		mes "everyone wants to hear";
		mes "what Freya told her...";
		close;
	}
	else {
		mes "[Sappie]";
		mes "I work really hard to keep";
		mes "this room clean and cozy so";
		mes "that the High Priestess can";
		mes "rest and refresh herself in";
		mes "comfort. Take a deep breath:";
		mes "smell that relaxing aroma?";
		close;
	}
}

ra_fild03,139,355,5	script	Ishmael#em	914,{
	if (checkweight(907,200) == 0) {
		mes "^3355FFWait a second!";
		mes "Right now, you're carrying";
		mes "too many things with you.";
		mes "Please come back after";
		mes "using the Kafra Service";
		mes "to store some of your items.^000000";
		close;
	}
	if (aru_em < 17) {
		mes "[Ishmael]";
		mes "I-I don't know you, do I?";
		mes "Sorry, but would you, um,";
		mes "just step away? I... I don't";
		mes "like being too close to";
		mes "other people. Please!";
		emotion ET_PROFUSELY_SWEAT;
		close;
	}
	else if (aru_em == 17) {
		mes "[Ishmael]";
		mes "I-I don't know you, do I?";
		mes "Sorry, but would you, um,";
		mes "just step away? Wait, you";
		mes "don't have something you";
		mes "need from me, do you?";
		emotion ET_PROFUSELY_SWEAT;
		next;
		switch(select("Give Her Niren's File:Leave Her Alone")) {
		case 1:
			if (countitem(7343) > 0) {
				mes "["+ strcharinfo(0) +"]";
				mes "You know High Priestess";
				mes "Niren, right? This is from her.";
				next;
				mes "[Ishmael]";
				mes "Oh, really? Okay, just...";
				mes "Just toss the file over";
				mes "to me. I-I don't want";
				mes "you coming any closer.";
				emotion ET_PROFUSELY_SWEAT;
				next;
				mes "[Ishmael]";
				mes "I'll be glad to help out";
				mes "the priestess. Even though";
				mes "we're natives, she's never";
				mes "discriminated against us.";
				mes "Hmm... What does she want?";
				next;
				mes "[Ishmael]";
				mes "Let's see... She wants";
				mes "me to forge a copy of this";
				mes "file? Piece of cake, I'm an";
				mes "expert at forging writing!";
				mes "Just give me a moment and--";
				next;
				emotion ET_HUK;
				mes "[Ishmael]";
				mes "Dang it! I completely forgot!";
				mes "Someone stole my pen yesterday!";
				mes "Now how am I gonna do this...?";
				next;
				emotion ET_CRY;
				mes "[Ishmael]";
				mes "My pen was made of a very";
				mes "rare gemstone called ^FF0000Sardonyx^000000.";
				mes "*Sob* I don't think anyone";
				mes "sells Sardonyx in Arunafeltz.";
				mes "You can only get that in the";
				mes "countries next to us...";
				next;
				mes "[Ishmael]";
				mes "I really want to help";
				mes "High Priestess Niren...";
				mes "I can't let her down after";
				mes "she's been so good to us...";
				set aru_em,18;
				delitem 7343,1; //File02
				changequest 2137,2138;
				close;
			}
			else {
				mes "^3355FFYou seem to have";
				mes "misplaced Niren's file...^000000";
				close;
			}
		case 2:
			mes "[Ishmael]";
			mes "I... I don't see you";
			mes "backing away. Stay back,";
			mes "and don't you dare touch me!";
			close;
		}
	}
	else if (aru_em == 18) {
		if (countitem(725) > 0) {
			mes "["+ strcharinfo(0) +"]";
			mes "Would you be able to forge";
			mes "a copy of High Priestess";
			mes "Niren's file if you had";
			mes "this gemstone?";
			next;
			mes "[Ishmael]";
			mes "Oh, you found";
			mes "a Sardonyx for me?";
			mes "It looks perfect!";
			next;
			mes "[Ishmael]";
			mes "Er, but, um...";
			mes "Would you please";
			mes "not come any closer?";
			mes "I still... Uh... I don't mean";
			mes "to be rude... It's just...";
			mes "I'm just not good with...";
			emotion ET_PROFUSELY_SWEAT;
			next;
			mes "[Ishmael]";
			mes "If you just toss the";
			mes "Sardonyx over here,";
			mes "I'll be able to forge";
			mes "your copy right away.";
			next;
			switch(select("Don't Give It to Her:Give It to Her")) {
			case 1:
				mes "[Ishmael]";
				mes "Huh? I thought you";
				mes "needed my help? I can't";
				mes "do anything unless you";
				mes "give me that gemstone.";
				close;
			case 2:
				mes "[Ishmael]";
				mes "Yay, thank you so much!";
				mes "Now I can get to work~";
				next;
				mes "^3355FF*Scribble*";
				mes "*Scribble*^000000";
				specialeffect EF_COMBOATTACK2;
				next;
				mes "^3355FF*Scribble*";
				mes "*Scribble*^000000";
				specialeffect EF_COMBOATTACK4;
				next;
				mes "^3355FF*Scribble*";
				mes "*Scribble*^000000";
				mes "^3355FF*Scribble*";
				mes "*Scribble*^000000";
				specialeffect EF_COMBOATTACK4;
				specialeffect EF_STEAL;
				emotion ET_AHA;
				next;
				mes "[Ishmael]";
				mes "There you go! I made the";
				mes "best quality copy I could for";
				mes "you since High Priestess";
				mes "Niren requested for it.";
				mes "Would you please send";
				mes "her my regards? Heh heh~";
				next;
				mes "^3355FFYou received a file";
				mes "containing a forged";
				mes "written approval for";
				mes "vacation from Ishmael.^000000";
				delitem 725,1; //Red_Jewel
				set aru_em,19;
				getitem 7343,1; //File02
				changequest 2138,2139;
				close;
			}
		}
		else {
			emotion ET_CRY;
			mes "[Ishmael]";
			mes "If only I only had";
			mes "my precious pen made";
			mes "of Sardonyx! Waaaah~!";
			close;
		}
	}
	else {
		mes "[Ishmael]";
		mes "High Priestess Niren is";
		mes "such a nice lady. I have to";
		mes "help her out whenever I get";
		mes "the chance. Er, do you mind";
		mes "stepping back a bit? I just...";
		mes "I don't like being near people!";
		close;
	}
}

que_temsky,101,93,1	script	Niren#em_sky	915,{
	end;
}
que_temsky,98,93,7	script	Zhed#em_sky	932,{
	end;
}
que_temsky,99,100,6	script	Pope#rachel2	936,{
	end;
}

que_temsky,100,93,0	script	#em_sky_s	-1,5,2,{
OnTouch:
	if (aru_em == 21) {
		mes "^3355FFHigh Priest Zhed and High";
		mes "Priestess Niren have already";
		mes "arrived, and are carefully";
		mes "watching the pope's face.";
		mes "They seem worried about";
		mes "what will happen...^000000";
		next;
		cutin "ra_gman",0;
		mes "[Zhed]";
		mes "How have you been, Your";
		mes "Eminence? I apologize for";
		mes "not visiting you sooner, but";
		mes "I've been too ashamed about";
		mes "my competency as a high priest.";
		next;
		cutin "ra_bishop",2;
		mes "[Pope]";
		mes "High Priest Zhed...";
		mes "You came... Thank you...";
		next;
		mes "^3355FFThe pope faintly smiled";
		mes "at High Priest Zhed, glad";
		mes "to finally see him again.^000000";
		next;
		cutin "ra_gwoman",0;
		mes "[Niren]";
		mes "Your Eminence...";
		mes "I hope you understand";
		mes "that I've tried my best";
		mes "to only show you what is";
		mes "beautiful in this world...";
		next;
		cutin "ra_bishop",2;
		mes "[Pope]";
		mes "...............................";
		mes "High Priestess Niren...";
		next;
		cutin "ra_gwoman",0;
		mes "[Niren]";
		mes "However, today I must";
		mes "reveal to you the world's";
		mes "ugliness as well. I'm sorry...";
		mes "I gave the Sky Garden people";
		mes "a vacation today so that we";
		mes "tell you something in private.";
		next;
		cutin "ra_bishop",2;
		mes "[Pope]";
		mes "Niren... What is it?";
		next;
		cutin "ra_gman2",0;
		mes "[Zhed]";
		mes "You may want to brace";
		mes "yourself, Your Eminence,";
		mes "for what we are about to";
		mes "tell you. You may very";
		mes "well be shocked...";
		next;
		mes "^3355FFHigh Priest Zhed explained";
		mes "all of Arunafeltz's secrets";
		mes "in detail. He revealed the";
		mes "split between the moderates";
		mes "and hard liners among the";
		mes "priests, the corruption...^000000";
		next;
		mes "^3355FFHe also explained the role";
		mes "of the priests in the inhumane";
		mes "testing conducted by the";
		mes "Rekenber Corporation, and";
		mes "the hard liner priests grab";
		mes "for power at Thor Volcano.^000000";
		next;
		mes "^3355FFThe pope stared at";
		mes "High Priest Zhed in";
		mes "shock as he spoke.^000000";
		next;
		mes "[Zhed]";
		mes "We all understand that";
		mes "you're already burdened";
		mes "enough with your duties";
		mes "as pope, but we feel that";
		mes "you should take action soon.";
		next;
		cutin "ra_bishop",2;
		mes "[Pope]";
		mes "...............................";
		mes "...............................";
		mes "...............................";
		next;
		cutin "ra_gwoman",2;
		mes "[Niren]";
		mes "We don't mean to force";
		mes "you, Your Eminence, but";
		mes "we don't have much time.";
		mes "We're facing the possibility";
		mes "of war, and we must stop it";
		mes "in order to save Arunafeltz.";
		next;
		mes "[Pope]";
		mes "...............................";
		mes "...............................";
		mes "...............................";
		mes ".......................Finally.";
		next;
		cutin "ra_gman2",0;
		mes "[Zhed, Niren]";
		mes "Excuse me?";
		next;
		cutin "ra_bishop",2;
		mes "[Pope]";
		mes "Niren, Zhed...";
		mes "I'm glad that you all";
		mes "finally came to me for";
		mes "a solution to all this.";
		mes "I'm young, but I remember";
		mes "what I saw in the holy ground.";
		next;
		mes "[Pope]";
		mes "Don't you remember, Niren?";
		mes "You found me in the holy";
		mes "ground, and safely led me";
		mes "out. But while I was there,";
		mes "I saw ^3131FFYmir's Heart^000000.";
		next;
		mes "[Zhed]";
		mes "How... How did you know";
		mes "that was Ymir's Heart?";
		next;
		mes "[Pope]";
		mes "The priests in the Sky";
		mes "Garden don't mind talking";
		mes "freely around me. I guess";
		mes "they looked at me more as";
		mes "a child, rather than as pope.";
		next;
		mes "[Pope]";
		mes "I'm already aware of the";
		mes "moderate priest group";
		mes "conducting their cruel tests,";
		mes "and of the hard liners building";
		mes "their military power. However,";
		mes "I couldn't really do anything.";
		next;
		mes "[Pope]";
		mes "Most people don't take my";
		mes "position seriously because";
		mes "of my age. Nobody believes";
		mes "I can change the situation.";
		mes "I've also heard everything";
		mes "about what you did, Zhed.";
		next;
		mes "[Pope]";
		mes "I'm sorry that you were";
		mes "punished by the other priests";
		mes "for allowing an outsider to";
		mes "enter the holy ground, but";
		mes "I understand your reasons.";
		mes "Isn't this the adventurer?";
		next;
		mes "[Pope]";
		mes ""+ strcharinfo(0) +"...";
		mes "I'm very glad to see you";
		mes "again. Not only did you";
		mes "share news of the outside";
		mes "world with me, but you've";
		mes "been protecting our peace.";
		next;
		cutin "ra_gwoman",0;
		mes "[Niren]";
		mes "I really didn't know...";
		mes "How'd you feel about this,";
		mes "or how much you actually";
		mes "knew. I'm truly sorry, Your";
		mes "Eminence. I'm ashamed";
		mes "for underestimating you.";
		next;
		cutin "ra_bishop",2;
		mes "[Pope]";
		mes "Please don't apologize,";
		mes "Niren. If it weren't for you,";
		mes "I'd be drawn into the utter";
		mes "foolishness of the selfish";
		mes "people in this temple. Niren,";
		mes "you're like a mother to me.";
		next;
		cutin "ra_gwoman",0;
		mes "[Niren]";
		mes "Your Eminence...";
		mes "Thank you. I feel";
		mes "the same way...";
		next;
		cutin "ra_bishop",2;
		mes "[Pope]";
		mes "Zhed...?";
		mes "I need you to do";
		mes "something for me.";
		next;
		cutin "ra_gman",0;
		mes "[Zhed]";
		mes "Of course,";
		mes "Your Eminence.";
		next;
		mes "[Pope]";
		mes "Write a report about the";
		mes "tests conducted by the";
		mes "moderates in Arunafeltz and";
		mes "the Schwarzwald Republic,";
		mes "and their relationship with";
		mes "that Schwarzwald Corporation.";
		next;
		mes "[Zhed]";
		mes "I'll get it done immediately,";
		mes "Your Eminence. Thank you.";
		next;
		cutin "ra_bishop",2;
		mes "[Pope]";
		mes "Niren.";
		next;
		cutin "ra_gwoman",0;
		mes "[Niren]";
		mes "Yes, Your Eminence?";
		next;
		cutin "ra_bishop",2;
		mes "[Pope]";
		mes "Please collect the";
		mes "evidence that proves";
		mes "that the hard liners have";
		mes "been gearing for war.";
		next;
		mes "[Niren]";
		mes "Yes, Your Eminence.";
		next;
		mes "[Pope]";
		mes "I'll summon all the High";
		mes "Priests and Priestesses";
		mes "soon. I'd like the two of";
		mes "you to stand by my side.";
		mes "I won't let anyone disgrace";
		mes "our holy ground with bloodshed.";
		next;
		cutin "ra_gman",0;
		mes "[Zhed]";
		mes "As you command,";
		mes "Your Eminence.";
		next;
		cutin "ra_bishop",2;
		mes "[Pope]";
		mes ""+ strcharinfo(0) +"...";
		mes "I appreciate all your work";
		mes "on behalf of Arunafeltz.";
		mes "My influence may still be";
		mes "weak, but I'll do my best";
		mes "with Zhed and Niren's support.";
		next;
		mes "[Pope]";
		mes "I would be expecting";
		mes "too much to ask you to";
		mes "stay by my side. But I'd like";
		mes "to thank you for all you've";
		mes "done. But if it is Freya's";
		mes "will, we'll meet again...";
		next;
		mes "[Pope]";
		mes "May Freya bless you...";
		next;
		specialeffect2 EF_ENCHANTPOISON;
		mes "^3355FFThe pope prayed in earnest";
		mes "for you, and you feel a strong";
		mes "aura of warmth and kindness";
		mes "permeate your entire being.^000000";
		set aru_em,22;
		changequest 2140,2141;
		close2;
		warp "rachel",142,136;
	}
	end;
}

rachel,142,136,0	script	#em_end	-1,5,5,{
OnInit:
	stopnpctimer;
	end;

OnTouch_:
	if (aru_em == 22) {
		initnpctimer;
		disablenpc "#em_end";
	}
	end;

OnTimer4000:
	mapannounce "rachel","Pope: Citizens of Arunafeltz. High Priests and Priestesses.",bc_map,"0xFFCE00";
	end;

OnTimer7000:
	mapannounce "rachel","Pope: I appreciate you all for coming to the Sky Garden as I've asked.",bc_map,"0xFFCE00";
	end;

OnTimer10000:
	mapannounce "rachel","Pope: As the chosen vessel of Goddess Freya, I hereby announce the words I received from her yesterday.",bc_map,"0xFFCE00";
	end;

OnTimer15000:
	mapannounce "rachel","Pope: I have observed all that is happening in Arunafeltz.",bc_map,"0x66CCCC";
	end;

OnTimer19000:
	mapannounce "rachel","Pope: I am aware of the factioning between the High Priests, their selfish fighting, their failure to achieve solidarity.",bc_map,"0x66CCCC";
	end;

OnTimer23000:
	mapannounce "rachel","Pope: However, I did not intervene for the sake of those that still pray for peace in Arunafeltz.",bc_map,"0x66CCCC";
	end;

OnTimer30000:
	mapannounce "rachel","Pope: High Priest Zhed and High Priestess Niren, both of you must do your best to assist my vessel until the day of my arrival.",bc_map,"0x66CCCC";
	end;

OnTimer35000:
	mapannounce "rachel","Pope: As for the other high priests, you have forgotten your duty to serve me, to enlighten my followers with my teachings. For pursuing your selfish desires, you will be all under Zhed and Niren's command.",bc_map,"0x66CCCC";
	end;

OnTimer43000:
	mapannounce "rachel","Pope: My followers in Arunafeltz, keep your faith in me, and keep your country strong.",bc_map,"0x66CCCC";
	end;

OnTimer47000:
	mapannounce "rachel","Pope: Then, paradise will surely be yours.",bc_map,"0x66CCCC";
	end;

OnTimer53000:
	mapannounce "rachel","Citizens: Hail Freya! Hail to the Pope!",bc_map,"0x99CC00";
	stopnpctimer;
	enablenpc "#em_end";
	end;
}

que_temsky,99,7,0	warp	quetemskyout	1,1,ra_temin,276,231

// Muff's Loan :: zdan_total_edq
//============================================================
comodo,224,187,3	script	Muff	86,{
	if (checkweight(907,200) == 0) {
		mes "[Muff]";
		mes "You're carrying way too";
		mes "much stuff right now.";
		mes "Come back after you put";
		mes "your stuff in Kafra Storage.";
		close;
	}
	if (diamond_edq == 0) {
		mes "[Muff]";
		mes "What th-?! Geez,";
		mes "you didn't have to";
		mes "scare me like that!";
		mes "What is it that you want?";
		next;
		switch(select("You look troubled.:Nothing. Take care!")) {
		case 1:
			if (BaseLevel < 61) {
				mes "[Muff]";
				mes "Oh. That's awfully";
				mes "thoughtful of you.";
				mes "I was thinking about";
				mes "asking you for help,";
				mes "but you don't look like";
				mes "you could handle it. Sorry.";
				set diamond_edq,3;
				close;
			}
			else {
				mes "[Muff]";
				mes "Well, I'm actually in";
				mes "pretty big trouble.";
				mes "I borrowed some money";
				mes "from this loan shark";
				mes "named ^0000FFBelder^000000 in Alberta.";
				mes "It was a bad move!";
				next;
				mes "[Muff]";
				mes "My business hasn't been";
				mes "good, but I felt like I didn't";
				mes "have a choice. I made enough";
				mes "money to repay him now, but";
				mes "then I lost my bond of debt.";
				next;
				mes "[Muff]";
				mes "When I asked Belder if";
				mes "I could pay him back without";
				mes "that bond, he insisted that";
				mes "he didn't remember loaning";
				mes "money to me. I bet he just";
				mes "wants to keep my collateral!";
				next;
				mes "[Muff]";
				mes "I knew that guy was shady!";
				mes "Would you help me find my";
				mes "lost bond of debt? You see,";
				mes "the collateral I gave him";
				mes "is really valuable to me.";
				next;
				switch(select("Do you know where you lost it?:I'm sorry to hear that. Bye!")) {
				case 1:
					mes "[Muff]";
					mes "If I knew where I lost";
					mes "it, then it wouldn't be";
					mes "lost now, would it? Well,";
					mes "I remember going to a union";
					mes "meeting, and I had a drink";
					mes "or two. Okay, I had a lot.";
					next;
					mes "[Muff]";
					mes "On the way home,";
					mes "I stumbled in a field";
					mes "near Comodo... I remember";
					mes "seeing some water... That";
					mes "must be where I dropped my";
					mes "wallet with the bond of debt.";
					next;
					mes "[Muff]";
					mes "If you can find my";
					mes "wallet, I'll make sure";
					mes "to repay you. Please";
					mes "help me if you can!";
					setquest 3100;
					set diamond_edq,1;
					close;
				case 2:
					mes "[Muff]";
					mes "H-hey! Where are you";
					mes "going?! You can't just";
					mes "leave me here! I need help!";
					close;
				}
			}
		case 2:
			mes "[Muff]";
			mes "What, are you trying to";
			mes "make me mad on purpose?";
			mes "I get it. You're working for";
			mes "Belder! I'm gonna get";
			mes "my treasure from that";
			mes "bastard, no matter what!";
			next;
			switch(select("I don't understand.:I'm sorry, I was just kidding...")) {
			case 1:
				mes "[Muff]";
				mes "You think you can";
				mes "fool me?! Get lost!";
				mes "I'm not telling you";
				mes "anything, you snake!";
				close;
			case 2:
				mes "[Muff]";
				mes "So you're not spying on";
				mes "me for Belder? Well, then";
				mes "why are you pestering me?";
				close;
			}
		}
	}
	if ((diamond_edq == 3) && (BaseLevel < 61)) {
		mes "[Muff]";
		mes "Wait a second...";
		mes "You're not as strong";
		mes "as you were earlier.";
		mes "You'd better train first";
		mes "so you can be better";
		mes "prepared to help me.";
		close;
	}
	if ((diamond_edq == 3) && (BaseLevel > 60)) {
		mes "[Muff]";
		mes "Well, I'm actually in";
		mes "pretty big trouble.";
		mes "I borrowed some money";
		mes "from this loan shark";
		mes "named ^0000FFBelder^000000 in Alberta.";
		mes "It was a bad move!";
		next;
		mes "[Muff]";
		mes "My business hasn't been";
		mes "good, but I felt like I didn't";
		mes "have a choice. I made enough";
		mes "money to repay him now, but";
		mes "then I lost my bond of debt.";
		next;
		mes "[Muff]";
		mes "When I asked Belder if";
		mes "I could pay him back without";
		mes "that bond, he insisted that";
		mes "he didn't remember loaning";
		mes "money to me. I bet he just";
		mes "wants to keep my collateral!";
		next;
		mes "[Muff]";
		mes "I knew that guy was shady!";
		mes "Would you help me find my";
		mes "lost bond of debt? You see,";
		mes "the collateral I gave him";
		mes "is really valuable to me.";
		next;
		switch(select("Do you know where you lost it?:What was the collateral?:Bye!")) {
		case 1:
			mes "[Muff]";
			mes "If I knew where I lost";
			mes "it, then it wouldn't be";
			mes "lost now, would it? Well,";
			mes "I remember going to a union";
			mes "meeting, and I had a drink";
			mes "or two. Okay, I had a lot.";
			next;
			mes "[Muff]";
			mes "I took this path in the";
			mes "Papuchicha Forest to head";
			mes "back home, and I must have";
			mes "passed out near a river.";
			mes "After that, my wallet with the";
			mes "bond of debt was missing.";
			next;
			mes "[Muff]";
			mes "I don't care about the";
			mes "other stuff in my wallet,";
			mes "but I really need my bond";
			mes "of debt to get my collateral";
			mes "back from that Belder.";
			setquest 3100;
			set diamond_edq,1;
			close;
		case 2:
			mes "[Muff]";
			mes "Well, the collateral was";
			mes "this huge, stunningly";
			mes "beautiful jewel. It's really";
			mes "precious to me. But well,";
			mes "I don't want to tell you";
			mes "more about it.";
			next;
			mes "[Muff]";
			mes "I understand if you don't";
			mes "want to help me after";
			mes "hearing that, but if you";
			mes "change your mind, please";
			mes "come talk to me again.";
			close;
		case 3:
			mes "[Muff]";
			mes "H-hey! Where are you";
			mes "going?! You can't just";
			mes "leave me here! I need help!";
			close;
		}
	}
	if ((diamond_edq == 1) || (diamond_edq == 2)) {
		mes "[Muff]";
		mes "You didn't leave to";
		mes "look for my wallet yet?";
		mes "I think it's somewhere";
		mes "in the Papuchicha Forest.";
		mes "Please find the bond of debt";
		mes "inside as soon as you can.";
		close;
	}
	if (diamond_edq == 4) {
		mes "[" + strcharinfo(0) + "]";
		mes "Is this wet, tattered";
		mes "piece of paper the bond";
		mes "of debt that you need?";
		next;
		mes "[Muff]";
		mes "Yes, that's it!";
		mes "Ugh, but look at it...";
		mes "It's ruined! You can't";
		mes "even read what's written";
		mes "on it! I need... I need";
		mes "to fix this somehow!";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "Do you have any ideas?";
		mes "I mean, if we dried the";
		mes "paper, the letters would";
		mes "still be faded, right?";
		next;
		mes "[Muff]";
		mes "I got it! There's this";
		mes "famous inventor named";
		mes "^FF0000Dorian^000000 in Izlude. I heard";
		mes "he just invented something";
		mes "like a Magic Dryer. Yeah,";
		mes "that should work perfectly!";
		next;
		mes "[Muff]";
		mes "I don't know exactly";
		mes "how it works, but it";
		mes "should be able to restore";
		mes "my bond of debt. I mean...";
		mes "It's magic, right? Will you";
		mes "ask Dorian to help me out?";
		next;
		switch(select("Accept His Request:Decline His Request")) {
		case 1:
			mes "[Muff]";
			mes "Thank you so much!";
			mes "Please talk to Inventor";
			mes "Dorian in Izlude, and ask";
			mes "him to restore my bond of";
			mes "debt. I'll be waiting for";
			mes "you right here, okay?";
			changequest 3102,3103;
			set diamond_edq,6;
			close;
		case 2:
			mes "[Muff]";
			mes "Really? I was hoping";
			mes "that you'd continue to";
			mes "help me, but... Alright.";
			mes "I'm sure you've got other";
			mes "problems. If you change your";
			mes "mind, though, just come back.";
			set diamond_edq,5;
			close;
		}
	}
	if (diamond_edq == 5) {
		mes "[Muff]";
		mes "Oh, I knew you'd come";
		mes "back and help me restore";
		mes "my bond of debt! You look";
		mes "too nice to just leave";
		mes "me hanging like that.";
		next;
		switch(select("Accept His Request:Decline His Request")) {
		case 1:
			mes "[Muff]";
			mes "Thank you so much!";
			mes "Please talk to Inventor";
			mes "Dorian in Izlude, and ask";
			mes "him to restore my bond of";
			mes "debt. I'll be waiting for";
			mes "you right here, okay?";
			changequest 3102,3103;
			set diamond_edq,6;
			close;
		case 2:
			mes "[Muff]";
			mes "Really? I was hoping";
			mes "that you'd continue to";
			mes "help me, but... Alright.";
			mes "I'm sure you've got other";
			mes "problems. If you change your";
			mes "mind, though, just come back.";
			close;
		}
	}
	if ((diamond_edq > 5) && (diamond_edq < 13)) {
		mes "[Muff]";
		mes "Would you please visit";
		mes "^FF0000Inventor Dorian^000000 in Izlude,";
		mes "and ask him to use his";
		mes "Magic Dryer to restore";
		mes "my bond of debt?";
		close;
	}
	if ((diamond_edq == 13) && (countitem(7722) > 0)) {
		mes "[Muff]";
		mes "You're back! So how";
		mes "did it go? I was getting";
		mes "pretty anxious... So did";
		mes "Dorian's Magic Dryer work?";
		next;
		delitem 7722,1; //Debt_Note
		mes "[Muff]";
		mes "Oh! It worked much better";
		mes "than I expected! Belder";
		mes "can't complain now! Haha!";
		mes "You've done well, my friend.";
		mes "Here, please take these as";
		mes "a meager reward for your help.";
		completequest 3109;
		set diamond_edq,14;
		getitem 603,1; //Old_Blue_Box
		getitem 608,4; //Seed_Of_Yggdrasil
		next;
		mes "[Muff]";
		mes "Now that I have my bond";
		mes "of debt, I should get my";
		mes "jewel back from Belder.";
		mes "He won't have any excuse";
		mes "to keep my treasure now!";
		close;
	}
	else if ((diamond_edq == 13) && (countitem(7722) < 1)) {
		mes "[Muff]";
		mes "H-hey! Where's my";
		mes "bond of debt?! Don't";
		mes "tell me you lost it!";
		mes "You'd better go back";
		mes "to Dorian... Hopefully";
		mes "you left it with him!";
		set diamond_edq,10;
		close;
	}
	if (diamond_edq > 13) {
		mes "[Muff]";
		mes "Yes! Finally, I'm";
		mes "free of debt! I've got";
		mes "my jewel back, business";
		mes "is doing well... Life sure";
		mes "is good right now. Hahaha!";
		close;
	}
	mes "[Muff]";
	mes "Listen to me: never";
	mes "take out a loan if you";
	mes "can help it. And if you do,";
	mes "borrow from someone";
	mes "more reputable than...";
	mes "Well, you know my story.";
	mes "*Sigh*";
	close;
}

alberta,127,143,3	script	Belder	85,{
	if (diamond_edq == 0) {
		mes "[Belder]";
		mes "I ought to hire some";
		mes "part-time promotional";
		mes "workers... Some sexy";
		mes "ladies ought to bring in";
		mes "the customers by th--Oh!";
		mes "Welcome to Belder Loans!";
		next;
		switch(select("I'll promote your loans!:Uh...")) {
		case 1:
			if (Sex == SEX_MALE) {
				mes "[Belder]";
				mes "You? But you're a dude!";
				mes "No sweaty, stinky, sleazy";
				mes "man should be the image";
				mes "of Belder Loans! We need";
				mes "to exude trust, dependability,";
				mes "and... And lady charms.";
				close;
			}
			else {
				mes "[Belder]";
				mes "You? Well, don't take";
				mes "this the wrong way, but";
				mes "all your curves? Wrong";
				mes "places, honey. Later!";
				close;
			}
		case 2:
			mes "[Belder]";
			mes "Hey, aren't you";
			mes "interested in a loan?";
			mes "Better you come to";
			mes "me than you relying";
			mes "on some guy that'll";
			mes "try to rip you off!";
			close;
		}
	}
	if (diamond_edq < 13) {
		mes "[Belder]";
		mes "Welcome! So, do you";
		mes "need money fast? Belder";
		mes "Loans get you the money";
		mes "you need at low interest rates!";
		next;
		switch(select("Ask About Muff")) {
		case 1:
			mes "[Belder]";
			mes "Muff? Oh! Is that";
			mes "supposed to be a name?";
			mes "I thought you meant--No.";
			mes "Nope. Doesn't ring a bell.";
			next;
			mes "[Belder]";
			mes "Huh? Collateral?";
			mes "Now why would I refuse";
			mes "to take back his money";
			mes "if I really loaned it to him?";
			mes "Leave me alone now, this kind";
			mes "of talk is bad for business!";
			close;
		}
	}
	if (diamond_edq > 12) {
		mes "[Belder]";
		mes "He really found his bond";
		mes "of debt? Damn it--I mean,";
		mes "what a surprise! That's,";
		mes "uh, real good for him.";
		mes "Now that I think of it, I did";
		mes "loan a Muff money once.";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "......";
		next;
		mes "[Belder]";
		mes "What's with that dirty look?";
		mes "I've got nothing to hide!";
		mes "Can't you see that I'm";
		mes "the victim here? Me!";
		mes "Just... Scram before you";
		mes "make me more upset.";
		close;
	}
	mes "[Belder]";
	mes "Welcome! Did you need";
	mes "money fa--Oh. Hey, it's";
	mes "you again, the do-gooder.";
	mes "Why don't you do me some";
	mes "good, and go back where";
	mes "you came from. Jerk.";
	close;

OnTouch_:
	if (diamond_edq < 3) {
		mes "[Belder]";
		mes "Cash flow problems?";
		mes "Well, I'm your solution!";
		mes "I offer unsecured loans";
		mes "at an extremely low interest";
		mes "rate, twenty-four hours a day!";
		mes "Belder Loans is Alberta's best!";
		close;
	}
	end;
}

cmd_fild01,55,160,0	script	Heap of Earth	844,{
	if (diamond_edq == 1) {
		mes "^3355FFIt looks like someone";
		mes "dug a hole in the ground,";
		mes "and then covered it again.^000000";
		next;
		switch(select("Dig Up the Spot:Cancel")) {
		case 1:
			mes "[" + strcharinfo(0) + "]";
			mes "There must be something";
			mes "in the ground. I better";
			mes "dig it up and check...";
			next;
			mes "[" + strcharinfo(0) + "]";
			mes "Ergh...! Ugh!";
			mes "This would be a lot";
			mes "easier with a shovel!";
			mes "Only a little more to go...";
			next;
			mes "[" + strcharinfo(0) + "]";
			mes "Hm? I hit something?";
			mes "Let's--Yucky! It's so";
			mes "wet and--Ugh! Nasty";
			mes "little thing. Now what?";
			mes "I dropped it! I have to";
			mes "try to dig it up again...";
			changequest 3100,3101;
			set diamond_edq,2;
			close;
		case 2:
			mes "[" + strcharinfo(0) + "]";
			mes "Something isn't quite";
			mes "kosher about all this.";
			mes "Nope, this time, I'm";
			mes "not going to do this.";
			close;
		}
	}
	else if (diamond_edq == 2) {
		mes "[" + strcharinfo(0) + "]";
		mes "Here we are... It's...";
		mes "A soggy wallet? This";
		mes "must be the one Muff";
		mes "lost. Oh, and the bond";
		mes "of debt is inside... Mm...";
		mes "Ugh, it's really soaked...";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "Well, I've done all";
		mes "I can for now. I should";
		mes "bring this back to Muff.";
		changequest 3101,3102;
		set diamond_edq,4;
		close;
	}
	if (diamond_edq > 3) {
		mes "[" + strcharinfo(0) + "]";
		mes "This is the same spot";
		mes "I dug up from before.";
		mes "It's useless to me now!";
		close;
	}
	mes "[" + strcharinfo(0) + "]";
	mes "Huh. There's a weird";
	mes "bump in the ground. Well,";
	mes "I can't just go looking at";
	mes "anything that's slightly";
	mes "out of the ordinary. Heh.";
	mes "I'm not weird like that.";
	close;
}

-	script	::Dorian_izlude	-1,{
	if (checkweight(907,200) == 0) {
		mes "[Dorian]";
		mes "Ugh, just like a member";
		mes "of the working class:";
		mes "hoarding all your items";
		mes "like a packrat? Have the";
		mes "decency to relocate your";
		mes "goods to Kafra Storage, please.";
		close;
	}
	if (diamond_edq < 6) {
		mes "[Dorian]";
		mes "You must be in awe";
		mes "of my elegant, artistic";
		mes "touch. In my hands, almost";
		mes "anything can become a";
		mes "work of art... Even you~";
		close;
	}
	if (diamond_edq == 6) {
		mes "[Dorian]";
		mes "I am Inventor Dorian,";
		mes "and I welcome you to my";
		mes "workship. All I create is";
		mes "infused with art's essence~";
		mes "So... How may I assist you?";
		next;
		switch(select("I want to use your Magic Dryer.:I want to be your student.:No, thanks.")) {
		case 1:
			mes "[Dorian]";
			mes "Magic Dryer? No, no, no.";
			mes "That, my friend, is my";
			mes "Mystic Heater de Elegance.";
			mes "Don't disgrace my creation";
			mes "like that. What, pray tell,";
			mes "do you need to dry?";
			next;
			switch(select("This Bond of Debt:My Hair:I'm just curious.")) {
			case 1:
				mes "[Dorian]";
				mes "Mm? My masterpiece is far";
				mes "too sophisticated for such";
				mes "petty purposes, but... You";
				mes "intrigue me. Show me this";
				mes "''bond of debt'' you wish";
				mes "to restore. ";
				next;
				mes "[Dorian]";
				mes "Ah, now I understand.";
				mes "This print is beyond";
				mes "recognition. Only my creation";
				mes "can restore the life that";
				mes "nature has taken away! Yes...";
				mes "You were right to come here.";
				next;
				mes "[Dorian]";
				mes "Allow me to explain how";
				mes "the Mystic Dryer de Elegance";
				mes "works. A glamourous ruby";
				mes "engine whose design was born";
				mes "of my genius, generates a";
				mes "powerful magnetic field.";
				next;
				mes "[Dorian]";
				mes "The ruby engine's";
				mes "magnetic field operates";
				mes "according to the ^FF00003 Centrifuge";
				mes "Wavelength Theory^000000. Now,";
				mes "pearl inset sensors detect";
				mes "the amount of calibration...";
				next;
				mes "[Dorian]";
				mes "...............................";
				mes "...............................";
				mes "...............................";
				mes "...............................";
				mes "...............................";
				mes "...............................";
				next;
				mes "[Dorian]";
				mes "...............................";
				mes "...............................";
				mes "...............................";
				mes "...............................";
				mes "Blah-blah--that's just";
				mes "about it in a nutshell.";
				next;
				switch(select("Zzz...:Oh, please!")) {
				case 1:
					mes "[Dorian]";
					mes "You... You fell asleep?";
					mes "But I was dutifully explaining";
					mes "the machine's operation!";
					mes "No, no, I won't hear your";
					mes "excuses. What's that?";
					mes "You were listening?";
					next;
					mes "[Dorian]";
					mes "Hm. Well then, let me";
					mes "ask you this: what is the";
					mes "main theory by which the";
					mes "Mystic Heater de Elegance";
					mes "operates? Hmm? Well?";
					next;
					input .@input$;
					if (.@input$ == "3 Centrifuge Wavelength Theory") {
						mes "[Dorian]";
						mes "That's right. My apologies.";
						mes "I suppose I misjudged you.";
						mes "Yes, the magnetic field can";
						mes "restore the shape and integrity";
						mes "of damaged, inanimate objects.";
						mes "However, there is a problem.";
						next;
						mes "[Dorian]";
						mes "The ruby engine is broken,";
						mes "and I've been too busy with";
						mes "other projects as ordered by";
						mes "the Rune-Midgarts Kingdom.";
						mes "I'll need your assistance";
						mes "to fix the engine. Alright?";
						next;
						mes "[Dorian]";
						mes "If you help me fix the";
						mes "Mystic Dryer de Elegance,";
						mes "I'll allow you to use it";
						mes "as much as you like. First,";
						mes "I'd like you to gather all";
						mes "of the repair materials.";
						next;
						mes "[Dorian]";
						mes "Please procure";
						mes "^FF000020 Rusty Screws^000000,";
						mes "^FF000010 Iron Ores^000000,";
						mes "^FF00005 Steel^000000,";
						mes "^FF00002 Rubies^000000, and";
						mes "^FF000010 Red Gemstones^000000.";
						next;
						mes "[Dorian]";
						mes "Once you bring all the";
						mes "repair materials, I'll";
						mes "explain how you can";
						mes "repair the Mystic";
						mes "Dryer de Elegance.";
						changequest 3103,3104;
						set diamond_edq,9;
						close;
					}
					else {
						mes "[Dorian]";
						mes "Oh? You sound dissatisfied";
						mes "with my explanation. Well...";
						mes "I surely can't let you use the";
						mes "machine unless you fathom";
						mes "how it works. Otherwise,";
						mes "you just might break it.";
						close;
					}
				case 2:
					mes "[Dorian]";
					mes "Were you too bored while";
					mes "listening to me speak?";
					mes "Ah! This wound! It's so...";
					mes "Alas! It's too emotional!";
					mes "I am seriously hurt!";
					set diamond_edq,7;
					//PushPC 4 3
					close;
				}
				break;
			case 2:
				mes "[Dorian]";
				mes "How can you compare my";
				mes "Mystic Dryer de Elegance";
				mes "to a simple hair dryer?";
				mes "Your comment offends me!";
				mes "I'm no mere engineer...";
				mes "I'm a true virtuoso!";
				set diamond_edq,7;
				//PushPC 4 3
				close;
			case 3:
				mes "[Dorian]";
				mes "Allow me to explain how";
				mes "the Mystic Dryer de Elegance";
				mes "works. A glamourous ruby";
				mes "engine whose design was born";
				mes "of my genius, generates a";
				mes "powerful magnetic field.";
				next;
				mes "[Dorian]";
				mes "The ruby engine's";
				mes "magnetic field operates";
				mes "according to the ^FF00003 Centrifuge";
				mes "Wavelength Theory^000000. Now,";
				mes "pearl inset sensors detect";
				mes "the amount of calibration...";
				next;
				mes "[Dorian]";
				mes "...............................";
				mes "...............................";
				mes "...............................";
				mes "...............................";
				mes "...............................";
				mes "...............................";
				next;
				mes "[Dorian]";
				mes "...............................";
				mes "...............................";
				mes "...............................";
				mes "...............................";
				mes "Blah-blah--that's just";
				mes "about it in a nutshell.";
				next;
				mes "[" + strcharinfo(0) + "]";
				mes "......";
				next;
				mes "[Dorian]";
				mes "I know, I know.";
				mes "Astounding, isn't it?";
				close;
			}
		case 2:
			if (Sex == SEX_MALE) {
				mes "[Dorian]";
				mes "Ah, my apologies.";
				mes "Too many have asked, so";
				mes "I now only accept beautiful";
				mes "female students. Don't be";
				mes "too disappointed: I know";
				mes "you can find someone else.";
				close;
			}
			else {
				mes "[Dorian]";
				mes "Mademoiselle, it is my";
				mes "honor to meet you: even";
				mes "the moon covers its face,";
				mes "and roses raise their thorns,";
				mes "shameful of themselves";
				mes "and jealous of your beauty.";
				next;
				mes "[Dorian]";
				mes "Ah! But why go under the";
				mes "pretense of being my student";
				mes "when we can leisurely spend";
				mes "time together as lovers?";
				mes "Won't you--Mademoiselle?";
				mes "Wait, where are you going?";
				close;
			}
		case 3:
			mes "[Dorian]";
			mes "I don't have enough";
			mes "time for trivel, so";
			mes "please leave me to my";
			mes "artistic endeavors and";
			mes "bother me no longer.";
			close;
		}
	}
	if (diamond_edq == 7) {
		mes "[Dorian]";
		mes "Ugh, your behavior";
		mes "from before disgusts me...";
		mes "But those pitiful eyes...";
		mes "Seeking sympathy. Ah,";
		mes "you're only human.";
		mes "I shall forgive you.";
		next;
		mes "[Dorian]";
		mes "Alright then. Please";
		mes "listen carefully this time,";
		mes "and don't insult my pride";
		mes "again. When you are ready,";
		mes "please come talk to me.";
		set diamond_edq,6;
		close;
	}
	if (diamond_edq == 9) {
		mes "[Dorian]";
		mes "Ah, did you bring all";
		mes "the materials I need to";
		mes "repair the Mystic Dryer";
		mes "de Elegance? Let me take";
		mes "a glance at what you brought...";
		next;
		if ((countitem(7317) > 19) && (countitem(1002) > 9) && (countitem(999) > 4) && (countitem(723) > 1) && (countitem(716) > 4)) {
			mes "[Dorian]";
			mes "Ah, well done. Now";
			mes "that the materials are";
			mes "ready, I can instruct you on";
			mes "how to repair the machine.";
			next;
			delitem 7317,20; //Screw
			delitem 1002,10; //Iron_Ore
			delitem 999,5; //Steel
			delitem 723,2; //Cardinal_Jewel
			delitem 716,5; //Red_Gemstone
			changequest 3104,3105;
			set diamond_edq,10;
			mes "[Dorian]";
			mes "I expect you to fail";
			mes "a few times, but the";
			mes "Mystic Dryer de Elegance";
			mes "is designed for durability:";
			mes "even if you tried, you'd";
			mes "have trouble scratching it.";
			next;
			mes "[Dorian]";
			mes "First, insert a new rough";
			mes "ruby into the ruby engine,";
			mes "and tighten the screw on";
			mes "the joint until you hear";
			mes "a ^0000FFclick^000000. You'll break it";
			mes "if you tighten it further.";
			next;
			mes "[Dorian]";
			mes "Next you'll need to";
			mes "operate the four switches";
			mes "coded by the colors red,";
			mes "blue, yellow, and green.";
			next;
			mes "[Dorian]";
			mes "Press the ^0000FFRed^000000 switch";
			mes "if the engine is in 1st";
			mes "Gear. Press the ^0000FFBlue^000000";
			mes "switch in 2nd Gear,";
			mes "^0000FFYellow^000000 for 3rd, and";
			mes "^0000FFGreen^000000 for 4th.";
			next;
			mes "[Dorian]";
			mes "The engine's sound is the";
			mes "only indicator of which";
			mes "gear is running. You'll";
			mes "have to figure that out";
			mes "on your own. I'm sorry";
			mes "I designed it that way...";
			next;
			mes "[Dorian]";
			mes "Ah, one last tip!";
			mes "If you enter the correct";
			mes "engine gear, the machine";
			mes "will start without a problem,";
			mes "even if you press the same";
			mes "switch more than once.";
			next;
			mes "[Dorian]";
			mes "Please let me know";
			mes "if you get it started so";
			mes "I can verify if the Mystic";
			mes "Dryer de Elegance is fully";
			mes "operational. Good luck now~";
			close;
		}
		else {
			mes "[Dorian]";
			mes "Oh? You're still missing";
			mes "some materials. Well, we're";
			mes "in no rush. If you already";
			mes "forgot, then let me remind";
			mes "you what you need to bring...";
			next;
			mes "[Dorian]";
			mes "Please procure";
			mes "^FF000020 Rusty Screws^000000,";
			mes "^FF000010 Iron Ores^000000,";
			mes "^FF00005 Steel^000000,";
			mes "^FF00002 Rubies^000000, and";
			mes "^FF000010 Red Gemstones^000000.";
			close;
		}
	}
	if (diamond_edq == 10) {
		mes "[Dorian]";
		mes "Did you already forget";
		mes "my instructions for fixing my";
		mes "Mystic Dryer de Elegance?";
		mes "I can't blame you since";
		mes "they're a bit complicated.";
		next;
		switch(select("Instructions:Cancel")) {
		case 1:
			mes "[Dorian]";
			mes "First, insert a new rough";
			mes "ruby into the ruby engine,";
			mes "and tighten the screw on";
			mes "the joint until you hear";
			mes "a ^0000FFclick^000000. You'll break it";
			mes "if you tighten it further.";
			next;
			mes "[Dorian]";
			mes "Next you'll need to";
			mes "operate the four switches";
			mes "coded by the colors red,";
			mes "blue, yellow, and green.";
			next;
			mes "[Dorian]";
			mes "Press the ^0000FFRed^000000 switch";
			mes "if the engine is in 1st";
			mes "Gear. Press the ^0000FFBlue^000000";
			mes "switch in 2nd Gear,";
			mes "^0000FFYellow^000000 for 3rd, and";
			mes "^0000FFGreen^000000 for 4th.";
			next;
			mes "[Dorian]";
			mes "The engine's sound is the";
			mes "only indicator of which";
			mes "gear is running. You'll";
			mes "have to figure that out";
			mes "on your own. I'm sorry";
			mes "I designed it that way...";
			next;
			mes "[Dorian]";
			mes "Ah, one last tip!";
			mes "If you enter the correct";
			mes "engine gear, the machine";
			mes "will start without a problem,";
			mes "even if you press the same";
			mes "switch more than once.";
			next;
			mes "[Dorian]";
			mes "Please let me know";
			mes "if you get it started so";
			mes "I can verify if the Mystic";
			mes "Dryer de Elegance is fully";
			mes "operational. Good luck now~";
			close;
		case 2:
			mes "[Dorian]";
			mes "Really? It's no problem";
			mes "for me to explain it again";
			mes "to you. Don't be shy now:";
			mes "not all of us can be geniuses~";
			close;
		}
	}
	if (diamond_edq == 11) {
		mes "[Dorian]";
		mes "Ah, so you failed to";
		mes "fix the Mystic Dryer de";
		mes "Elegance? That's expected:";
		mes "I didn't really design it for the common user in mind. Well,";
		mes "why don't you try it again?";
		changequest 3106,3105;
		set diamond_edq,10;
		close;
	}
	if (diamond_edq == 12) {
		mes "[Dorian]";
		mes "Ah, I underestimated you.";
		mes "You really repaired the";
		mes "Mystic Dryer de Elegance~";
		mes "This is a testament to my";
		mes "incredible teaching prowess.";
		mes "Congratulations are in order!";
		next;
		mes "[Dorian]";
		mes "Now all you have to do";
		mes "is insert the damaged";
		mes "document and press the";
		mes "switch. Simple, yes?";
		changequest 3107,3108;
		set diamond_edq,13;
		next;
		mes "[Dorian]";
		mes "Well, I'm afraid my";
		mes "usefulness to you has";
		mes "been all used up. Ah,";
		mes "but I'll make sure to";
		mes "contact you if I need";
		mes "your kind of help. Farewell~";
		close;
	}
	if (diamond_edq > 12) {
		mes "[Dorian]";
		mes "Ah, hello! How have";
		mes "you been? Are you in need";
		mes "of my artistic inventions?";
		mes "Or did you just miss me?";
		close;
	}

OnTouch:
	if (diamond_edq < 9) {
		mes "[Dorian]";
		mes "Wh-what are you doing?";
		mes "D-don't sully my wonderful";
		mes "masterpieces with your";
		mes "uncultured hands!";
		close;
	}
	if (diamond_edq == 9) {
		mes "[Dorian]";
		mes "Can't you see that the";
		mes "machine won't work without";
		mes "the materials? Even a simple";
		mes "child should understand that!";
		close;
	}
	end;
}

-	script	::StrangeMachine_izlude	-1,{
	if (checkweight(907,200) == 0) {
		mes "[Dorian]";
		mes "Ugh, just like a member";
		mes "of the working class:";
		mes "hoarding all your items";
		mes "like a packrat? Have the";
		mes "decency to relocate your";
		mes "goods to Kafra Storage, please.";
		close;
	}
	if (diamond_edq == 10) {
		set .@bolt_rand,rand(2,5);
		mes "^3355FFThis must be Dorian's";
		mes "Mystic Dryer de Elegance.";
		mes "What do you want to do?^000000";
		next;
		while(1) {
			set .@switch_sound,rand(1,4);
			switch(select("Check the Machine:Replace the Engine:Tighten the Screw:Press a Switch")) {
			case 1:
				if (.@hit_status == 0) {
					mes "^3355FFThere's no power";
					mes "in this behemoth";
					mes "of a machine.^000000";
					next;
				}
				break;
			case 2:
				if (.@engine == 0) {
					mes "^3355FFYou replace the rough";
					mes "ruby in the ruby engine";
					mes "with a new rough ruby.";
					mes "It clicks into place";
					mes "inside the engine core.^000000";
					next;
					set .@engine,.@engine+1;
				}
				else if (.@engine > 0) {
					mes "^3355FFThe ruby in the";
					mes "engine has already";
					mes "been replaced.^000000";
					next;
				}
				break;
			case 3:
				if (.@engine <  1) {
					mes "^3355FFYou probably need";
					mes "to replace the ruby";
					mes "in the engine first.^000000";
					next;
				}
				else if ((.@engine == 1) && (.@bolt < .@bolt_rand)) {
					mes "^3355FF*Krrrr-Krrrrr*^000000";
					mes "^3355FF*Krrrr-Krrrrr*^000000";
					next;
					set .@bolt,.@bolt+1;
				}
				else if ((.@engine == 1) && (.@bolt == .@bolt_rand)) {
					mes "^3355FF*Krrrr-Krrrrr*^000000";
					mes "^0000FF*Click*^000000";
					next;
					set .@bolt,.@bolt+1;
					set .@bolt_suc,.@bolt_suc+1;
				}
				else if ((.@engine == 1) && (.@bolt > .@bolt_rand)) {
					mes "^3355FF*Krrrr-Krrrrr*^000000";
					mes "^3355FF*KrrICK-ICK-ICK-KOOM*^000000";
					next;
					mes "^3355FFYou tightened the";
					mes "screw too tightly!";
					mes "The joint is broken...";
					mes "You should go back";
					mes "to Dorian for help.^000000";
					changequest 3105,3106;
					set diamond_edq,11;
					close;
				}
				else {
					mes "^3355FFSomething must have";
					mes "gone wrong. You'd be";
					mes "better off starting";
					mes "from the beginning...^000000";
					close;
				}
				break;
			case 4:
				if ((.@engine <  1) && (.@bolt_suc < 1)) {
					mes "^3355FFYou need to replace";
					mes "the engine's ruby and";
					mes "tighten the main screw";
					mes "before you mess with";
					mes "these switches.^000000";
					next;
				}
				else if ((.@engine == 1) && (.@bolt_suc < 1)) {
					mes "^3355FFYou need to tighten";
					mes "the scren before you can";
					mes "start the engine safely.";
					next;
				}
				else if ((.@red >= 1) && (.@blue >= 1) && (.@yellow >= 1) && (.@green >= 1)) {
					mes "^3355FFThe Mystic Dryer de";
					mes "Elegance started with";
					mes "a mighty buzz, and the";
					mes "sound lowers to a calm";
					mes "hum as it stabilizes.";
					mes "It looks like you fixed it!^000000";
					next;
					mes "^3355FFYou should tell Dorian";
					mes "first so that you can";
					mes "use this machine.^000000";
					changequest 3105,3107;
					set diamond_edq,12;
					close;
				}
				else if ((.@engine == 1) && (.@bolt_suc == 1)) {
					if (.@switch_sound == 1) {
						mes "^3355FF*Buzz Buzz*^000000";
						mes "^3355FF*Buzz Buzz*^000000";
						mes "^3355FFThe machine is";
						mes "vibrating weakly.";
						mes "Which switch do";
						mes "you want to press?^000000";
						next;
						switch(select("Red Switch:Blue Switch:Yellow Switch:Green Switch")) {
						default:
							mes "^3355FF*Whiz Whiz*^000000";
							mes "^3355FF*Whiz Whiz*^000000";
							next;
							mes "^3355FFThe machine made some";
							mes "violent, erratic sounds,";
							mes "and vibrated violently";
							mes "before coming to a sudden";
							mes "stop. That was the wrong";
							mes "switch. You'd better ask Dorian...";
							changequest 3105,3106;
							set diamond_edq,11;
							close;
						case 1:
							if (.@red  >= 0) {
								mes "^3355FF*Buzzz Buzzz*^000000";
								mes "^3355FF*Clang...!*^000000";
								next;
								mes "^3355FFThe machine's";
								mes "vibrations have";
								mes "grown stronger.";
								mes "It looks like you";
								mes "chose the right switch.^000000";
								next;
								set .@red,.@red+1;
							}
							else {
								mes "^3355FF*Whiz Whiz*^000000";
								mes "^3355FF*Whiz Whiz*^000000";
								next;
								mes "^3355FFThe machine made some";
								mes "violent, erratic sounds,";
								mes "and vibrated violently";
								mes "before coming to a sudden";
								mes "stop. That was the wrong";
								mes "switch. You'd better ask Dorian...";
								changequest 3105,3106;
								set diamond_edq,11;
								close;
							}
							break;
						}
					}
					else if (.@switch_sound == 2) {
						mes "^3355FF*Purr Purr*^000000";
						mes "^3355FF*Purr Purr*^000000";
						mes "^3355FFThe machine is vibrating";
						mes "a little bit more strongly.";
						mes "Which switch will";
						mes "you try now?^000000";
						next;
						switch(select("Red Switch:Blue Switch:Yellow Switch:Green Switch")) {
						default:
							mes "^3355FF*Whiz Whiz*^000000";
							mes "^3355FF*Whiz Whiz*^000000";
							next;
							mes "^3355FFThe machine made some";
							mes "violent, erratic sounds,";
							mes "and vibrated violently";
							mes "before coming to a sudden";
							mes "stop. That was the wrong";
							mes "switch. You'd better ask Dorian...";
							changequest 3105,3106;
							set diamond_edq,11;
							close;
						case 2:
							if (.@blue >= 0) {
								mes "^3355FF*Purrr Purr*^000000";
								mes "^3355FF*Clang...!*^000000";
								next;
								mes "^3355FFThe machine's";
								mes "vibrations have";
								mes "grown stronger.";
								mes "It looks like you";
								mes "chose the right switch.^000000";
								next;
								set .@blue,.@blue+1;
							}
							else {
								mes "^3355FF*Whiz Whiz*^000000";
								mes "^3355FF*Whiz Whiz*^000000";
								next;
								mes "^3355FFThe machine made some";
								mes "violent, erratic sounds,";
								mes "and vibrated violently";
								mes "before coming to a sudden";
								mes "stop. That was the wrong";
								mes "switch. You'd better ask Dorian...";
								changequest 3105,3106;
								set diamond_edq,11;
								close;
							}
							break;
						}
					}
					else if (.@switch_sound == 3) {
						mes "^3355FF*Bzzz Bzzz*";
						mes "*Bzzz Bzzz*";
						mes "The machine's vibrations";
						mes "are a bit more stable now.";
						mes "Which switch will you try?^000000";
						next;
						switch(select("Red Switch:Blue Switch:Yellow Switch:Green Switch")) {
						default:
							mes "^3355FF*Whiz Whiz*^000000";
							mes "^3355FF*Whiz Whiz*^000000";
							next;
							mes "^3355FFThe machine made some";
							mes "violent, erratic sounds,";
							mes "and vibrated violently";
							mes "before coming to a sudden";
							mes "stop. That was the wrong";
							mes "switch. You'd better ask Dorian...";
							changequest 3105,3106;
							set diamond_edq,11;
							close;
						case 3:
							if (.@yellow >= 0) {
								mes "^3355FF*Bzzzz Bzzzz*^000000";
								mes "^3355FF*Clang...!*^000000";
								next;
								mes "^3355FFThe machine's";
								mes "vibrations have";
								mes "grown stronger.";
								mes "It looks like you";
								mes "chose the right switch.^000000";
								next;
								set .@yellow,.@yellow+1;
							}
							else {
								mes "^3355FF*Whiz Whiz*^000000";
								mes "^3355FF*Whiz Whiz*^000000";
								next;
								mes "^3355FFThe machine made some";
								mes "violent, erratic sounds,";
								mes "and vibrated violently";
								mes "before coming to a sudden";
								mes "stop. That was the wrong";
								mes "switch. You'd better ask Dorian...";
								changequest 3105,3106;
								set diamond_edq,11;
								close;
							}
							break;
						}
					}
					else if (.@switch_sound == 4) {
						next;
						mes "^3355FF*Pzzzz Pzzz*";
						mes "*Pzzzz Pzzz*";
						mes "The machine is vibrating";
						mes "fairly strongly now. Which";
						mes "switch will you try?^000000";
						next;
						switch(select("Red Switch:Blue Switch:Yellow Switch:Green Switch")) {
						default:
							mes "^3355FF*Whiz Whiz*^000000";
							mes "^3355FF*Whiz Whiz*^000000";
							next;
							mes "^3355FFThe machine made some";
							mes "violent, erratic sounds,";
							mes "and vibrated violently";
							mes "before coming to a sudden";
							mes "stop. That was the wrong";
							mes "switch. You'd better ask Dorian...";
							changequest 3105,3106;
							set diamond_edq,11;
							close;
						case 4:
							if (.@green >= 0) {
								mes "^3355FFPzzzzz Pzzzz*^000000";
								mes "^3355FF*Clang...!*^000000";
								next;
								mes "^3355FFThe machine's";
								mes "vibrations have";
								mes "grown stronger.";
								mes "It looks like you";
								mes "chose the right switch.^000000";
								next;
								set .@green,.@green+1;
							}
							else {
								mes "^3355FF*Whiz Whiz*^000000";
								mes "^3355FF*Whiz Whiz*^000000";
								next;
								mes "^3355FFThe machine made some";
								mes "violent, erratic sounds,";
								mes "and vibrated violently";
								mes "before coming to a sudden";
								mes "stop. That was the wrong";
								mes "switch. You'd better ask Dorian...";
								changequest 3105,3106;
								set diamond_edq,11;
								close;
							}
							break;
						}
					}
				}
			}
		}
	}
	if (diamond_edq == 12) {
		mes "[Dorian]";
		mes "It sounds like you're";
		mes "done fixing my Mystic";
		mes "Dryer de Elegance.";
		mes "Why don't you come";
		mes "back so I can explain";
		mes "how you can use it?";
		close;
	}
	if ((diamond_edq == 13) && (countitem(7722) < 1)) {
		mes "^3355FFYou lift the main";
		mes "operational switch,";
		mes "turning on a light and";
		mes "opening a convenient";
		mes "slot. You insert the";
		mes "wet bond of debt.^000000";
		next;
		mes "^3355FFThe engine's pistons";
		mes "and cylinders churn";
		mes "with a lively din, and";
		mes "when the heater stops,";
		mes "the slot reopens.^000000";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "So this is the original";
		mes "bond of debt that Muff";
		mes "lost? It looks... Perfect!";
		changequest 3108,3109;
		getitem 7722,1; //Debt_Note
		close;
	}
	else if ((diamond_edq == 13) && (countitem(7722) > 0)) {
		mes "You already used this";
		mes "machine to restore the";
		mes "bond of debt. There's no";
		mes "need to mess around";
		mes "with it any longer.";
		close;
	}
	mes "[Dorian]";
	mes "Who are you?";
	mes "Don't you dare lay";
	mes "a hand on my precious";
	mes "masterpiece inventions! ";
	close;
}

// Broken Diamond
//============================================================
moc_ruins,152,147,5	script	Ibrahim	732,3,3,{
	if (checkweight(907,200) == 0) {
		mes "[Ibrahim]";
		mes "You're carrying too much";
		mes "stuff with you. Dump it";
		mes "in Kafra Storge, sell it,";
		mes "drop it, whatever, before";
		mes "you come talk to me, okay?";
		close;
	}
	if (diamond_edq < 14) {
		mes "[Ibrahim]";
		mes "Have you heard of the";
		mes "Four Cursed Jewels?";
		mes "I hear that one of them,";
		mes "the Diamond of Destruction,";
		mes "just appeared recently.";
		mes "What I'd do to find it...";
		close;
	}
	if ((diamond_edq == 14) && (BaseLevel > 65)) {
		mes "[Ibrahim]";
		mes "" + strcharinfo(0) + ", right?";
		mes "Don't be too suprised.";
		mes "I've heard of you here";
		mes "and there. Heh heh~!";
		next;
		while(1) {
			switch(select("How can I help you?:What'd you hear?:Bye!")) {
			case 1:
				mes "[Ibrahim]";
				mes "I wanted to ask you about";
				mes "the Four Cursed Jewels.";
				mes "All the merchants in the";
				mes "area are talking about them.";
				mes "Anyway, I was able to obtain";
				mes "the Diamond of Destruction!";
				next;
				mes "[" + strcharinfo(0) + "]";
				mes "(^666666Hmm... He might";
				mes "be talking about that";
				mes "jewel Muff gave to Belder";
				mes "as collateral for his debt.^000000)";
				next;
				mes "[Ibrahim]";
				mes "I paid a hefty sum for it,";
				mes "but then I lost it after";
				mes "just a few days. I kept it";
				mes "under high security, but";
				mes "that didn't stop the thief...";
				next;
				mes "[Ibrahim]";
				mes "It's a risky job, but";
				mes "I want to employ you to";
				mes "find where my diamond went.";
				mes "It'd be best if you could get";
				mes "it back, but I can understand";
				mes "if you can't. You up for it?";
				next;
				switch(select("Sure, I'll help you.:No, sorry.")) {
				case 1:
					mes "[Ibrahim]";
					mes "Great! I knew it was";
					mes "a good idea to find you.";
					mes "The thief didn't leave any";
					mes "clues, but he should have";
					mes "some problems selling the gem.";
					mes "It's one of a kind, you know.";
					next;
					mes "[Ibrahim]";
					mes "Visiting the large";
					mes "jewelry shops in the";
					mes "Rune-Midgarts Kingdom";
					mes "would be a good start.";
					mes "Their merchants know the";
					mes "most about those jewels.";
					next;
					mes "[Ibrahim]";
					mes "The thief will also have";
					mes "to sell the jewel to a jeweler";
					mes "that can afford astronomical";
					mes "prices. There are three big";
					mes "locations you should check.";
					next;
					mes "[Ibrahim]";
					mes "Find ^0000FFJhonnita^000000 in Alberta,";
					mes "^0000FFKimeunbang^000000 in Payon, and";
					mes "^0000FFLeblo^000000 in Geffen. They wanted ";
					mes "that diamond badly too, so";
					mes "they'll remember if they";
					mes "heard any clues about it.";
					next;
					mes "[Ibrahim]";
					mes "Please don't let them";
					mes "know I've hired you to";
					mes "investigate that diamond.";
					mes "They won't speak to you";
					mes "if they figure out that";
					mes "I've sent you to them.";
					next;
					mes "[Ibrahim]";
					mes "Well, I'll be right";
					mes "here. I hope you come";
					mes "back with some good news.";
					setquest 3110;
					set diamond_edq,15;
					close;
				case 2:
					mes "[Ibrahim]";
					mes "Really? I went through";
					mes "a lot of trouble to find";
					mes "you, but... I understand.";
					mes "If you have a change of";
					mes "heart, I'll be willing to";
					mes "hire you for your expertise.";
					close;
				}
			case 2:
				if ((BaseJob == Job_Novice) || (BaseJob == Job_Super_Novice)) {
					mes "[Ibrahim]";
					mes "So it's true...";
					mes "People say you're";
					mes "just a Novice, but";
					mes "you still help them";
					mes "out with their problems.";
					close;
				}
				else if (BaseClass == Job_Swordman) {
					mes "[Ibrahim]";
					mes "I've got a few buddies";
					mes "in the Swordman Association.";
					mes "They dropped your name when";
					mes "I mentioned I was looking";
					mes "for someone smart and strong.";
					close;
				}
				else if (BaseClass == Job_Crusader) {
					mes "[Ibrahim]";
					mes "Heh, so you really";
					mes "are one of those holy";
					mes "knights. I'm glad to";
					mes "have met you for myself.";
					close;
				}
				else if (BaseClass == Job_Acolyte) {
					mes "[Ibrahim]";
					mes "I didn't think a member";
					mes "of the clergy would be so";
					mes "strong, but you know, your";
					mes "reputation precedes you.";
					close;
				}
				else if (BaseClass == Job_Merchant) {
					mes "[Ibrahim]";
					mes "Hey, if I didn't know";
					mes "your name, how could I call";
					mes "myself a Merchant? You set";
					mes "an example for us all~";
					close;
				}
				else if (BaseClass == Job_Blacksmith) {
					mes "[Ibrahim]";
					mes "We have a mutual friend,";
					mes "you know that? Aragham";
					mes "really spoke highly of you.";
					close;
				}
				else if (BaseClass == Job_Alchemist) {
					mes "[Ibrahim]";
					mes "I know a guy who knows";
					mes "a guy in the Alchemist Guild.";
					mes "Around their parts, it sounds";
					mes "like you're pretty hot stuff.";
					close;
				}
				else if (BaseClass == Job_Magician) {
					mes "[Ibrahim]";
					mes "Magic is totally beyond";
					mes "my understanding, but";
					mes "if it gets the job done,";
					mes "it can't be all bad.";
					mes "I hear you're really";
					mes "good at that stuff.";
					close;
				}
				else if (BaseClass == Job_Thief) {
					mes "[Ibrahim]";
					mes "Heh, it sounds like almost";
					mes "anyone who's anyone in";
					mes "Morocc knows who you are.";
					mes "Pleasure to make your";
					mes "acquaintance~";
					close;
				}
				else if (BaseClass == Job_Archer) {
					mes "[Ibrahim]";
					mes "Anyone who's used";
					mes "a bow seems to drop";
					mes "your name when it comes";
					mes "to accuracy and heroism.";
					mes "Hard to believe that I'm";
					mes "actually talking to you~";
					close;
				}
				else if (BaseClass == Job_Bard) {
					mes "[Ibrahim]";
					mes "Is it really true?";
					mes "I hear you sing";
					mes "like an angel...";
					close;
				}
				else if (BaseClass == Job_Dancer) {
					mes "[Ibrahim]";
					mes "Is it really true?";
					mes "I hear you dance";
					mes "like the devil...";
					close;
				}
				else if ((Class == Job_Star_Gladiator) || (Class == Job_Star_Gladiator2)) {
					mes "[Ibrahim]";
					mes "Me and your old master,";
					mes "Phoenix, we go way back.";
					mes "He had nothing but pride";
					mes "when he talked about you~";
					close;
				}
				else if (Class == Job_Soul_Linker) {
					mes "[Ibrahim]";
					mes "I heard your name from";
					mes "this spiritual message";
					mes "I got from a dream. That";
					mes "doesn't sound crazy to";
					mes "you, does it?";
					close;
				}
			case 3:
				mes "[Ibrahim]";
				mes "Aren't you the least";
				mes "bit curious how I know";
				mes "about you? Wait, come back!";
				mes "I'm not really that suspicious!";
				close;
			}
		}
	}
	if (diamond_edq == 15) {
		mes "[Ibrahim]";
		mes "Find ^0000FFJhonnita^000000 in Alberta,";
		mes "^0000FFKimeunbang^000000 in Payon, and";
		mes "^0000FFLeblo^000000 in Geffen. They wanted ";
		mes "that diamond badly too, so";
		mes "they'll remember if they";
		mes "heard any clues about it.";
		next;
		mes "[Ibrahim]";
		mes "Please don't let them";
		mes "know I've hired you to";
		mes "investigate that diamond.";
		mes "They won't speak to you";
		mes "if they figure out that";
		mes "I've sent you to them.";
		close;
	}
	if ((diamond_edq > 15) && (diamond_edq < 25)) {
		mes "[Ibrahim]";
		mes "Are you still investigating";
		mes "the diamond's whereabouts?";
		mes "Well, I hope you can bring";
		mes "me back good news soon.";
		close;
	}
	if (diamond_edq == 25) {
		mes "[Ibrahim]";
		mes "Oh, you're back.";
		mes "Made any headway in";
		mes "your investigation of the";
		mes "Diamond of Destruction?";
		next;
		select("Explain What You Learned");
		mes "[Ibrahim]";
		mes "What?! The Z Gang stole";
		mes "my diamond? No wonder!";
		mes "I bet they wanted to use its";
		mes "curse for something evil.";
		mes "How about the jewel?";
		mes "Did you retrieve it?";
		next;
		select("Give Diamond of Destruction");
		mes "[Ibrahim]";
		mes "Oh! Great! So it was";
		mes "in the Z Gang's hideout?";
		mes "Wow, even after hearing so";
		mes "much about you, you still";
		mes "exceeded my expectations!";
		next;
		delitem 7723,1; //Diamond_Of_Ruin
		mes "[Ibrahim]";
		mes "Thank God, I finally";
		mes "have this back! Ah, and";
		mes "as for your payment...";
		mes "Well, I don't know if";
		mes "it's enough for what you";
		mes "did, but I hope you like it.";
		completequest 3118;
		set diamond_edq,26;
		getitem 732,2; //Crystal_Jewel__
		getitem 603,1; //Old_Blue_Box
		next;
		mes "[Ibrahim]";
		mes "Now that I have the";
		mes "Diamond of Destruction";
		mes "again, I can research";
		mes "more about its strange";
		mes "properties. I'll contact you";
		mes "again if I need your help.";
		close;
	}
	if (diamond_edq == 26) {
		if (jewel_nd == 0) {
			if ((treasure_nd > 10) && (BaseLevel > 65)) {
				mes "[Ibrahim]";
				mes "Ah, I hear from a friend";
				mes "in Morocc that you happened";
				mes "to obtain the Unlucky Emerald.";
				mes "I'm sure it's one of the Four";
				mes "Cursed Jewels. You wouldn't";
				mes "mind if I look at it, do you?";
				next;
				switch(select("No!:Sure, why not?")) {
				case 1:
					mes "[Ibrahim]";
					mes "No? Well, I'm sure you";
					mes "have your reasons. Still,";
					mes "maybe I can learn something";
					mes "that would benefit both of";
					mes "us if I examined it.";
					close;
				case 2:
					mes "[Ibrahim]";
					mes "Ah, I appreciate it!";
					mes "Now, let's see here...";
					next;
					if (countitem(7725) > 0) {
						mes "[Ibrahim]";
						mes "Yes, judging from its";
						mes "characteristic shape, this";
						mes "is the cursed emerald.";
						mes "Like me, you now possess";
						mes "one of the Four Cursed Jewels.";
						next;
					}
					else {
						mes "[Ibrahim]";
						mes "Hm? Did you happen to";
						mes "misplace your emerald?";
						mes "I mean, it's not with you...";
						close;
					}
				}
				mes "[Ibrahim]";
				mes "Tell me, what were";
				mes "you planning to do with";
				mes "the Unlucky Emerald?";
				next;
				switch(select("I'm going to sell it.:I'm going to study it.")) {
				case 1:
					mes "[Ibrahim]";
					mes "Oh, you weren't going";
					mes "to keep it? I wish I could";
					mes "afford to buy it from you...";
					mes "But I've had enough trouble";
					mes "buying the Diamond of";
					mes "Destruction.";
					next;
					mes "[Ibrahim]";
					mes "You already met several";
					mes "renown jewelers while";
					mes "you were looking for the";
					mes "dimaond. Why don't you ask";
					mes "them if they'll buy that";
					mes "Unlucky Emerald from you?";
					set jewel_nd,1;
					close;
				case 2:
					mes "[Ibrahim]";
					mes "Ah, I applaud your";
					mes "thirst for knowledge,";
					mes "your quest to seek truth.";
					mes "If you really want an expert";
					mes "to look at the emerald, talk";
					mes "to this scholar in Comodo.";
					next;
					mes "[Ibrahim]";
					mes "This man is an antique";
					mes "appraiser, an specialist in";
					mes "ancient artifacts and treasure.";
					mes "I highly recommend consulting";
					mes "him since he's been invaluable";
					mes "in my own jewel research.";
					set jewel_nd,10;
					close;
				}
			}
			else if ((treasure_nd > 10) && (BaseLevel < 66)) {
				mes "[Ibrahim]";
				mes "I heard that you obtained";
				mes "the Unlucky Emerald from";
				mes "a friend in Morocc. Let me";
				mes "warn you that you might not";
				mes "be strong enough to handle";
				mes "its power. Be careful.";
				next;
				mes "[Ibrahim]";
				mes "If you're not wary,";
				mes "its curse might destroy";
				mes "you. Understand?";
				close;
			}
			else {
				mes "[Ibrahim]";
				mes "Thank you for all";
				mes "you've done for me. I'll";
				mes "continue my research in the";
				mes "cursed jewel's powers, and";
				mes "I'll contact you if I need your";
				mes "help again. Take care~";
				close;
			}
		}
		else if (jewel_nd == 1) {
			mes "[Ibrahim]";
			mes "I don't have the funds";
			mes "to buy your emerald at";
			mes "a fair price, but why don't";
			mes "you ask the other jewelers?";
			mes "They might be interested";
			mes "in buying your jewel.";
			close;
		}
		else if (jewel_nd == 2) {
			mes "[Ibrahim]";
			mes "Ah, you're back~";
			mes "So were you able to";
			mes "sell the Unlucky Emerald?";
			next;
			select("Everyone seems scared!");
			mes "[Ibrahim]";
			mes "Ah, I see now. They";
			mes "must be too afraid of";
			mes "that particular jewel's";
			mes "curse. Huh. Weird.";
			next;
			mes "[Ibrahim]";
			mes "I guess the Diamond of";
			mes "Destruction is considered";
			mes "pretty safe since it's been";
			mes "around for a while without";
			mes "any strange incidents.";
			next;
			mes "[Ibrahim]";
			mes "You just dug up the";
			mes "emerald? I suppose it";
			mes "might be too soon to sell it.";
			mes "Not too many people will";
			mes "test their luck with that gem,";
			mes "now that I think about it.";
			next;
			mes "[Ibrahim]";
			mes "Ah, here's an idea.";
			mes "Why don't you ask my";
			mes "scholar friend in Comodo";
			mes "to examine it? Maybe you";
			mes "can learn more about the";
			mes "emerald's so-called curse.";
			next;
			mes "[Ibrahim]";
			mes "I'll go ahead and send";
			mes "him a message ahead of";
			mes "time so that he'll know to";
			mes "expect your arrival, okay?";
			set jewel_nd,9;
			close;
		}
		else if ((jewel_nd == 9) || (jewel_nd == 10)) {
			mes "[Ibrahim]";
			mes "Why don't you visit my";
			mes "scholar friend in Comodo,";
			mes "and ask him to examine your";
			mes "Unlucky Emerald? The more";
			mes "you learn about that so-called";
			mes "curse, the better. Good luck!";
			close;
		}
		else {
			mes "[Ibrahim]";
			mes "Oh, so what have you";
			mes "learned about the Unlucky";
			mes "Emerald so far? Hmmm. Yes,";
			mes "very intriguing. Good luck";
			mes "with your research efforts~";
			close;
		}
	}
	mes "[Ibrahim]";
	mes "Have you heard of";
	mes "the Four Cursed Jewels?";
	mes "I wish to retrieve my";
	mes "Diamond of Destruction,";
	mes "but whoever has it has";
	mes "kept it well hidden...";
	close;

OnTouch:
if ((diamond_edq == 14) && (BaseLevel > 65)) {
	mes "[Ibrahim]";
	mes "Psst, adventurer!";
	mes "You're " + strcharinfo(0) + ", right?";
		mes "There's something";
		mes "I'd like to discuss";
		mes "with you in private!";
		close;
	}
	end;
}

alberta,102,80,3	script	Jhonnita	86,{
	if ((diamond_edq == 15) || (diamond_edq == 16)) {
		mes "[Jhonnita]";
		mes "Oh, it's always nice";
		mes "to see a new face~";
		mes "So were you interested";
		mes "in selling or buying";
		mes "a jewel? We only carry";
		mes "the best. Hahahaha!";
		next;
		switch(select("What's the most popular jewel?:Do you know Diamond of Destruction?")) {
		case 1:
			mes "[Jhonnita]";
			mes "Ah, take a look at this";
			mes "lustrous ruby. Isn't it";
			mes "just breathtaking? It can";
			mes "be yours for the specially";
			mes "discounted price of just";
			mes "1,000,000 zeny!";
			next;
			mes "[Jhonnita]";
			mes "Wait, where are you--?";
			mes "Damn, I shouldn't have";
			mes "marked it up so much.";
			mes "Rookie mistake, rookie";
			mes "mistake! I'm a professional!";
			close;
		case 2:
			mes "[Jhonnita]";
			mes "Diamond of Destruction?";
			mes "Heh heh! A rookie like you";
			mes "wouldn't appreciate its true";
			mes "value. I wanted it so badly,";
			mes "but that Ibrahim was much";
			mes "luckier that I was...";
			next;
			mes "[Jhonnita]";
			mes "It makes me so upset to";
			mes "think about how I lost to";
			mes "him. All the big jewelers";
			mes "are trying to snatch up";
			mes "the Four Cursed Jewels...";
			mes "But only one showed up so far.";
			close;
		}
	}
	else if (diamond_edq == 26) {
		if (jewel_nd == 0) {
			mes "[Jhonnita]";
			mes "Hey. You look familiar.";
			mes "You're not one of my";
			mes "regulars, are you?";
			mes "No, couldn't be.";
			mes "You're not dressed";
			mes "richly enough. No offense.";
			close;
		}
		else if (jewel_nd == 1) {
			mes "[Jhonnita]";
			mes "So you here to sell";
			mes "any Garlets, or did you";
			mes "come to spend your life";
			mes "savings on my jewels?";
			next;
			switch(select("Show me some jewels...:I have a rare emerald to sell...")) {
			case 1:
				mes "[Jhonnita]";
				mes "It couldn't hurt to";
				mes "show you my wares, but...";
				mes "I doubt you can afford them.";
				mes "Jewels aren't cheap, you know?";
				next;
				if (Sex == SEX_MALE) {
					mes "[Jhonnita]";
					mes "Oh, I get it now. You want";
					mes "to give one as a present to";
					mes "some woman. Isn't that typical?";
					mes "Can't you come up with a more";
					mes "creative way to buy some";
					mes "woman's love? Huh?";
					close;
				}
				else {
					mes "[Jhonnita]";
					mes "Sorry, sweetheart, but";
					mes "I have a strict ''no window";
					mes "shopping'' policy. ";
					close;
				}
			case 2:
				if (countitem(7725) > 0) {
					mes "[Jhonnita]";
					mes "Rare emerald, eh?";
					mes "It better not be something";
					mes "like a Garlet or a Zargon!";
					mes "Sometimes you guys make";
					mes "that kind of mistake.";
					mes "Okay, let's see...";
					next;
					mes "[Jhonnita]";
					mes "......";
					mes "Umm...";
					next;
					mes "[Jhonnita]";
					mes "...........................Um.";
					mes "It's an incredibly nice";
					mes "emerald, but I can't take";
					mes "this off your hands. Er,";
					mes "would you be on your way?";
					next;
					mes "[" + strcharinfo(0) + "]";
					mes "Huh? But why?";
					next;
					mes "[Jhonnita]";
					mes "J-just get out of my";
					mes "shop! Go... Go to Payon";
					mes "and talk to Kimeunbang!";
					mes "Y-yeah, and don't come back!";
					set jewel_nd,2;
					close;
				}
				else {
					mes "[Jhonnita]";
					mes "What'd I tell you, huh?";
					mes "I don't want you here";
					mes "in my shop anymore!";
					close;
				}
			}
		}
		else if (jewel_nd == 2) {
			mes "[Jhonnita]";
			mes "Look, I'll level with you.";
			mes "Rumors spread fast amongst";
			mes "us jewelers. Nobody will get";
			mes "near you if they know you";
			mes "have that jewel. You get it";
			mes "now? Please... Stay back...";
			close;
		}
		else {
			mes "[Jhonnita]";
			mes "I don't want anything";
			mes "to do with your weird";
			mes "emerald or with you.";
			mes "Aren't you leaving";
			mes "already? Go away!";
			close;
		}
	}
	else {
		mes "[Jhonnita]";
		mes "Are you here to buy or";
		mes "sell any jewels? I always";
		mes "buy high and sell low.";
		mes "Come to me for all of";
		mes "your gem related needs~";
		close;
	}
}

payon,69,65,5	script	Kimeunbang	89,{
	if (diamond_edq == 15) {
		mes "[Kimeunbang]";
		mes "Zzzz... Huh?";
		mes "Why'd you wake me?";
		mes "Such a good dream too.";
		mes "Go to another jewelry shop,";
		mes "I don't feel like opening up.";
		next;
		switch(select("What's wrong?:Diamond of Destruction?")) {
		case 1:
			mes "[Kimeunbang]";
			mes "I'm just disappointed.";
			mes "I was so close to getting";
			mes "that Diamond of Destruction.";
			mes "So close! Belder, my business";
			mes "partner, actually snatched";
			mes "it up from some poor sap.";
			next;
			mes "[Kimeunbang]";
			mes "Somehow, Belder ended up";
			mes "returning the jewel. I was";
			mes "going to buy the jewel from";
			mes "the original owner, but then";
			mes "this upstart jeweler got to";
			mes "it before I could. Damn it!";
			next;
			mes "[Kimeunbang]";
			mes "This was a monumental";
			mes "failure. I haven't been";
			mes "able to sleep, I don't want";
			mes "to work. Ugh. Well, thanks";
			mes "for letting me get that off";
			mes "my chest. I feel a bit better.";
			next;
			mes "[Kimeunbang]";
			mes "Here, take this as";
			mes "a little gift. Not all of us";
			mes "jewelers are as greedy";
			mes "as you think we are. Heh.";
			getitem 722,1; //Scarlet_Jewel
			set diamond_edq,16;
			close;
		case 2:
			mes "[Kimeunbang]";
			mes "Oh! That diamond is the";
			mes "very reason for my agony!";
			mes "I was so close to getting it...";
			mes "But then someone got to the";
			mes "diamond before I did. Argh!";
			close;
		}
	}
	else if (diamond_edq == 26) {
		if (jewel_nd == 0) {
			mes "[Kimeunbang]";
			mes "I don't feel like opening";
			mes "my shop any time soon, but";
			mes "you can always come back later.";
			close;
		}
		else if (jewel_nd == 1) {
			mes "[Kimeunbang]";
			mes "Oh, hello. Looks like";
			mes "I'm back in business~";
			next;
			switch(select("I have a jewel to sell.:How are you?")) {
			case 1:
				mes "[Kimeunbang]";
				mes "Really? Alright, just";
				mes "give me a minute to";
				mes "appraise your gem.";
				next;
				if (countitem(7725) > 0) {
					mes "[Kimeunbang]";
					mes "...........";
					mes "...........";
					next;
					mes "[Kimeunbang]";
					mes "I'm sorry, but I just";
					mes "can't buy this emerald.";
					mes "It's... It's too big of";
					mes "a risk. The rumors I keep";
					mes "hearing, they're just horrible.";
					set jewel_nd,2;
					close;
				}
				else {
					mes "[Kimeunbang]";
					mes "Hm? Where's this";
					mes "jewel you were talking";
					mes "about? Did you forget";
					mes "to bring it with you?";
					close;
				}
				break;
			case 2:
				mes "[Kimeunbang]";
				mes "I don't even feel like";
				mes "talking about my business.";
				mes "I've been considering closing";
				mes "up shop for good, actually.";
				close;
			}
		}
		else if (jewel_nd == 2) {
			mes "[Kimeunbang]";
			mes "No jeweler around will";
			mes "buy that cursed emerald.";
			mes "I'm really sorry. You can";
			mes "try talking to Ibrahim, though.";
			mes "He's the one that got that";
			mes "Diamond of Destruction.";
			close;
		}
		else {
			mes "[Kimeunbang]";
			mes "Ugh... The stress";
			mes "of the jewel business";
			mes "is finally getting to me.";
			mes "Oh well, I've had a good run...";
			close;
		}
	}
	else {
		mes "[Kimeunbang]";
		mes "I don't feel like opening";
		mes "my shop any time soon, but";
		mes "you can always come back later.";
		close;
	}
}

geffen,128,153,3	script	Leblo	883,{
	if ((diamond_edq == 15) || (diamond_edq == 16)) {
		mes "[Leblo]";
		mes "Oh, I've heard about";
		mes "you. You're the one that's";
		mes "been asking questions about";
		mes "the Diamond of Destruction.";
		next;
		emotion ET_HUK, playerattached();
		mes "[Leblo]";
		mes "Don't be so suprised:";
		mes "word travels fast in the";
		mes "jewel industry. Well, if you";
		mes "want to learn more, you'll";
		mes "have to do something for";
		mes "me first. We have a deal?";
		next;
		switch(select("Deal.:No, thanks.")) {
		case 1:
			mes "[Leblo]";
			mes "I've been suffering back";
			mes "pain lately, and none of";
			mes "the medicines do any good.";
			mes "I resorted to seeing this";
			mes "famous doctor in Payon, but";
			mes "then I totally screwed it up.";
			next;
			mes "[Leblo]";
			mes "She was such a nag, and";
			mes "I said something... unpleasant";
			mes "to her. Now she refuses to";
			mes "examine me! But if I sent";
			mes "you, then you could get";
			mes "some medicine for me.";
			next;
			mes "[Leblo]";
			mes "She knows my symptoms";
			mes "so if you make her happy,";
			mes "she'll give you my medicine...";
			mes "I hope. Anyway, her name is";
			mes "^0000FFWola^000000, and she's in Payon.";
			changequest 3110,3111;
			set diamond_edq,17;
			close;
		case 2:
			mes "[Leblo]";
			mes "Oh, yeah? Well, that's";
			mes "fine with me. I don't";
			mes "have anything to lose.";
			close;
		}
	}
	if (diamond_edq == 17) {
		mes "[Leblo]";
		mes "Please look for a female";
		mes "doctor named ^0000FFWola^000000 in Payon.";
		mes "Do what you can to get my";
		mes "medicine from her... Oh,";
		mes "and try to be nice~ ";
		close;
	}
	if (diamond_edq == 21) {
		mes "[Leblo]";
		mes "Oh? Is that... Is that my";
		mes "medicine? Thank goodness!";
		mes " Hm? Oh, so I misunderstood";
		mes " her. I thought she was awfully";
		mes " arrogant, but she was just";
		mes " concerned for my health.";
		next;
		mes "[Leblo]";
		mes "I better make it a point";
		mes "to properly apologize to";
		mes "her the next time I see her.";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "...............................";
		mes "...............................";
		mes "...............................";
		next;
		mes "[Leblo]";
		mes "Right, I need to tell";
		mes "you about the Diamond of";
		mes "Destruction. Listen carefully.";
		mes "A while ago, a young couple";
		mes "tried to sell me that very";
		mes "jewel. Suspicious, eh?";
		next;
		mes "[Leblo]";
		mes "I had no interest in";
		mes "buying it, so I actually";
		mes "offered them a much lower";
		mes "price than they hoped.";
		mes "Now that I think about it...";
		next;
		mes "[Leblo]";
		mes "That man and woman looked";
		mes "like the members of that";
		mes "infamous Z Gang. Heh!";
		mes "I should have reported it,";
		mes "but... You know. Back";
		mes "pain and all that.";
		next;
		mes "[Leblo]";
		mes "I hear the Rogue Guild";
		mes "is secretly investigating";
		mes "the Z Gang, so that couple";
		mes "must have plenty of enemies.";
		mes "So... Yeah. The Z Gang has";
		mes "the Diamond of Destruction.";
		next;
		mes "[Leblo]";
		mes "You should talk with one";
		mes "of the investigators of the";
		mes "Rogue Guild if you want to";
		mes "learn more about the Z Gang.";
		mes "Pretty useful info, huh?";
		mes "Take care, my friend.";
		changequest 3114,3115;
		set diamond_edq,22;
		close;
	}
	if (diamond_edq == 22) {
		mes "[Leblo]";
		mes "You should talk with one";
		mes "of the investigators of the";
		mes "Rogue Guild if you want to";
		mes "learn more about the Z Gang.";
		mes "Pretty useful info, huh?";
		mes "Take care, my friend.";
		close;
	}
	if (diamond_edq == 26) {
		if (jewel_nd == 0) {
			mes "[Leblo]";
			mes "Hm? I'm sorry, but";
			mes "I'm pretty busy with";
			mes "all my other customers.";
			mes "Besides, you don't strike";
			mes "me as a jewel seller or";
			mes "buyer. Please excuse me...";
			close;
		}
		else if (jewel_nd == 1) {
			mes "[Leblo]";
			mes "Oh, hey. Did you";
			mes "have any business";
			mes "with me today?";
			next;
			switch(select("I have a jewel to sell.:No, not really.")) {
			case 1:
				mes "[Leblo]";
				mes "Oh, yeah? Well then,";
				mes "I'm curious now. You";
				mes "mind if I take a look?";
				next;
				if (countitem(7725) > 0) {
					mes "[Leblo]";
					mes "Now, what do we ha--";
					mes "Oh. I'm sorry, but I just";
					mes "can't buy this. I hate to";
					mes "break it to you, but I hear";
					mes "that thing has an awful curse.";
					mes "See what you can do about that!";
					set jewel_nd,2;
					close;
				}
				else {
					mes "[Leblo]";
					mes "Hmm. The jewel";
					mes "you were just";
					mes "talking about...";
					mes "Did you remember";
					mes "to bring it with you?";
					close;
				}
			case 2:
				mes "[Leblo]";
				mes "Oh, yeah? Well, that's";
				mes "fine too. No skin off";
				mes "my nose, that's what";
				mes "I always say.";
				close;
			}
		}
		else if (jewel_nd == 2) {
			mes "[Leblo]";
			mes "Oh. I'm sorry, but I just";
			mes "can't buy this. I hate to";
			mes "break it to you, but I hear";
			mes "that thing has an awful curse.";
			mes "See what you can do about that!";
			close;
		}
		else {
			mes "[Leblo]";
			mes "I'm sorry that you're";
			mes "the one that currently";
			mes "has that jewel. Hopefully,";
			mes "the curse is just a rumor...";
			mes "But, well, I didn't get this";
			mes "old by taking chances...";
			close;
		}
	}
	mes "[Leblo]";
	mes "Hello, are you interested";
	mes "in buying or selling any";
	mes "precious jewels?";
	close;
}

payon_in03,167,149,3	script	Wola	75,2,2,{
	if (checkweight(907,1) == 0) {
		mes "^3355FFWait a second!";
		mes "Right now, you're carrying";
		mes "too many things with you.";
		mes "Please come back after";
		mes "using the Kafra Service";
		mes "to store some of your items.^000000";
		close;
	}
	if (diamond_edq == 18) {
		mes "[" + strcharinfo(0) + "]";
		mes "Hello? I'm here to";
		mes "see Doctor Wola.";
		next;
		mes "[Wola]";
		mes "Oh... Ah... Um...";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "Excuse me, but do you";
		mes "know where I can find her?";
		next;
		mes "[Wola]";
		mes "I, um...";
		mes "Oh no! Wah!";
		mes "See what you did?!";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "I... I don't know";
		mes "what you're talking";
		mes "about. I'm just wanted";
		mes "to talk to Doctor Wola.";
		next;
		mes "[Wola]";
		mes "I'm Doctor Wola!";
		mes "And look! You made me";
		mes "drop the herbal medicine";
		mes "I was brewing! The pot";
		mes "is all broken now...";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "Huh? Oh, I'm...";
		mes "I'm really sorry.";
		next;
		mes "[Wola]";
		mes "That's... That's";
		mes "quite alright. It's";
		mes "my fault, really.";
		mes "Waaah~! My pot!";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "I'm sorry, I don't know";
		mes "what's going on exactly.";
		mes "Was that pot expensive?";
		next;
		mes "[Wola]";
		mes "Oh, I'm acting stupid";
		mes "again! I don't have time";
		mes "to cry over a broken pot!";
		mes "I need to check my hands";
		mes "for any cuts! If they're";
		mes "hurt, I can't help anyone!";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "That's, uh, that";
		mes "sounds pretty serious.";
		mes "So how are your hands?";
		mes "They don't hurt, do they?";
		next;
		mes "[Wola]";
		mes "Oooh, never mind that!";
		mes "They're fine, thank goodness.";
		mes "What's important now is that";
		mes "you help me fix my herbal";
		mes "medicine pot. You're partly";
		mes "responsible, you know.";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "Okay. Just tell";
		mes "me what I should do.";
		mes "Hold on, I came here to";
		mes "ask you if you'd give me";
		mes "some medicine. You see--";
		next;
		mes "[Wola]";
		mes "Perfect! All the more";
		mes "reason you should help";
		mes "me fix this pot! I can't";
		mes "make any medicine without";
		mes "this pot now, can I? Let's";
		mes "see... I'll need glue, glue...";
		next;
		mes "[Wola]";
		mes "Hurry and get";
		mes "^0000FF20 Fine Sands^000000,";
		mes "^0000FF5 Empty Bottles^000000,";
		mes "^0000FF10 Brigans^000000, and";
		mes "^0000FF10 Soft Blades of Grass^000000.";
		mes "There's no time to waste!";
		next;
		mes "["+ strcharinfo(0) +"]";
		mes "You... You got it.";
		changequest 3111,3112;
		set diamond_edq,19;
		close;
	}
	if (diamond_edq == 19) {
		mes "[Wola]";
		mes "Good, you're back.";
		mes "So you brought everything";
		mes "I need to glue my pot";
		mes "back together again?";
		next;
		if ((countitem(7043) > 19) && (countitem(7054) > 9) && (countitem(7194) > 9) && (countitem(713) > 4)) {
			mes "[Wola]";
			mes "Great. Give them here.";
			mes "I should grind this, mix";
			mes "that... Wait, was that right?";
			mes "What were these for again?";
			mes "Argh, I'm acting stupid again!";
			mes "You mind not watching me?";
			delitem 7043,20; //Fine_Sand
			delitem 7054,10; //Brigan
			delitem 7194,10; //Soft_Leaf
			delitem 713,5; //Empty_Bottle
			next;
			mes "[Wola]";
			mes "Just... Just sit in";
			mes "that corner, don't say";
			mes "anything, and wait till";
			mes "I say I'm done, okay?!";
			next;
			mes "[Wola]";
			mes "...............................";
			mes "...............................";
			mes "...............................";
			next;
			mes "[Wola]";
			mes "...............................";
			mes "...............................";
			mes "...............................";
			mes "...............................";
			next;
			mes "[Wola]";
			mes "...............................";
			mes "...............................";
			mes "...............................";
			mes "...............................";
			mes "...............................";
			next;
			mes "[Wola]";
			mes "There. It's finally";
			mes "fixed. Oh wait, did you";
			mes "say something about needing";
			mes "some medicine? I completely";
			mes "forgot about that for a while.";
			mes "What did you say exactly?";
			changequest 3112,3113;
			set diamond_edq,20;
			next;
			switch(select("Will you go out with me?:Leblo's back medicine?")) {
			case 1:
				if (Sex == SEX_MALE) {
					mes "[Wola]";
					mes "^666666*Blush*^000000 But it's too";
					mes "early for us to just go";
					mes "on a date. I'm sorry, it's...";
					mes "I have someone in mind.";
					mes "Still, I'm flattered that";
					mes "you think of me that way.";
					close;
				}
				else {
					mes "[Wola]";
					mes "Wow, I can't believe";
					mes "I'm turning you down--";
					mes "a little surprised,";
					mes "actually. But yes.";
					mes "I have someone I like.";
					mes "And it's not really you. Sorry.";
					close;
				}
			case 2:
				mes "[Wola]";
				mes "Leblo... Oh, that";
				mes "man in Geffen? Finally...";
				mes "He has the sense to send";
				mes "someone to get his meds.";
				mes "I already prepared it.";
				mes "Where did I put it now?";
				next;
				mes "[Wola]";
				mes "I want to double check";
				mes "his medicine first so";
				mes "please tell him that I'll";
				mes "have it delivered to him";
				mes "shortly. In a while, maybe?";
				next;
				mes "[" + strcharinfo(0) + "]";
				mes "Um, you're not mad or";
				mes "anything? I gathered that";
				mes "you didn't like him much...";
				next;
				mes "[Wola]";
				mes "What are saying?";
				mes "I'm a doctor: we're duty";
				mes "bound to heal everyone we can!";
				mes "That's not to say that some";
				mes "patients suffer longer and";
				mes "more painful treatment...";
				next;
				mes "[" + strcharinfo(0) + "]";
				mes "(^666666She hates him!";
				mes "But I guess Leblo's life";
				mes "should be okay in her hands.^000000)";
				next;
				mes "[Wola]";
				mes "Alright, just let Leblo";
				mes "know that I'm taking care";
				mes "of him, that his life should";
				mes "be okay in my hands. I need";
				mes "to get back to work: please";
				mes "take care of yourself!";
				changequest 3113,3114;
				set diamond_edq,21;
				close;
			}
		}
		else {
			mes "[Wola]";
			mes "Hurry and get";
			mes "^0000FF20 Fine Sands^000000,";
			mes "^0000FF5 Empty Bottles^000000,";
			mes "^0000FF10 Brigans^000000, and";
			mes "^0000FF10 Soft Blades of Grass^000000.";
			mes "There's no time to waste!";
			next;
			mes "["+ strcharinfo(0) +"]";
			mes "You... You got it.";
			close;
		}
	}
	if (diamond_edq == 20) {
		mes "[Wola]";
		mes "Sorry, I completely";
		mes "missed what you said";
		mes "earlier. Something about";
		mes "needing medicine from me?";
		next;
		switch(select("Will you go out with me?:Leblo's back medicine?")) {
		case 1:
			if (Sex == SEX_MALE) {
				mes "^666666*Blush*^000000 But it's too";
				mes "early for us to just go";
				mes "on a date. I'm sorry, it's...";
				mes "I have someone in mind.";
				mes "Still, I'm flattered that";
				mes "you think of me that way.";
				close;
			}
			else {
				mes "[Wola]";
				mes "Wow, I can't believe";
				mes "I'm turning you down--";
				mes "a little surprised,";
				mes "actually. But yes.";
				mes "I have someone I like.";
				mes "And it's not really you. Sorry.";
				close;
			}
		case 2:
			mes "[Wola]";
			mes "Leblo... Oh, that";
			mes "man in Geffen? Finally...";
			mes "He has the sense to send";
			mes "someone to get his meds.";
			mes "I already prepared it.";
			mes "Where did I put it now?";
			next;
			mes "[Wola]";
			mes "I want to double check";
			mes "his medicine first so";
			mes "please tell him that I'll";
			mes "have it delivered to him";
			mes "shortly. In a while, maybe?";
			next;
			mes "[" + strcharinfo(0) + "]";
			mes "Um, you're not mad or";
			mes "anything? I gathered that";
			mes "you didn't like him much...";
			next;
			mes "[Wola]";
			mes "What are saying?";
			mes "I'm a doctor: we're duty";
			mes "bound to heal everyone we can!";
			mes "That's not to say that some";
			mes "patients suffer longer and";
			mes "more painful treatment...";
			next;
			mes "[" + strcharinfo(0) + "]";
			mes "(^666666She hates him!";
			mes "But I guess Leblo's life";
			mes "should be okay in her hands.^000000)";
			next;
			mes "[Wola]";
			mes "Alright, just let Leblo";
			mes "know that I'm taking care";
			mes "of him, that his life should";
			mes "be okay in my hands. I need";
			mes "to get back to work: please";
			mes "take care of yourself!";
			changequest 3113,3114;
			set diamond_edq,21;
			close;
		}
	}
	mes "[Wola]";
	mes "Food has a dramatic";
	mes "effect on your body:";
	mes "treat it like a drug, and";
	mes "watch what you eat. Oh,";
	mes "and do your research!";
	next;
	mes "[Wola]";
	mes "You know, your lifestyle";
	mes "and habits determine";
	mes "your health in the future.";
	mes "Take care of yourself!";
	close;

OnTouch:
	if (diamond_edq == 17) {
		mes "[Wola]";
		mes "Oh, how can this be";
		mes "happening? What ";
		mes "should I do?";
		set diamond_edq,18;
		close;
	}
	end;
}

in_rogue,366,46,3	script	Rogue Investigator	828,{
	if ((diamond_edq == 22) && (BaseJob == Job_Rogue)) {
		mes "[Investigator]";
		mes "Oh, remember me?";
		mes "It's been a long time";
		mes "so I can't blame you:";
		mes "I was there when you";
		mes "first joined our guild!";
		mes "So what brings you here?";
		next;
		select("Ask About the Z Gang");
		mes "[Investigator]";
		mes "Ah, Z Gang, you said?";
		next;
		mes "[Investigator]";
		mes "Oh, yeah, we're investigating";
		mes "those guys. We actually found";
		mes "their hideout, but then they";
		mes "escaped before we could even";
		mes "catch them. Wily bastards...";
		mes "I think they were tipped off!";
		next;
		mes "[Investigator]";
		mes "A few of our Rogue agents";
		mes "are investigating the hideout,";
		mes "but I doubt they can find";
		mes "anything useful there.";
		next;
		select("Where's the hideout?");
		mes "[Investigator]";
		mes "Well, it's a large";
		mes "warehouse over in Comodo.";
		mes "You're free to scope out";
		mes "the place if you really want.";
		mes "But chances are slim that";
		mes "you'll find anything at all.";
		close;
	}
	else if ((diamond_edq == 22) && (BaseClass == Job_Thief)) {
		mes "[Investigator]";
		mes "Hey, how's it going?";
		mes "You ever consider joining";
		mes "the Rogues? It'd be a lot";
		mes "of fun. You're welcome to";
		mes "look around, so feel free";
		mes "to ask me any questions.";
		next;
		select("Ask About the Z Gang");
		mes "[Investigator]";
		mes "Oh, yeah, we're investigating";
		mes "those guys. We actually found";
		mes "their hideout, but then they";
		mes "escaped before we could even";
		mes "catch them. Wily bastards...";
		mes "I think they were tipped off!";
		next;
		mes "[Investigator]";
		mes "A few of our Rogue agents";
		mes "are investigating the hideout,";
		mes "but I doubt they can find";
		mes "anything useful there.";
		next;
		select("Where's the hideout?");
		mes "[Investigator]";
		mes "Well, I'm not sure if";
		mes "I can just tell you that";
		mes "kind of information. I mean,";
		mes "you're not a member of our";
		mes "guild... Yet. What can I do?";
		next;
		mes "[Investigator]";
		mes "I got it. I'll just tell my";
		mes "boss that you bribed me";
		mes "with 10,000 zeny. Not too";
		mes "much, is it? We Rogues are";
		mes "pretty bad... But not that bad.";
		next;
		switch(select("Sounds good!:Never mind, I'll find it myself...")) {
		case 1:
			if (Zeny > 9999) {
				set Zeny, Zeny-10000;
				mes "[Investigator]";
				mes "Thanks for the money~";
				mes "Of course, if you became";
				mes "a Rogue, this amount is";
				mes "nothing compared to what";
				mes "you can gank. ^666666*Ahem*^000000";
				next;
				mes "[Investigator]";
				mes "The Z Gang's old hideout";
				mes "is a large warehouse over";
				mes "in Comodo. I doubt you'll";
				mes "find anything there, but";
				mes "you can go check it out. ";
				changequest 3115,3116;
				set diamond_edq,23;
				close;
			}
			else {
				mes "[Investigator]";
				mes "Aw, what am I gonna";
				mes "do if my boss askes to";
				mes "see the money? We've got";
				mes "to make this bribe authentic!";
				close;
			}
		case 2:
			mes "[Investigator]";
			mes "Be my guest, maybe it'll be";
			mes "a good learning experience.";
			mes "But yeah, come back when";
			mes "you think about changing";
			mes "your job, okay? Be a Rogue~";
			close;
		}
	}
	else if (diamond_edq == 22) {
		mes "[Investigator]";
		mes "Whoa, what are you doin'";
		mes "here? I've got no beef with";
		mes "you, but some Rogues, see,";
		mes "some Rogues, have got a ";
		mes "lot of beef. Uh, what'd you";
		mes "want? Not a fight, I hope.";
		next;
		select("Ask About the Z Gang");
		mes "[Investigator]";
		mes "Those guys have been";
		mes "bothering everyone!";
		mes "Yeah, I guess you can";
		mes "say we're united on this.";
		next;
		mes "[Investigator]";
		mes "Oh, yeah, we're investigating";
		mes "those guys. We actually found";
		mes "their hideout, but then they";
		mes "escaped before we could even";
		mes "catch them. Wily bastards...";
		mes "I think they were tipped off!";
		next;
		mes "[Investigator]";
		mes "A few of our Rogue agents";
		mes "are investigating the hideout,";
		mes "but I doubt they can find";
		mes "anything useful there.";
		next;
		select("Where's the hideout?");
		mes "[Investigator]";
		mes "Uh, I can't just give";
		mes "you that information";
		mes "if you're not a member";
		mes "of our guild. But since";
		mes "we're together on this,";
		mes "I'll just ask for 10,000 zeny.";
		next;
		switch(select("Sounds good!:Forget it...")) {
		case 1:
			if (Zeny > 9999) {
				set Zeny, Zeny-10000;
				mes "[Investigator]";
				mes "Right on, right on.";
				mes "Alright, I didn't charge";
				mes "you much since the hideout";
				mes "is a large warehouse in";
				mes "Comodo where I doubt you'll";
				mes "find anything. But who knows? ";
				changequest 3115,3116;
				set diamond_edq,23;
				close;
			}
			else {
				mes "[Investigator]";
				mes "Hey, this isn't enough";
				mes "money. I thought people";
				mes "in your job made more";
				mes "zeny than this everyday!";
				close;
			}
		case 2:
			mes "[Investigator]";
			mes "Alright, but I think";
			mes "you'll need a little";
			mes "extra help finding that";
			mes "place. I mean, Rogues are";
			mes "hiding experts, and even";
			mes "we had some trouble!";
			close;
		}
	}
	mes "[Investigator]";
	mes "Geez, I'm so sleepy...";
	mes "Everyone's out on a mission,";
	mes "and I'm stuck here on guard";
	mes "duty. When can I see some";
	mes "action again? Ugh! Rogues";
	mes "hate standing in one place!";
	close;
}

cmd_in02,94,208,5	script	Investigator	828,{
	if ((diamond_edq == 22) || (diamond_edq == 23)) {
		if (BaseJob == Job_Rogue) {
			mes "[Investigator]";
			mes "Hey, pal. You part";
			mes "of the guild? Nice.";
			mes "If they sent you to help";
			mes "out, well, there's nothing";
			mes "to do here. This place is";
			mes "pretty much cleaned out.";
			close;
		}
		else {
			mes "[Investigator]";
			mes "What do you want?";
			mes "Oh, your guild musta";
			mes "sent you to help out.";
			mes "Yeah. We investigated.";
			mes "Didn't find anything.";
			mes "The Z Gang's long gone.";
			next;
			mes "[Investigator]";
			mes "Feel free to look";
			mes "around, but if you ask";
			mes "me, it's a waste of time.";
			close;
		}
	}
	else {
		mes "[Investigator]";
		mes "Yeesh, I'm stuck here";
		mes "till I receive my new";
		mes "orders from the guild.";
		mes "They didn't... They didn't";
		mes "forget about me, did they?";
		close;
	}
}

cmd_in02,69,188,1	script	Small Safe	844,{
	if ((diamond_edq == 22) || (diamond_edq == 23)) {
		mes "^3355FFThere is a small";
		mes "safe hidden under the";
		mes "shadows of these boxes.^000000";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "Looks like the Z Gang";
		mes "forgot to take this with";
		mes "them in their rush to escape.";
		mes "Hmmm... How do I open this?";
		mes "Is there a key, a switch,";
		mes "something I can use?";
		hideoffnpc "Odd Switch#Switch1";
		close;
	}
	if (diamond_edq == 24) {
		mes "[" + strcharinfo(0) + "]";
		mes "Hey, the safe opened!";
		mes "Those switches must have";
		mes "done the trick. Now...";
		mes "Let's look inside.";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "Awesome! It's a huge";
		mes "red diamond! Is this";
		mes "Ibrahim's Diamond";
		mes "of Destruction?";
		next;
		getitem 7723,1; //Diamond_Of_Ruin
		mes "[Investigator]";
		mes "Zzz... Zzz...";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "Lucky for me, this";
		mes "guy is asleep. I better";
		mes "sneak out of here, and";
		mes "return this big diamond";
		mes "to Ibrahim as soon as I can.";
		changequest 3117,3118;
		set diamond_edq,25;
		close;
	}
	if ((diamond_edq < 22) && (diamond_edq > 24)) {
		mes "[" + strcharinfo(0) + "]";
		mes "There was something here";
		mes "fixed to the ground, but now";
		mes "it looks like something yanked";
		mes "it out forcefully. How weird.";
		close;
	}
	mes "[" + strcharinfo(0) + "]";
	mes "What is this?";
	mes "Oh well, I don't";
	mes "think it's important.";
	close;
}

cmd_in02,69,196,0	script	Odd Switch#Switch1	844,{
	if ((diamond_edq == 22) || (diamond_edq == 23)) {
		mes "^3355FFThere is a tiny";
		mes "switch on the ground";
		mes "near the whiskey barrels.^000000";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "Was this here before?";
		mes "What happens if I press";
		mes "this teeny little switch?";
		next;
		hideoffnpc "Odd Switch#Switch2";
		mes "^3355FF*Click Click*^000000";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "That sound...!";
		mes "Something happened,";
		mes "but what could it be?";
		hideonnpc "Odd Switch#Switch1";
		close;
	}
	end;

OnInit:
	hideonnpc "Odd Switch#Switch1";
	end;
}

cmd_in02,80,189,0	script	Odd Switch#Switch2	844,{
	if ((diamond_edq == 22) || (diamond_edq == 23)) {
		mes "^3355FFThere is a tiny";
		mes "switch on the ground";
		mes "under the boxes' shadows.^000000";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "Was this here before?";
		mes "What happens if I press";
		mes "this teeny little switch?";
		next;
		mes "^3355FF*Click Click*^000000";
		next;
		changequest 3116,3117;
		set diamond_edq,24;
		mes "[" + strcharinfo(0) + "]";
		mes "That sound...!";
		mes "Something happened,";
		mes "but what could it be?";
		close2;
		hideonnpc "Odd Switch#Switch1";
		hideonnpc "Odd Switch#Switch2";
	}
	end;

OnInit:
	hideonnpc "Odd Switch#Switch2";
	end;
}

// Z-Gang Quest
//============================================================
prontera,150,326,0	script	Wanted Notice#edq	111,{
	if (zdan_edq == 0) {
		mes "====National Wanted Notice====";
		mes "Please report any information";
		mes "regarding these criminals to";
		mes "the Rune-Midgarts Kingdom's";
		mes "Homeland Security Office.";
		next;
		mes "====National Wanted Notice====";
		mes "^0000ffLouis Von Silokens^000000 - Human Male";
		mes "^0000ffMartha Hertizan^000000 - Human Female";
		mes "^0000ffCatfoii^000000 - Pet Cat";
		next;
		mes "====National Wanted Notice====";
		mes "These infamous members";
		mes "of the Z Gang are suspected";
		mes "of stealing national treasure,";
		mes "committing fraud, forgery,";
		mes "and promoting overall";
		mes "depravity and immorality.";
		next;
		mes "====National Wanted Notice====";
		mes "Anyone that captures";
		mes "the listed criminals will";
		mes "be rewarded with the Book";
		mes "of Forbidden Mystery, one";
		mes "of the kingdom's treasures.";
		setquest 3119;
		set zdan_edq,1;
		close;
	}
	else if (zdan_edq > 18) {
		mes "[Chief Officer]";
		mes "Ever since you captured";
		mes "the Z Gang, crime has gone";
		mes "down to an all time low.";
		mes "Thanks for helping us out~";
		next;
		mes "[Chief Officer]";
		mes "Peace never lasts long...";
		mes "Some other fiends will";
		mes "replace the Z Gang soon.";
		mes "When that happens, I hope";
		mes "we can count on you again.";
		close;
	}
	else {
		mes "====National Wanted Notice====";
		mes "Please report any information";
		mes "regarding these criminals to";
		mes "the Rune-Midgarts Kingdom's";
		mes "Homeland Security Office.";
		next;
		mes "====National Wanted Notice====";
		mes "^0000ffLouis Von Silokens^000000 - Human Male";
		mes "^0000ffMartha Hertizan^000000 - Human Female";
		mes "^0000ffCatfoii^000000 - Pet Cat";
		next;
		mes "====National Wanted Notice====";
		mes "These infamous members";
		mes "of the Z Gang are suspected";
		mes "of stealing national treasure,";
		mes "committing fraud, forgery,";
		mes "and promoting overall";
		mes "depravity and immorality.";
		next;
		mes "====National Wanted Notice====";
		mes "Anyone that captures";
		mes "the listed criminals will";
		mes "be rewarded with the Book";
		mes "of Forbidden Mystery, one";
		mes "of the kingdom's treasures.";
		close;
	}

//OnInit:
//	enablenpc "Wanted Notice#edq";
//	end;

OnEnable:
	enablenpc "Wanted Notice#edq";
	end;

OnDisable:
	disablenpc "Wanted Notice#edq";
	end;
}

prontera,148,326,3	script	Chief Officer#edq	734,{
	if (checkweight(907,200) == 0) {
		mes "[Chief Officer]";
		mes "You're carrying far too";
		mes "much right now. Please";
		mes "lighten your load by";
		mes "placing your items";
		mes "in the Kafra Storage.";
		close;
	}
	if ((zdan_edq == 0) && (BaseLevel > 70)) {
		mes "[Chief Officer]";
		mes "Adventurers, this is";
		mes "your chance to protect";
		mes "and serve your country!";
		mes "Please read the National";
		mes "Wanted Notice for more details.";
		close;
	}
	if ((zdan_edq == 1) && (BaseLevel > 70)) {
		mes "[Chief Officer]";
		mes "Attention, attention.";
		mes "The Homeland Security Office";
		mes "of the Rune-Midgarts Kingdom";
		mes "is looking for any information";
		mes "regarding the Z Gang.";
		next;
		mes "[Chief Officer]";
		mes "There are three members:";
		mes "Louis Von Silokens a.k.a.";
		mes "Louis, Martha Hertizan a.k.a.";
		mes "Martha, and Catfoii a.k.a.";
		mes "Catfoii must be captured!";
		changequest 3119,3120;
		set zdan_edq,2;
		close;
	}
	if ((zdan_edq == 1) && (BaseLevel < 70)) {
		mes "[Chief Officer]";
		mes "Oh, were you interested";
		mes "in pursuing the criminals";
		mes "listed in the Wanted notice?";
		mes "I'm sorry, but you're not";
		mes "ready for that kind of task...";
		mes "But I encourge you to train!";
		mes "Thank you.";
		close;
	}
	if (zdan_edq == 2) {
		mes "[Chief Officer]";
		mes "Hello, Rune-Midgartian.";
		mes "What brings you here?";
		next;
		switch(select("National Wanted Notice:Z Gang:What crimes did Z Gang commit?")) {
		case 1:
			mes "[Chief Officer]";
			mes "Ah, were you interested";
			mes "in pursuing the Z Gang?";
			mes "Please carefully read the";
			mes "Wanted notice posted right";
			mes "next to me. We desperately";
			mes "need help to catch them.";
			next;
			mes "[Chief Officer]";
			mes "If you want to learn";
			mes "more about the Z Gang,";
			mes "I suggest talking to";
			mes "a knight named Valdes.";
			mes "He's fairly familiar with";
			mes "their brand of antics.";
			next;
			mes "[Chief Officer]";
			mes "Unfortunately, I fear";
			mes "that he may be ashamed";
			mes "of his failure to catch";
			mes "them. However, I am sure";
			mes "that he will be of service";
			mes "in your quest for justice.";
			close;
		case 2:
			mes "[Chief Officer]";
			mes "You wanted to know more";
			mes "about the Z Gang members?";
			mes "I don'tknow much about them,";
			mes "but I can divulge what";
			mes "little I've heard.";
			next;
			switch(select("Louis:Martha:Catfoii:Book of Forbidden Mystery?")) {
			case 1:
				mes "[Chief Officer]";
				mes "Louis Von Silokens was";
				mes "the third son of the noble";
				mes "Silokens family which was";
				mes "ruined many years ago.";
				mes "According to record, he failed";
				mes "Magic Academy three times.";
				next;
				mes "[Chief Officer]";
				mes "It is believed that he formed";
				mes "the Z Gang as a result of";
				mes "his personal failure. He";
				mes "also stole the Book of";
				mes "Forbidden Mystery from";
				mes "the Royal Library.";
				next;
				mes "[Chief Officer]";
				mes "We must capture him and";
				mes "retrieve that book before";
				mes "he can abuse its power.";
				mes "We need the help of the";
				mes "people in order to find him";
				mes "and bring Louis to justice!";
				close;
			case 2:
				mes "[Chief Officer]";
				mes "Martha Hertizan is the";
				mes "second daughter of Alberta's";
				mes "Hertizan family. No one knows";
				mes "why she ran away from home";
				mes "at the age of twenty.";
				next;
				mes "[Chief Officer]";
				mes "Martha handles the Z Gang's";
				mes "finances. It's a shame that";
				mes "such a talented merchant";
				mes "has stooped to thievery. ";
				close;
			case 3:
				mes "[Chief Officer]";
				mes "We initially believed";
				mes "that Catfoii was just";
				mes "Louis and Martha's pet";
				mes "cat... But it's actually a";
				mes "master criminal. Approach";
				mes "it with extreme caution!";
				next;
				mes "[Chief Officer]";
				mes "That's all I know about";
				mes "that animal. The true";
				mes "nature of Catfoii is";
				mes "shrouded in mystery.";
				close;
			case 4:
				mes "[Chief Officer]";
				mes "Out of all their crimes,";
				mes "their theft of the Book of";
				mes "Forbidden Mystery poses the";
				mes "greatest danger to the kingdom.";
				mes "If they misuse that book, it";
				mes "will cause grave disaster.";
				close;
			}
		case 3:
			mes "[Chief Officer]";
			mes "That damned Z Gang seems";
			mes "to be in the center of all the";
			mes "crime that's happening in";
			mes "Rune-Midgarts. They caused";
			mes "a riot in Geffen by spreading";
			mes "rumors about ghosts...";
			next;
			mes "[Chief Officer]";
			mes "They poisoned the";
			mes "drinking water in the";
			mes "wells of Payon, seized";
			mes "countless goods, profited";
			mes "off illegal trade... The list";
			mes "just keeps going on and on...";
			next;
			mes "[Chief Officer]";
			mes "Out of all their crimes,";
			mes "their theft of the Book of";
			mes "Forbidden Mystery poses the";
			mes "greatest danger to the kingdom.";
			mes "If they misuse that book, it";
			mes "will cause grave disaster.";
			close;
		}
	}
	if (zdan_edq == 18) {
		mes "[Chief Officer]";
		mes "Oh, you were that nice";
		mes "adventurer that dropped";
		mes "by the other day. How";
		mes "may I help you?";
		next;
		mes "[Chief Officer]";
		mes "What's that?! You really";
		mes "found the Z Gang's hideout,";
		mes "captured them, and retrieved";
		mes "the forbidden book?";
		next;
		delitem 7724,1; //Forbidden_Secret_Art
		mes "[Chief Officer]";
		mes "Splendid! Just splendid!";
		mes "On behalf of the king,";
		mes "let me give you a well";
		mes "deserved reward. Thank";
		mes "you for your work on";
		mes "behalf of our kingdom!";
		next;
		mes "[Chief Officer]";
		mes "Your bravery and ardent";
		mes "patriotism will forever";
		mes "be recorded in the annals";
		mes "of Rune-Midgarts history.";
		mes "Congratulations! ";
		completequest 3134;
		set zdan_edq,19;
		getexp (checkre(3))?100000:1000000,0;
		close;
	}
	mes "[Chief Officer]";
	mes "All those that oppose";
	mes "the peace and safety of";
	mes "our kingdom will not be";
	mes "spared the fury of our";
	mes "righteous swords!";
	close;
}

prt_in,168,18,1	script	Valdes	65,2,2,{
	if (checkweight(907,100) == 0) {
		mes "^3355FFWait a second!";
		mes "Right now, you're carrying";
		mes "too many things with you.";
		mes "Please come back after";
		mes "using the Kafra Service";
		mes "to store some of your items.^000000";
		close;
	}
	if (zdan_edq == 2) {
		mes "[Valdes]";
		mes "^666666*Urp*^000000 What?";
		mes "You here to... ";
		mes "Um, commiserate";
		mes "about failing life too?";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "Are you Valdes?";
		mes "I came to ask you";
		mes "for your help...";
		next;
		mes "[Valdes]";
		mes "^666666*Hiccup*^000000 Why~?";
		mes "I'm-I'm no good";
		mes "to anybody anymore!";
		next;
		switch(select("Ask About Z Gang:The chief officer is worried about you.:Bye!")) {
		case 1:
			mes "[Valdes]";
			mes "That Z Gang...";
			mes "Ruined my life!";
			mes "My career, my pension...";
			mes "It's all gone because";
			mes "of those damn criminals!";
			next;
			mes "[Valdes]";
			mes "You? Capture them?";
			mes "Good luck! Just... Just";
			mes "try not to end up like me.";
			mes "Oh, you want info-information?";
			mes "^666666*Hic*^000000 Heh, scratch my back,";
			mes "well, you know the rest.";
			next;
			mes "[Valdes]";
			mes "Heh, I just ran out of wine!";
			mes "Gimme, um, ^0000FFMorocc Fruit Wine^000000!";
			mes "5 bottles! Then... Maybe...";
			mes "I'll tell you want you really";
			mes "wanna know. Heh heh~ ^666666*Hic!*^000000";
			changequest 3120,3121;
			set zdan_edq,3;
			close;
		case 2:
			mes "[Valdes]";
			mes "R-really? Well...";
			mes "I don't care! Just";
			mes "let me drink in peace!";
			close;
		case 3:
			mes "[Valdes]";
			mes "Wha--? Who do you think";
			mes "you're messing with, ^666666*Hic!*^000000";
			mes "just comin' by to say ''bye?!''";
			mes "I useta be a knight once,";
			mes "you know that? I could";
			mes "totally kick your--^666666*Urp!*^000000";
			close;
		}
	}
	if (zdan_edq == 3) {
		mes "[Valdes]";
		mes "Heeeey yoooou~";
		mes "You bring me wine?";
		next;
		if (countitem(12049) > 4) {
			delitem 12049,5; //Int_Dish04
			mes "[Valdes]";
			mes "^666666*Sniff*^000000 Yeeesh, that";
			mes "smeells good. This must";
			mes "be it. Oh, yeah. Knight's";
			mes "honor. First I talk, then";
			mes "I can drink theesh...";
			next;
			mes "[Valdes]";
			mes "Well, maybe...";
			mes "Just one tashte~";
			mes "^666666*Gulp Gulp Gulp*^000000";
			next;
			mes "[" + strcharinfo(0) + "]";
			mes "You just...";
			mes "That was three bottles!";
			next;
			mes "[Valdes]";
			mes "Ah, delicious!";
			mes "Alright, fellow,";
			mes "now I feel better";
			mes "able to speak now~";
			next;
			mes "[" + strcharinfo(0) + "]";
			mes "(^666666Whaaaat~?^000000)";
			next;
			mes "[Valdes]";
			mes "I assume you already know";
			mes "the basic information about";
			mes "the Z Gang, their members,";
			mes "et cetera. I'm not sure why";
			mes "they've gone on such a brazen";
			mes "crime spree so suddenly.";
			next;
			mes "[Valdes]";
			mes "Their success is unprecedented:";
			mes "they've even managed to elude";
			mes "the Rogue Guild repeatedly.";
			mes "I investigated them six";
			mes "months ago, but I was";
			mes "dismissed for my failure.";
			next;
			mes "[Valdes]";
			mes "I am aware that the Z Gang";
			mes "has many informers in their";
			mes "employ, and that the Rogue";
			mes "Guild are still investigating";
			mes "the Z Gang on their own.";
			next;
			mes "[Valdes]";
			mes "I suggest meeting one";
			mes "of the Rogue agents,";
			mes "Marybell, in the Rogue";
			mes "Guild. The password you";
			mes "must give her is ''^0000FFThe";
			mes "dawn is yet to come^000000.''";
			next;
			mes "[Valdes]";
			mes "That is all I can tell you.";
			mes "Please give my regards to";
			mes "Marybell for me, and catch";
			mes "those Z Gang bastards.";
			mes "^666666*Sigh*^000000 I think I'll go rest";
			mes "a bit now. Good luck!";
			changequest 3121,3122;
			set zdan_edq,4;
			close;
		}
		else {
			mes "[Valdes]";
			mes "You stinkface!";
			mes "Where's my wine?!";
			mes "My ^0000FF5 bottles of";
			mes "Morocc Fruit Wine^000000?!";
			mes "I'm not gonna talk";
			mes "to you without it!";
			close;
		}
	}
	if (zdan_edq == 4) {
		mes "[Valdes]";
		mes "I suggest meeting";
		mes "Marybell in the Rogue";
		mes "Guild, and giving her the";
		mes "password, ''^0000FFThe dawn";
		mes "is yet to come^000000.''";
		close;
	}
	mes "[Valdes]";
	mes "^666666*Sigh*^000000 It is my greatest";
	mes "wish that the knight master";
	mes "will allow me to rejoin the";
	mes "corps. However, I doubt that";
	mes "he's forgiven my failure. ^666666*Sob*^000000";
	mes "It's like my life is over...";
	close;

OnTouch:
	if (zdan_edq == 2) {
		mes "[Valdes]";
		mes "^666666*Sigh*^000000 Goddamn thieves.";
		mes "Why should I even care";
		mes "about them?! They're not";
		mes "my problem anymore...";
		mes "^666666*Hiccup*^000000";
		close;
	}
	end;
}

in_rogue,359,116,3	script	Marybell	747,{
	if (zdan_edq == 4) {
		mes "[Marybell]";
		mes "Hey. What do you want?";
		next;
		switch(select("Password")) {
		case 1:
			mes "[Marybell]";
			mes "Can't hear you.";
			mes "Come a little closer,";
			mes "and talk right in my ear.";
			next;
			input .@input$;
			if (.@input$ == "The dawn is yet to come.") {
				mes "[Marybell]";
				mes "Valdes sent you?!";
				mes "I'm surprised he finally";
				mes "decided to talk to someone!";
				mes "Alright, if you're his friend,";
				mes "then I'll do what I can to";
				mes "help. What do you need?";
				next;
				switch(select("Ask About Z Gang:Valdes says, ''Hi.''")) {
				case 1:
					mes "[Marybell]";
					mes "The Z Gang again?";
					mes "I guess he's still mad";
					mes "about capturing them.";
					mes "It's not a big deal for me";
					mes "to tell you what I know, but";
					mes "can I really trust you?";
					next;
					mes "[Marybell]";
					mes "Let me test you out.";
					mes "Go to Payon, find an old";
					mes "guy named Moonho Ahn. He's";
					mes "a legendary gambler better";
					mes "known as the White Meteor.";
					mes "If he trusts you, I trust you.";
					next;
					mes "[Marybell]";
					mes "Heh, but he'll only trust";
					mes "you if you can beat him at";
					mes "gambling. Whether you return";
					mes "or not, I'm gonna continue";
					mes "investigating the Z Gang for";
					mes "the Rogue Guild and Valdes.";
					changequest 3122,3123;
					set zdan_edq,5;
					break;
				case 2:
					mes "[Marybell]";
					mes "Weird. It's not like him";
					mes "to send his regards like that.";
					mes "He's not sick or anything,";
					mes "is he? If he's still drinking";
					mes "the next time I see him,";
					mes "I'm gonna clobber that guy!";
					next;
					mes "[Marybell]";
					mes "Geez. Doesn't he know";
					mes "I volunteered for this";
					mes "job for his sake? Oh well,";
					mes "it's really nice to know";
					mes "that he's thinking of me.";
					mes "What? Rogues can have friends!";
					close;
				}
			}
			else {
				mes "[Marybell]";
				mes "Can't tell what";
				mes "you're saying exactly.";
				next;
				if (Sex == SEX_MALE) {
					mes "[Marybell]";
					mes "If this is some kind";
					mes "of weird come-on, you'd";
					mes "better have something";
					mes "better to say than that!";
					close;
				}
				else {
					mes "[Marybell]";
					mes "What, are you coming";
					mes "on to me? I'm down with";
					mes "any gender, but, heh,";
					mes "you sure about this?";
					mes "Hahahahahahahahaha!";
					close;
				}
			}
		}
	}
	if ((zdan_edq > 4) && (zdan_edq < 7)) {
		mes "[Marybell]";
		mes "Let me test you out.";
		mes "Go to Payon, find an old";
		mes "guy named Moonho Ahn. He's";
		mes "a legendary gambler better";
		mes "known as the White Meteor.";
		mes "If he trusts you, I trust you.";
		close;
	}
	if (zdan_edq == 7) {
		mes "[Marybell]";
		mes "What, you're back?";
		mes "Aww, nuts! That signature";
		mes "on your arm... You really";
		mes "beat him? How the hell--?";
		next;
		set zdan_edq,8;
		mes "[Marybell]";
		mes "Wait, lemme check";
		mes "and make sure. Yeap.";
		mes "That's the one. That's";
		mes "the signature he used when";
		mes "he was the White Meteor.";
		next;
		mes "[Marybell]";
		mes "How did you beat him?";
		mes "Last time, he cleaned me";
		mes "out of all my money! You";
		mes "must be some kinda genius";
		mes "to beat that guy. Heh. Guess";
		mes "I underestimated you.";
		next;
		mes "[Marybell]";
		mes "Hold on, before anything";
		mes "else, I gotta tell everyone";
		mes "that someone actually beat";
		mes "the White Meteor. They're";
		mes "totally not gonna believe it!";
		close;
	}
	if (zdan_edq == 8) {
		mes "[Marybell]";
		mes "Well, I promised to tell";
		mes "you what I know about the";
		mes "Z Gang, so I gotta make good.";
		mes "What did you want to ask me?";
		next;
		while(1) {
			switch(select("Z Gang's Goal:Z Gang's Recent Movements:Z Gang's Stronghold:Thanks for the tip!")) {
			case 1:
				mes "[Marybell]";
				mes "Actually, I don't know";
				mes "what they plan to do, but";
				mes "obviously it isn't something";
				mes "very good. Personally, I think";
				mes "they just want to harass";
				mes "people as much as they can.";
				next;
				mes "[Marybell]";
				mes "You could conquer the";
				mes "world, I guess, with that";
				mes "Book of Forbidden Mystery,";
				mes "but I don't think they're";
				mes "that... Calculating.";
				next;
				set .@a,.@a+1;
				break;
			case 2:
				mes "[Marybell]";
				mes "Well, the Z Gang is";
				mes "responsible for a series";
				mes "of thefts lately. They've";
				mes "only been stealing jewels";
				mes "like diamonds, rubies,";
				mes "emeralds. Not sure why...";
				next;
				set .@b,.@b+1;
				break;
			case 3:
				mes "[Marybell]";
				mes "Well, we don't know where";
				mes "they're hiding out. If that";
				mes "was the case, the Z Gang";
				mes "would already be caught!";
				mes "They even got away from";
				mes "us Rogues. Crazy, huh?";
				next;
				mes "[Marybell]";
				mes "The Rogue Guild has got";
				mes "the best intel network so...";
				mes "The Z Gang probably has";
				mes "informers everywhere in";
				mes "all the towns. It's the only";
				mes "way they can escape us.";
				next;
				mes "[Marybell]";
				mes "Hey, we can use that...";
				mes "If we can catch one of";
				mes "their informers, one of";
				mes "them'll spill the beans";
				mes "on where the Z Gang is";
				mes "hiding. Worth a shot, yeah?";
				next;
				set .@c,.@c+1;
				break;
			case 4:
				if ((.@a > 0) && (.@b > 0) && (.@c > 0)) {
					mes "[Marybell]";
					mes "Oh, and one more thing...";
					mes "I hear that they're trying";
					mes "to secretly recruit more";
					mes "gang members in Morocc.";
					mes "The nerve of those guys...!";
					next;
					mes "[Marybell]";
					mes "It might be a good idea";
					mes "for you to go to Morocc";
					mes "and check it out. Good";
					mes "luck finding those Z Gang";
					mes "guys. And take care!";
					changequest 3125,3126;
					set zdan_edq,9;
					close;
				}
				else {
					mes "[Marybell]";
					mes "Hey, I'm willing to";
					mes "let you in on everything";
					mes "I know. After all the trouble";
					mes "you went through, I'm sure";
					mes "you've got a ton of questions!";
					next;
				}
				break;
			}
		}
	}
	if ((zdan_edq > 8) && (zdan_edq < 11)) {
		mes "[Marybell]";
		mes "Oh, and one more thing...";
		mes "I hear that they're trying";
		mes "to secretly recruit more";
		mes "gang members in Morocc.";
		mes "The nerve of those guys...! ";
		next;
		mes "[Marybell]";
		mes "I haven't heard anything";
		mes "else, but would you let me";
		mes "know if you get any new leads?";
		mes "The Rogue Guild wants these";
		mes "Z Gang guys really bad!";
		close;
	}
	if (zdan_edq == 11) {
		mes "[Marybell]";
		mes "Hey, I've been looking for";
		mes "you! One of the Rogues";
		mes "dispatched to Comodo found";
		mes "this envelope on a Z Gang";
		mes "informer. Could be a clue!";
		next;
		mes "[Marybell]";
		mes "What's weird is that the";
		mes "informer was fighting so";
		mes "hard to hold on to blank";
		mes "piece of paper... But we ";
		mes "know that's not really";
		mes "the case here, is it?";
		next;
		mes "[Marybell]";
		mes "They must've been using";
		mes "some kinda secret invisible";
		mes "ink. Anyway, I gave the paper";
		mes "to a professional decoder to";
		mes "crack, and he oughta be";
		mes "done by now. Anyway...";
		next;
		mes "[Marybell]";
		mes "I thought maybe you'd";
		mes "want to go talk to him,";
		mes "see if he's finished with it.";
		mes "His name's Gooho Ahn over";
		mes "in Payon, one of the best";
		mes "decoders in this kingdom.";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "Gooho Ahn?";
		mes "Why does it feel like";
		mes "I should know that";
		mes "name? It's so familiar...";
		next;
		mes "[Marybell]";
		mes "I guess it's 'cause";
		mes "Gooho is the younger brother";
		mes "of Moonho, that legendary";
		mes "gambler you happened to beat.";
		next;
		mes "[Marybell]";
		mes "Eh, that's not what's";
		mes "important right now.";
		mes "Would you talk to Gooho";
		mes "Ahn and see what he found?";
		changequest 3128,3129;
		set zdan_edq,12;
		close;
	}
	if ((zdan_edq > 11) && (zdan_edq < 14)) {
		mes "[Marybell]";
		mes "Hey, why don't you talk";
		mes "to Gooho Ahn in Payon,";
		mes "and ask if he finished";
		mes "decoding that secret letter";
		mes "from the Z Gang already?";
		close;
	}
	if (zdan_edq == 14) {
		mes "[Marybell]";
		mes "Oh, Gooho decoded everything?";
		mes "Let's see... Something about";
		mes "the Book of Forbidden Mystery... Ah. There we go! The location";
		mes "of the Z Gang's hideout!";
		mes "Heh heh! We got 'em!";
		next;
		mes "[Marybell]";
		mes "One of the Rogues followed";
		mes "the informer carrying this";
		mes "letter, but he just vanished";
		mes "during the chase... Like, by";
		mes "magic, I guess. So we don't";
		mes "the hideout's exact place.";
		next;
		mes "[Marybell]";
		mes "Even so, we're definitely";
		mes "sure that it's around the";
		mes "secret path to the South";
		mes "Morocc underground. Um,";
		mes "but we can't let you come";
		mes "with the Rogue Guild.";
		next;
		mes "[Marybell]";
		mes "Sorry, I know, it's official";
		mes "business and all that hooplah.";
		mes "Tell you what. We'll each go";
		mes "hunt them down separately, and";
		mes "whoever finds 'em first gets";
		mes "dibs on beating them up!";
		changequest 3131,3132;
		set zdan_edq,15;
		close;
	}
	if ((zdan_edq > 14) && (zdan_edq < 18)) {
		mes "[Marybell]";
		mes "We're definitely sure";
		mes "that the Z Gang's hideout";
		mes "is near the secret path";
		mes "to the South Morocc";
		mes "underground. You";
		mes "know where that is?";
		next;
		mes "[Marybell]";
		mes "Tell you what. We'll each go";
		mes "hunt them down separately, and";
		mes "whoever finds 'em first gets";
		mes "dibs on beating them up!";
		close;
	}
	if (zdan_edq > 18) {
		mes "[Marybell]";
		mes "Nice work! You captured";
		mes "the Z Gang! The kingdom";
		mes "musta given you a really";
		mes "big reward, huh?";
		next;
		mes "[Marybell]";
		mes "Valdes really wanted to";
		mes "capture them himself, but";
		mes "I'm sure he'll be real happy";
		mes "to hear the news. Maybe he";
		mes "and I can celebrate with";
		mes "a little drink later.";
		next;
		mes "[Marybell]";
		mes "Hey, it's been a real";
		mes "thrill working with you,";
		mes "real thrill. Maybe we can";
		mes "team up again for a good";
		mes "cause some other time, yeah?";
		close;
	}
	if (BaseJob == Job_Thief) {
		mes "[Marybell]";
		mes "That Z Gang has been";
		mes "a pain the Rogue Guild's";
		mes "ass. No one runs away";
		mes "from us! Nobody! But...";
		mes "They can do it somehow.";
		next;
		mes "[Marybell]";
		mes "There's not much here";
		mes "that might interest you,";
		mes "but knock yourself out";
		mes "if you wanna look around.";
		close;
	}
}

payon,244,62,3	script	Moonho Ahn	897,{
	if (zdan_edq == 5) {
		mes "[Moonho Ahn]";
		mes "Hahaha, now what brings";
		mes "a youngster like you before";
		mes "me? You're not here for what";
		mes "I think you are... Are you?";
		next;
		switch(select("Are you the White Meteor?:I'm here to challenge you.")) {
		case 1:
			mes "[Moonho Ahn]";
			mes "It's been so long since";
			mes "I've heard that name...";
			mes "I'm retired now, and it";
			mes "seems no one wants to";
			mes "challenge me anymore.";
			next;
			mes "[Moonho Ahn]";
			mes "Those were some good";
			mes "times. Back then, only";
			mes "Dalho Kwak was able to";
			mes "give me a real challenge.";
			mes "I wonder what he's doing now?";
			close;
		case 2:
			mes "[Moonho Ahn]";
			mes "Oh, I was right...!";
			mes "You're here to challenge";
			mes "me! It's been a long time...";
			mes "Alright. I accept. However,";
			mes "I set all of the terms.";
			next;
			mes "[Moonho Ahn]";
			mes "I'm too old to play the";
			mes "complicated games I loved";
			mes "in the past. Let's just";
			mes "play a simple game that";
			mes "I call Coin Shake.";
			next;
			mes "[Moonho Ahn]";
			mes "This is basically";
			mes "a guessing game played";
			mes "in rounds, best 2 out of 3.";
			mes "One of us will be the coin";
			mes "shaker, and the other will";
			mes "be the guesser.";
			next;
			mes "[Moonho Ahn]";
			mes "The coin shaker begins";
			mes "the round by grabbing";
			mes "a random amount of coins,";
			mes "shaking them in his closed";
			mes "hands, and then stopping,";
			mes "keeping the coins concealed.";
			next;
			mes "[Moonho Ahn]";
			mes "The other person, the";
			mes "guesser, will then declare";
			mes "a guess on whether the total";
			mes "value of the coins is ^0000FFOdd^000000 or";
			mes "^0000FFEven^000000. Afterwards, the coin";
			mes "shaker reveals the coins...";
			next;
			mes "[Moonho Ahn]";
			mes "If the guesser is right,";
			mes "then he wins the round.";
			mes "However, if he guessed";
			mes "wrong, then it's a victory";
			mes "for the coin shaker. ";
			next;
			mes "[Moohno Ahn]";
			mes "We each take turns,";
			mes "switching roles.";
			mes "If it's your turn to";
			mes "guess, remember to";
			mes "call out ^0000FFOdd^000000 or ^0000FFEven^000000.";
			next;
			mes "[Moonho Ahn]";
			mes "Lastly, I'm the house,";
			mes "so I'll charge a game";
			mes "participation fee. It's";
			mes "not much, just 500 zeny.";
			mes "We're only playing for fun,";
			mes "not to break the bank.";
			next;
			mes "[Moonho Ahn]";
			mes "Why don't you give me";
			mes "some time to prep the";
			mes "game? When you return,";
			mes "we'll be ready to play~";
			changequest 3123,3124;
			set zdan_edq,6;
			close;
		}
	}
	if (zdan_edq == 6) {
		mes "[Moonho Ahn]";
		mes "Are you ready to";
		mes "play Coin Shake?";
		next;
		switch(select("Yes, let's play!:How does this game work again?")) {
		case 1:
			if (Zeny > 500) {
				set Zeny, Zeny-500;
				mes "[Moonho Ahn]";
				mes "Good, let's get started!";
				mes "I'll let you go first~";
				next;
				mes "*Shake Shake*";
				mes "Guess! Is it";
				mes "Odd or Even?";
				next;
				while(1) {
					set .@number,rand(1,2);
					input .@input$;
					if (((.@input$ == "Odd") && (.@number == 1)) || ((.@input$ == "Even") && (.@number == 2))) {
						set .@number_right,.@number_right+1;
						mes "[Moonho Ahn]";
						mes "^0000ff"+.@input$+"^000000?";
						mes "Okay, you won.";
						next;
						if ((.@number_right < 2) && (.@number_false < 2)) {
							mes "^3355FF*Shake Shake*";
							mes "Guess! Is it";
							mes "Odd or Even?^000000";
							next;
						}
					}
					else if (((.@input$ == "Odd") && (.@number == 2)) || ((.@input$ == "Even") && (.@number == 1))) {
						set .@number_false,.@number_false+1;
						mes "[Moonho Ahn]";
						mes "Well, it's ^0000FFEven^000000.";
						mes "I won.";
						next;
						if ((number_right < 2) && (number_false <2)) {
							mes "^3355FF*Shake Shake*";
							mes "Guess! Is it";
							mes "Odd or Even?^000000";
							next;
						}
					}
					if (.@number_right == 2) {
						mes "[Moonho Ahn]";
						mes "Ah, you win this round.";
						mes "However, the game has";
						mes "just started. The next";
						mes "round will be mine~";
						set .@win,.@win+1;
						next;
						break;
					}
					else if (.@number_false == 2) {
						mes "[Moonho Ahn]";
						mes "It looks like I win";
						mes "this round. I guess";
						mes "my gambling skills";
						mes "haven't left me yet~";
						set .@lose,.@lose+1;
						next;
						break;
					}
					if ((.@input$ != "Even") && (.@input$ !="Odd")) {
						mes "[Moonho Ahn]";
						mes "You can only declare";
						mes "your guess as ^0000FFOdd^000000 or ^0000FFEven^000000.";
						mes "Please try entering it again.";
						next;
					}
				}
				mes "[Moonho Ahn]";
				mes "It's time for the";
				mes "second round. This";
				mes "time, I'll be the one";
				mes "declaring my guess,";
				mes "and you'll shake the coins.";
				next;
				while(1) {
					mes "["+ strcharinfo(0) +"]";
					mes "(^666666What should I guess?^000000)";
					next;
					set .@amuro,rand(1,2);
					if (select("Odd:Even") == 1) {
						if (.@amuro == 1) {
							set .@number_false_2,.@number_false_2+1;
							mes "[Moonho Ahn]";
							mes "Let's see...";
							mes "It's ^0000FFOdd^000000, isn't it?";
							next;
							mes "[Moonho Ahn]";
							mes "Looks like I won!";
							mes "Hahahahahahaha!";
							next;
						}
						else if (.@amuro == 2) {
							set .@number_right_2,.@number_right_2+1;
							mes "[Moonho Ahn]";
							mes "Let's see...";
							mes "It's Even, isn't it?";
							next;
							mes "[Moonho Ahn]";
							mes "I lost?";
							mes "So it's ^0000FFOdd^000000...";
							next;
						}
						if (.@number_right_2 == 2) {
							set .@win,.@win+1;
							mes "[Moonho Ahn]";
							mes "Nice job. I didn't";
							mes "expect for you to";
							mes "really beat me...";
							mes "Hahahahahahah~";
							next;
							break;
						}
						else if (.@number_false_2 == 2) {
							set .@lose,.@lose+1;
							mes "[Moonho Ahn]";
							mes "Hahaha! I'm sorry, but";
							mes "I won. I guess I still got it!";
							next;
							break;
						}
					}
					else {
						if (.@amuro == 1) {
							set .@number_right_2,.@number_right_2+1;
							mes "[Moonho Ahn]";
							mes "Let's see...";
							mes "It's odd, isn't it?";
							next;
							mes "[Moonho Ahn]";
							mes "I lost, huh?";
							mes "So it was ^0000ffEven^000000...";
							next;
						}
						else if (.@amuro == 2) {
							set .@number_false_2,.@number_false_2+1;
							mes "[Moonho Ahn]";
							mes "Let's see...";
							mes "It's ^0000FFEven^000000, isn't it?";
							next;
							mes "[Moonho Ahn]";
							mes "Looks like I won!";
							mes "Hahahahahahaha!";
							next;
						}
						if (.@number_right_2 == 2) {
							set .@win,.@win+1;
							mes "[Moonho Ahn]";
							mes "Nice job. I didn't";
							mes "expect for you to";
							mes "really beat me...";
							mes "Hahahahahahah~";
							next;
							break;
						}
						else if (.@number_false_2 == 2) {
							set .@lose,.@lose+1;
							mes "[Moonho Ahn]";
							mes "Hahaha! I'm sorry, but";
							mes "I won. I guess I still got it!";
							next;
							break;
						}
					}
				}
				if (.@win == 2) {
					mes "[Moonho Ahn]";
					mes "You really are amazing!";
					mes "It's time that the title of";
					mes "White Meteor be passed";
					mes "onto someone more worthy...";
					mes "You are the new White Meteor!";
					next;
					mes "[" + strcharinfo(0) +"]";
					mes "Really? Uh, can I get";
					mes "that in writing please?";
					mes "It'd mean so much to me!";
					next;
					mes "[Moonho Ahn]";
					mes "Ah, you want a letter";
					mes "of recommendation or";
					mes "my seal of approval, right?";
					mes "Heh, you must be very proud.";
					mes "Hold still for a second.";
					next;
					mes "^3355FF*Scribble Scribble*^000000";
					next;
					mes "[" + strcharinfo(0) + "]";
					mes "What does this";
					mes "scribble on my";
					mes "wrist mean?";
					next;
					mes "[Moonho Ahn]";
					mes "That is the sign of";
					mes "the White Meteor.";
					mes "Everyone that knows";
					mes "me will recognize it";
					mes "and its authenticity.";
					next;
					mes "[" + strcharinfo(0) + "]";
					mes "Thank you so much";
					mes "for the fun game!";
					mes "I better get going now,";
					mes "but maybe we can play";
					mes "again sometime~";
					next;
					mes "[Moonho Ahn]";
					mes "Hahahaa!";
					mes "After so long, it";
					mes "feels good to have";
					mes "played with a worthy";
					mes "opponent. Thank you~";
					changequest 3124,3125;
					set zdan_edq,7;
					close;
				}
				else if (.@lose == 2) {
					mes "[Moonho Ahn]";
					mes "I'm sorry, but I've won";
					mes "the game. I had a really";
					mes "great time, and I could";
					mes "tell that it was really close.";
					mes "It's been a while since I've";
					mes "had this much fun gambling~";
					next;
					mes "[Moonho Ahn]";
					mes "Oh, did you want to";
					mes "play again? I can tell";
					mes "that you're a bit eager to";
					mes "beat me so you're welcome";
					mes "to play me again anytime.";
					mes "I'll be right here, hahaha~";
					close;
				}
				else {
					mes "[Moonho Ahn]";
					mes "Ooh, this is exciting~";
					mes "We each both won a round:";
					mes "the next round will decide";
					mes "who wins and loses!";
					next;
					mes "[Moonho Ahn]";
					mes "It's my turn to shake";
					mes "the coins. Let's see";
					mes "how you fare this time...";
					next;
					mes "^3355FF*Shake Shake*";
					mes "Guess! Is it";
					mes "Odd or Even?^000000";
					next;
					while(1) {
						set .@number,rand(1,2);
						input .@input$;
						if (((.@input$ == "Odd") && (.@number == 1)) || ((.@input$ == "Even") && (.@number == 2))) {
							set .@number_right_3,.@number_right_3+1;
							mes "[Moonho Ahn]";
							mes "^0000FF"+.@input$+"^000000?";
							mes "Okay, you won.";
							next;
							if ((.@number_right_3 < 2) && (.@number_false_3 < 2)) {
								mes "^3355FF*Shake Shake*";
								mes "Guess! Is it";
								mes "Odd or Even?^000000";
								next;
							}
						}
						else if (((.@input$ == "Odd") && (.@number == 2)) || ((.@input$ == "Even") && (.@number == 1))) {
							set .@number_false_3,.@number_false_3+1;
							mes "[Moonho Ahn]";
							mes "Well, it's ^0000FF"+.@input$+"^000000.";
							mes "Looks like I won.";
							next;
							if ((.@number_right_3 < 2) && (.@number_false_3 <2)) {
								mes "^3355FF*Shake Shake*";
								mes "Guess! Is it";
								mes "Odd or Even?^000000";
								next;
							}
						}
						if (.@number_right_3 == 2) {
							mes "[Moonho Ahn]";
							mes "Oh... You won...";
							set .@win,.@win+1;
							next;
							break;
						}
						else if (.@number_false_3 == 2) {
							mes "[Moonho Ahn]";
							mes "Hahaha! I'm sorry, but";
							mes "I won. I guess I still got it!";
							set .@lose,.@lose+1;
							next;
							break;
						}
						if ((.@input$ != "Even") && (.@input$ !="Odd")) {
							mes "[Moonho Ahn]";
							mes "You can only declare";
							mes "your guess as ^0000FFOdd^000000 or ^0000FFEven^000000.";
							mes "Please try entering it again.";
							next;
						}
					}
					if (.@win >= 2) {
						mes "[Moonho Ahn]";
						mes "You really are amazing!";
						mes "It's time that the title of";
						mes "White Meteor be passed";
						mes "onto someone more worthy...";
						mes "You are the new White Meteor!";
						next;
						mes "[" + strcharinfo(0) +"]";
						mes "Really? Uh, can I get";
						mes "that in writing please?";
						mes "It'd mean so much to me!";
						next;
						mes "[Moonho Ahn]";
						mes "Ah, you want a letter";
						mes "of recommendation or";
						mes "my seal of approval, right?";
						mes "Heh, you must be very proud.";
						mes "Hold still for a second.";
						next;
						mes "^3355FF*Scribble Scribble*^000000";
						next;
						mes "[" + strcharinfo(0) + "]";
						mes "What does this";
						mes "scribble on my";
						mes "wrist mean?";
						next;
						mes "[Moonho Ahn]";
						mes "That is the sign of";
						mes "the White Meteor.";
						mes "Everyone that knows";
						mes "me will recognize it";
						mes "and its authenticity.";
						next;
						mes "[" + strcharinfo(0) + "]";
						mes "Thank you so much";
						mes "for the fun game!";
						mes "I better get going now,";
						mes "but maybe we can play";
						mes "again sometime~";
						next;
						mes "[Moonho Ahn]";
						mes "Hahahaa!";
						mes "After so long, it";
						mes "feels good to have";
						mes "played with a worthy";
						mes "opponent. Thank you~";
						changequest 3124,3125;
						set zdan_edq,7;
						close;
					}
					else if (.@lose == 2) {
						mes "[Moonho Ahn]";
						mes "I'm sorry, but I've won";
						mes "the game. I had a really";
						mes "great time, and I could";
						mes "tell that it was really close.";
						mes "It's been a while since I've";
						mes "had this much fun gambling~";
						next;
						mes "[Moonho Ahn]";
						mes "Oh, did you want to";
						mes "play again? I can tell";
						mes "that you're a bit eager to";
						mes "beat me so you're welcome";
						mes "to play me again anytime.";
						mes "I'll be right here, hahaha~";
						close;
					}
				}
			}
			else {
				mes "[Moonho Ahn]";
				mes "Hm? You don't even";
				mes "have 500 zeny to play";
				mes "a game? Well, whenever";
				mes "you feel like playing Coin";
				mes "Shake, drop on by.";
				close;
			}
			break;
		case 2:
			mes "[Moonho Ahn]";
			mes "This is basically";
			mes "a guessing game played";
			mes "in rounds, best 2 out of 3.";
			mes "One of us will be the coin";
			mes "shaker, and the other will";
			mes "be the guesser.";
			next;
			mes "[Moonho Ahn]";
			mes "The coin shaker begins";
			mes "the round by grabbing";
			mes "a random amount of coins,";
			mes "shaking them in his closed";
			mes "hands, and then stopping,";
			mes "keeping the coins concealed.";
			next;
			mes "[Moonho Ahn]";
			mes "The other person, the";
			mes "guesser, will then declare";
			mes "a guess on whether the total";
			mes "value of the coins is ^0000FFOdd^000000 or";
			mes "^0000FFEven^000000. Afterwards, the coin";
			mes "shaker reveals the coins...";
			next;
			mes "[Moonho Ahn]";
			mes "If the guesser is right,";
			mes "then he wins the round.";
			mes "However, if he guessed";
			mes "wrong, then it's a victory";
			mes "for the coin shaker. ";
			next;
			mes "[Moohno Ahn]";
			mes "We each take turns,";
			mes "switching roles.";
			mes "If it's your turn to";
			mes "guess, remember to";
			mes "call out ^0000FFOdd^000000 or ^0000FFEven^000000.";
			next;
			mes "[Moonho Ahn]";
			mes "Lastly, I'm the house,";
			mes "so I'll charge a game";
			mes "participation fee. It's";
			mes "not much, just 500 zeny.";
			mes "We're only playing for fun,";
			mes "not to break the bank.";
			close;
		}
	}
	mes "[Moonho Ahn]";
	mes "I still remember that";
	mes "last legendary match with";
	mes "Dalho Kwak. Now that man was";
	mes "a worthy opponent: he could";
	mes "read others' hands three times";
	mes "faster than the normal eye!";
	next;
	mes "[Moonho Ahn]";
	mes "God, I miss those";
	mes "days competing against";
	mes "him. I wonder how he's";
	mes "been doing lately...";
	close;
}

payon,192,176,3	script	Gooho Ahn	903,{
	if (zdan_edq == 12) {
		mes "[Gooho Ahn]";
		mes "Oh, hello";
		mes "adventurer.";
		mes "Um, was there";
		mes "something you wanted?";
		emotion ET_QUESTION, playerattached();
		next;
		switch(select("Rogue Guild's Decoding Request")) {
		case 1:
			mes "[Gooho Ahn]";
			mes "Ah, Marybell sent you?";
			mes "I see. I've been struggling";
			mes "trying to decrypt this letter.";
			mes "Did you want to take a look?";
			next;
			mes "[Gooho Ahn]";
			mes "This looks like a piece";
			mes "of blank paper, but if you";
			mes "add just the right amount";
			mes "of heat and light, you can";
			mes "read its contents. This is";
			mes "some powerful encryption!";
			emotion ET_HUK, playerattached();
			next;
			mes "[Gooho Ahn]";
			mes "So far, I've only decrypted";
			mes "just a portion of the letter's";
			mes "content. Unfortunately, I ran";
			mes "out of the materials I need to";
			mes "measure how much light and heat";
			mes "I need to unlock this message.";
			next;
			mes "[Gooho Ahn]";
			mes "I'm pretty swamped over";
			mes "here, but if you're working";
			mes "for Marybell, I don't think";
			mes "you'd mind doing a favor";
			mes "for me. Would you gather";
			mes "the materials I need?";
			next;
			mes "[Gooho Ahn]";
			mes "I need";
			mes "^0000FF20 Live Coals^000000,";
			mes "^0000FF1 Matchstick^000000,";
			mes "^0000FF2 Alcohol^000000, and";
			mes "^0000FF10 Burning Hearts^000000.";
			next;
			mes "[Gooho Ahn]";
			mes "Once I have the things";
			mes "I need, I should be able";
			mes "to decrypt the rest of";
			mes "this secret Z Gang letter.";
			changequest 3129,3130;
			set zdan_edq,13;
			close;
		}
	}
	if (zdan_edq == 13) {
		mes "[Gooho Ahn]";
		mes "So did you bring the";
		mes "things I need to measure";
		mes "how much light and heat";
		mes "I need to reveal the rest";
		mes "of this secret Z Gang letter?";
		next;
		if ((countitem(7098) > 19) && (countitem(970) > 1) && (countitem(7035) > 0) && (countitem(7097) > 9)) {
			mes "[Gooho Ahn]";
			mes "Oh, good. Thanks a lot.";
			mes "Now I should do my part,";
			mes "and decrypt the rest of";
			mes "this letter. Mm... Just";
			mes "give me a second to";
			mes "make some adjustments...";
			next;
			delitem 7098,20; //Live_Coal
			delitem 970,2; //Alchol
			delitem 7035,1; //Matchstick
			delitem 7097,10; //Burning_Heart
			emotion ET_AHA;
			mes "[Gooho Ahn]";
			mes "Oh, what? This language...";
			mes "It's ancient! Aegye hasn't";
			mes "been used in... centuries.";
			mes "I've seen Aegye used in the";
			mes "Book of Forbidden Mystery";
			mes "before it was stolen.";
			next;
			mes "[Gooho Ahn]";
			mes "There's a legend about";
			mes "an ancient civilization that";
			mes "set the foundation for our";
			mes "modern society. The Aegye";
			mes "language was considered";
			mes "a subcultural phenomenon.";
			next;
			mes "[Gooho Ahn]";
			mes "Aegye was popular among";
			mes "children and rebellious";
			mes "teens, but was considered";
			mes "vulgar until it entered the";
			mes "mainstream. Then, somehow... ";
			next;
			mes "[Gooho Ahn] ";
			mes "Aegye destroyed that";
			mes "ancient people's lingual";
			mes "system and civilization";
			mes "once it was popularized.";
			mes "That is why there is strong";
			mes "Aegye censorship today.";
			next;
			mes "[Gooho Ahn] ";
			mes "In fact, the existence";
			mes "of Aegye is supposed to";
			mes "be a huge secret. That";
			mes "language is responsible";
			mes "for one of the greatest";
			mes "disasters of all time.";
			next;
			mes "[" + strcharinfo(0) + "]";
			mes "...............................";
			mes "So what's the letter say?";
			next;
			mes "[Gooho Ahn] ";
			mes "Mm... Let me check...";
			mes "My knowledge in Aegye";
			mes "is very limited, so...";
			next;
			mes "[Gooho Ahn] ";
			mes "^0000ffWeii arr prowd Z G gna^000000";
			mes "^0000ffAynoen hwo sspotp uys^000000";
			mes "^0000ffwlil eb kckide on htier ssa!^000000";
			next;
			mes "[" + strcharinfo(0) + "]";
			mes "What...?";
			next;
			mes "[Gooho Ahn] ";
			mes "That's the nature";
			mes "of Aegye: it is chaotic,";
			mes "and barely legible!";
			mes "Regardless, this letter";
			mes "is invaluable as research";
			mes "material for my studies.";
			next;
			mes "[Gooho Ahn] ";
			mes "It will be enough";
			mes "for Marybell if you";
			mes "deliver the message to";
			mes "her verbatim. Listen";
			mes "to me carefully, and";
			mes "maybe write this down.";
			next;
			mes "[Gooho Ahn] ";
			mes "^0000ffWeii arr prowd Z G gna^000000";
			mes "^0000ffAynoen hwo sspotp uys^000000";
			mes "^0000ffwlil eb kckide on htier ssa!^000000";
			changequest 3130,3131;
			set zdan_edq,14;
			close;
		}
		else {
			mes "[Gooho Ahn]";
			mes "Please hurry!";
			mes "I'm sure Marybell is";
			mes "waiting to learn the";
			mes "whereabouts of the Z Gang,";
			mes "and this letter might have";
			mes "a clue. Oh, and remember...";
			next;
			mes "[Gooho Ahn] ";
			mes "I need";
			mes "^0000FF20 Live Coals^000000,";
			mes "^0000FF1 Matchstick^000000,";
			mes "^0000FF2 Alcohol^000000, and";
			mes "^0000FF10 Burning Hearts^000000.";
			next;
			mes "[Gooho Ahn]";
			mes "Once I have the things";
			mes "I need, I should be able";
			mes "to decrypt the rest of";
			mes "this secret Z Gang letter.";
			close;
		}
	}
	if (zdan_edq == 14) {
		mes "[Gooho Ahn] ";
		mes "Please tell Marybell";
		mes "the contents of the";
		mes "Z Gang's letter so that";
		mes "I can keep the original";
		mes "document for research purposes.";
		next;
		mes "[Gooho Ahn] ";
		mes "^0000ffWeii arr prowd Z G gna^000000";
		mes "^0000ffAynoen hwo sspotp uys^000000";
		mes "^0000ffwlil eb kckide on htier ssa!^000000";
		close;
	}
	mes "[Gooho Ahn] ";
	mes "Encryption is the art of";
	mes "hiding truth in layers of";
	mes "secrecy. My specialty is";
	mes "in unraveling the truth";
	mes "by breaking encryption.";
	close;
}

moc_ruins,90,67,3	script	Suspicious Man#1	4W_M_03,4,4,{
	if (zdan_edq == 9) {
		if ($@zdan == 0)
			callsub S_Quest;
		else {
			mes "[Suspicious Man]";
			if (strnpcinfo(2) == "1") {
				mes "...............................";
				mes "...............................";
				mes "............................... ";
			}
			else
				mes "... ...";
			next;
			mes "[" + strcharinfo(0) + "]";
			mes "Something's not quite";
			mes "right. I should come back";
			mes "and investigate this area";
			mes "later when there are fewer";
			mes "people watching...";
		}
	}
	else if (zdan_edq == 10) {
		if ($@zdan > 0) {
			mes "[" + strcharinfo(0) + "]";
			mes "Nuts! I was supposed";
			mes "to try to do this secretly!";
			mes "I better try to investigate";
			mes "this area again when no";
			mes "one is around here.";
		}
		else {
			set $@zdan,1;
			initnpctimer;
			mes "[????]";
			mes "Eeek...!";
			next;
			mes "[" + strcharinfo(0) + "]";
			mes "Haha! Got you!";
			mes "You're an informer for";
			mes "the Z Gang, aren't you?";
			next;
			mes "[????]";
			mes "I... I... d-don't";
			mes "know what you're";
			mes "talking about!";
			mes "I'm innocent!";
			next;
			mes "[" + strcharinfo(0) + "]";
			mes "Then you're telling";
			mes "me this note didn't";
			mes "just fall out of your";
			mes "pocket? What's this";
			mes "about trying to kill me?";
			next;
			mes "[????]";
			mes "Th-that's...";
			mes "I'm not--That...!";
			next;
			mes "[" + strcharinfo(0) + "]";
			mes "You better confess, or";
			mes "I'll drag you over to the";
			mes "Prontera Knightage or the";
			mes "Rogue Guild to take care";
			mes "of you. In fact, let's just";
			mes "head over to Prontera...";
			next;
			mes "[Z Gang Informer]";
			mes "N-no! I'll tell you";
			mes "everything! Please!";
			mes "My mother's old! I've";
			mes "got kids to feed!";
			mes "I... I can't go to jail!";
			next;
			mes "[" + strcharinfo(0) + "]";
			mes "Alright.";
			mes "Let's start by you";
			mes "telling me where";
			mes "I can find the Z Gang.";
			next;
			mes "[Z Gang Informer]";
			mes "I... I really don't";
			mes "know where to find them.";
			mes "I'm at the bottom of the";
			mes "food chain, I just follow";
			mes "their written instructions.";
			next;
			mes "[" + strcharinfo(0) + "]";
			mes "You know what?";
			mes "Never mind. I'm won't";
			mes "take you to be jailed by";
			mes "the Prontera Knights.";
			mes "I'll drop you off";
			mes "at the Rogues.";
			next;
			mes "[Z Gang Informer]";
			mes "...............................";
			mes "Their secret hideout is in";
			mes "South Morocc, and you can't";
			mes "enter the place without the";
			mes "secret password.";
			next;
			mes "[" + strcharinfo(0) + "]";
			mes "Nice. Now, you better";
			mes "stop running with the";
			mes "Z Gang. Otherwise, I'm";
			mes "not going to be so merciful";
			mes "the next time I see you.";
			next;
			mes "[Z Gang Informer]";
			mes "Anything you want!";
			mes "J-just let me liiive!";
			next;
			mes "[" + strcharinfo(0) + "]";
			mes "I should head back to";
			mes "Marybell, and see if she's";
			mes "learned any new information.";
			changequest 3127,3128;
			set zdan_edq,11;
			set $@zdan,0;
			stopnpctimer;
		}
	}
	else if (zdan_edq < 9) {
		if (strnpcinfo(2) == "1")
			mes "[Suspicious Man]";
		else
			mes "[Thug]";
		mes "What? Get lost!";
		mes "Listen, you don't";
		mes "want to mess with";
		mes "me. Just. Don't.";
	}
	else if (zdan_edq > 10 && zdan_edq < 15) {
		mes "[Z Gang Informer]";
		mes "Whoa, leave me alone!";
		mes "I'm just standing here";
		mes "on the road, I didn't";
		mes "do anything wrong!";
	}
	else if (zdan_edq > 14) {
		mes "[Former Z Gang Informer]";
		mes "You don't have to";
		mes "worry about me anymore.";
		mes "I've turned over a new";
		mes "leaf, got a real job,";
		mes "that sort of deal.";
	}
	else {
		if (strnpcinfo(2) == "1")
			mes "[Suspicious Man]";
		else
			mes "[Thug]";
		mes "What? Get lost!";
		mes "Listen, you don't";
		mes "want to mess with";
		mes "me. Just. Don't.";
	}
	close;

OnTimer30000:
	set $@zdan,0;
	end;

OnInit:
	hideonnpc "Suspicious Man#2";
	end;

OnTouch_:
	if (zdan_edq == 9 && $@zdan == 0)
		callsub S_Quest;
	end;

S_Quest:
	set $@zdan,1;
	initnpctimer;
	mes "[????]";
	mes "I know you've";
	mes "been pursuing us!";
	mes "Grrrr... DIE NOW!";
	specialeffect EF_STEAL;
	next;
	mes "[" + strcharinfo(0) + "]";
	mes "Wh-who are you?";
	next;
	mes "[????]";
	mes "Y-you're stronger";
	mes "than I thought!";
	mes "Run awaaaaay!";
	if (strnpcinfo(2) == "1") {
		hideonnpc "Suspicious Man#1";
		hideoffnpc "Suspicious Man#2";
	}
	else {
		hideonnpc "Suspicious Man#2";
		hideoffnpc "Suspicious Man#1";
	}
	set $@zdan,0;
	stopnpctimer;
	next;
	mes "[" + strcharinfo(0) + "] ";
	mes "Huh? That man must";
	mes "have dropped this";
	mes "note in his haste";
	mes "to get away from here.";
	mes "Let's see what it says...";
	next;
	mes "^666666Kill " + strcharinfo(0) + ", meow.";
	mes "That arrogant do-gooder";
	mes "is looking into us too much.";
	mes "Fail to kill him, and death";
	mes "will be too good for you, meow.^000000";
	next;
	mes "[" + strcharinfo(0) + "]";
	mes "That must have been";
	mes "an informer for the Z Gang.";
	mes "He can't have gotten too far:";
	mes "I have a chance to catch him!";
	changequest 3126,3127;
	set zdan_edq,10;
	close;
}
moc_ruins,75,171,3	duplicate(Suspicious Man#1)	Suspicious Man#2	4W_M_03,4,4

moc_fild17,209,235,0	script	Odd Slab	111,4,4,{
	if (((zdan_edq == 15) || (zdan_edq == 16) || (zdan_edq == 17)) && ($@door2 == 0)) {
		mes "[Odd Slab]";
		mes "^FF0000*Creak Creak*";
		mes "Etner sspawrod.^000000";
		next;
		emotion ET_HUK, playerattached();
		mes "[" + strcharinfo(0) + "]";
		mes "This slab is speaking";
		mes "to me! I... I think. Now";
		mes "where have I heard";
		mes "talking like this before?";
		next;
		mes "[Odd Slab]";
		mes "^FF0000*Creak Creak*";
		mes "Etner sspawrod.^000000";
		next;
		emotion ET_AHA, playerattached();
		mes "[" + strcharinfo(0) + "]";
		mes "Oh, right! This is that";
		mes "trashy language, Aegye,";
		mes "that Gooho Ahn told me";
		mes "about. It still sounds";
		mes "like... A poor excuse";
		mes "for language to me.";
		next;
		mes "[Odd Slab]";
		mes "^FF0000*Creak Creak*";
		mes "Etner sspawrod.^000000";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "Well, I guess";
		mes "I should talk to it. Um...";
		next;
		input .@input$;
		if (.@input$ == "Weii arr prowd Z G gna") {
			mes "[Odd Slab]";
			mes "^FF0000*Creak Creak*";
			mes "Etner n2d sspawrod.^000000";
			next;
			mes "[" + strcharinfo(0) + "]";
			mes "Whoa! It's saying";
			mes "something different now!";
			mes "I must be doing alright.";
			mes "Now what do I say?";
			next;
			input .@input$;
			if (.@input$ == "Aynoen hwo sspotp uys") {
				mes "[Odd Slab]";
				mes "^FF0000*Creak Creak*";
				mes "Etner r3d sspawrod.^000000";
				next;
				mes "[" + strcharinfo(0) + "]";
				mes "Heh! I'm on the right";
				mes "track. Now what do I say?";
				next;
				input .@input$;
				if (.@input$ == "wlil eb kckide on htier ssa!") {
					mes "[Odd Slab]";
					mes "*Creak Creak*";
					mes "*Creak Creak*";
					next;
					mes "[" + strcharinfo(0) + "]";
					mes "Um... Now what...?";
					next;
					if ($@door2 == 0) {
						set $@door2,1;
						mes "Waaaah! Waaah!";
						next;
						mes "[" + strcharinfo(0) + "]";
						mes "What th--?! What's";
						mes "happening?! I'm being";
						mes "sucked away somewhere!";
						changequest 3132,3133;
						set zdan_edq,16;
						close2;
						warp "z_agit",98,40;
						end;
					}
					else {
						mes "[" + strcharinfo(0) + "]";
						mes "Hm? Did I do something";
						mes "wrong? It just stopped";
						mes "working all of a sudden.";
						next;
						mes "[Odd Slab]";
						mes "^FF0000Rrsoy, rrsoy.";
						mes "Ai cn'at elt yoo ni ofr nwo. Plzea ecmo ckba tela.^000000";
						next;
						mes "^666666*Pzzzzz*^000000";
						next;
						mes "[" + strcharinfo(0) + "]";
						mes "I think...";
						mes "I think I should";
						mes "try this again.";
						close;
					}
				}
				else {
					mes "[Odd Slab]";
					mes "^666666*Pzzzzz*^000000";
					next;
					mes "[" + strcharinfo(0) + "]";
					mes "Huh? This talking slab";
					mes "thing isn't working now.";
					mes "Hello? What happened to you?";
					mes "Hey! Talk to me, will you?";
					set $@door2,0;
					close;
				}
			}
			else {
				mes "[Odd Slab]";
				mes "^666666*Pzzzzz*^000000";
				next;
				mes "[" + strcharinfo(0) + "]";
				mes "Huh? This talking slab";
				mes "thing isn't working now.";
				mes "Hello? What happened to you?";
				mes "Hey! Talk to me, will you?";
				set $@door2,0;
				close;
			}
		}
		else {
			mes "[Odd Slab]";
			mes "^666666*Pzzzzz*^000000";
			next;
			mes "[" + strcharinfo(0) + "]";
			mes "Huh? This talking slab";
			mes "thing isn't working now.";
			mes "Hello? What happened to you?";
			mes "Hey! Talk to me, will you?";
			set $@door2,0;
			close;
		}
	}
	if (((zdan_edq == 15) || (zdan_edq == 16) || (zdan_edq == 17)) && ($@door2 > 0)) {
		mes "[Odd Slab]";
		mes "*Creak Creak*";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "This slab looks pretty";
		mes "strange. Is it making";
		mes "noises? Hmm... Maybe this";
		mes "is a clue to the Z Gang!";
		mes "I'll come back later";
		mes "to investigate this~";
		close;
	}
	if (zdan_edq > 17) {
		mes "[" + strcharinfo(0) + "]";
		mes "This used to be";
		mes "where the Z Gang";
		mes "would hide out.";
		mes "Good riddance! ";
		close;

	}
	mes "[" + strcharinfo(0) + "]";
	mes "This is a peculiar";
	mes "looking slab. It sounds";
	mes "like... What are those";
	mes "noises? Huh. Weird.";
	mes "It's like it's just";
	mes "talking gibberish.";
	close;

OnInit:
	set $@zdan,0;
	set $@door2,0;
	set $@mosnter,0;
	end;

OnTouch:
	if (zdan_edq == 15) {
		mes "^FF0000*Creak Creak*^000000";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "Did I just...";
		mes "Hear something";
		mes "around here?";
		close;
	}
	end;
}

z_agit,98,44,0	script	EntranceCheck#1	-1,10,10,{
OnTouch_:
	if (((zdan_edq == 15) || (zdan_edq == 16)) && ($@monster_zgang == 0)) {
		set $@monster_zgang,1;
		donpcevent "#zdan_broad::OnEnable";
	}
	else if (((zdan_edq == 15) || (zdan_edq == 16)) && ($@monster_zgang > 0)) {
		end;
	}
	else if (zdan_edq == 17) {
		donpcevent "#ZGuard::OnDisable";
		hideoffnpc "Louis";
		hideoffnpc "Martha";
		hideoffnpc "Catfoii";
	}
	else {
		mes "[" + strcharinfo(0) + "]";
		mes "Where am I...?";
		mes "Something has gone";
		mes "terribly wrong, hasn't";
		mes "it? Let me go baaaack~!";
		close2;
		set $@monster_zgang,0;
		set $@door2,0;
		warp "moc_fild17",209,235;
	}
	end;
}

z_agit,98,92,0	script	EntranceCheck#2	-1,10,10,{
OnTouch_:
	if (((zdan_edq == 15) || (zdan_edq == 16)) && ($@monster_zgang == 0)) {
		set $@monster_zgang,1;
		donpcevent "#zdan_broad::OnEnable";
	}
	else if (((zdan_edq == 15) || (zdan_edq == 16)) && ($@monster_zgang > 0)) {
		end;
	}
	else if (zdan_edq == 17) {
		donpcevent "#ZGuard::OnDisable";
		hideoffnpc "Louis";
		hideoffnpc "Martha";
		hideoffnpc "Catfoii";
	}
	else {
		mes "[" + strcharinfo(0) + "]";
		mes "Where am I...?";
		mes "Something has gone";
		mes "terribly wrong, hasn't";
		mes "it? Let me go baaaack~! ";
		close2;
		set $@monster_zgang,0;
		set $@door2,0;
		warp "moc_fild17",209,235;
	}
	end;
}

z_agit,1,1,0	script	#zdan_broad	111,{
OnInit:
	disablenpc "#zdan_broad";
	stopnpctimer;
	end;

OnEnable:
	enablenpc "#zdan_broad";
	initnpctimer;
	hideonnpc "Louis";
	hideonnpc "Martha";
	hideonnpc "Catfoii";
	end;

OnDisable:
	disablenpc "#zdan_broad";
	stopnpctimer;
	end;

OnTimer3000:
	killmonster "z_agit","#ZGuard::OnMyMobDead";
	mapannounce "z_agit","Catfoii: Err? I heard something, meow! We must be under attack, meow!",bc_map,"0xFFFF00";
	end;

OnTimer5000:
	mapannounce "z_agit","Louis: Hey, Martha! Are you the one who just got in?",bc_map,"0xFFFF00";
	end;

OnTimer7000:
	mapannounce "z_agit","Martha: Louis, are you blind? I've been next to you this whole time! We'd better hide first.",bc_map,"0xFFFF00";
	end;

OnTimer9000:
	mapannounce "z_agit","Louis: We have an intruder! Hey, Catfoii, what happened? Did you leave the entrance open?",bc_map,"0xFFFF00";
	end;

OnTimer11000:
	mapannounce "z_agit","Catfoii: No, impossible, meow~! I have a photographic memory!",bc_map,"0xFFFF00";
	end;

OnTimer13000:
	mapannounce "z_agit","Martha: Shut up, both of you! I don't know who you are, but you must have a lot of guts to mess with the Z Gang!",bc_map,"0xFFFF00";
	end;

OnTimer15000:
	mapannounce "z_agit","Louis: Catfoii, summon the soldiers! This is an emergency! Stop the intruder!",bc_map,"0xFFFF00";
	end;

OnTimer18000:
	mapannounce "z_agit","Catfoii: I haven't seen how strong they are, but... Meowkay. Guys, go out and fight!",bc_map,"0xFFFF00";
	end;

OnTimer21000:
	donpcevent "#ZGuard::OnEnable";
	end;

OnTimer300000:
	killmonster "z_agit","#ZGuard::OnMyMobDead";
	donpcevent "#zdan_broad::OnDisable";
	set $@monster_zgang,0;
	set $@door2,0;
	end;

OnTimer350000:
	mapwarp "z_agit","moc_fild17",209,235;
	hideonnpc "Louis";
	hideonnpc "Martha";
	hideonnpc "Catfoii";
	set $@monster_zgang,0;
	set $@door2,0;
	end;
}

-	script	#ZGuard	-1,{
OnInit:
	disablenpc "#ZGuard";
	end;

OnEnable:
	enablenpc "#ZGuard";
	initnpctimer;
	monster "z_agit",97,78,"Catfoii's Guard",1479,1,"#ZGuard::OnMyMobDead";
	monster "z_agit",98,79,"Catfoii's Guard",1479,1,"#ZGuard::OnMyMobDead";
	monster "z_agit",96,81,"Catfoii's Guard",1523,1,"#ZGuard::OnMyMobDead";
	end;

OnDisable:
	killmonster "z_agit","#ZGuard::OnMyMobDead";
	stopnpctimer;
	end;

OnReset:
	killmonster "z_agit","#ZGuard::OnMyMobDead";
	set $@monster_zgang,0;
	end;

OnMyMobDead:
	if (mobcount("z_agit","#ZGuard::OnMyMobDead") < 1) {
		mes "[" + strcharinfo(0) + "]";
		mes "You cowardly Z Gang!";
		mes "Come out and surrender!";
		mes "I've defeated your monster";
		mes "soldiers already!";
		next;
		mes "[Martha]";
		mes "What should we do?";
		next;
		mes "[Catfoii]";
		mes "This is our greatest";
		mes "crisis ever! I don't";
		mes "know what to do, meow!";
		next;
		mes "[Louis]";
		mes "The Z Gang's not going";
		mes "to surrender yet! Come";
		mes "forth, my loyal servants";
		mes "of the darkness!";
		next;
		mes "[Catfoii]";
		mes "No-no-no-meow!";
		mes "Not that button!";
		next;
		mes "[Louis]";
		mes "Huh? Why...?";
		next;
		set zdan_edq,17;
		donpcevent "#ZGuard::OnDisable";
		hideoffnpc "Louis";
		hideoffnpc "Martha";
		hideoffnpc "Catfoii";
		close;
	}
	end;

OnTimer300000:
	mapwarp "z_agit","moc_fild17",209,235;
	donpcevent "#zdan_broad::OnDisable";
	killmonster "z_agit","#ZGuard::OnMyMobDead";
	set $@monster_zgang,0;
	set $@door2,0;
	donpcevent "#ZGuard::OnDisable";
	stopnpctimer;
	end;
}

z_agit,97,101,3	script	Louis	931,{
	if (checkweight(907,200) == 0) {
		mes "[Louis]";
		mes "You mess with the";
		mes "Z Gang, you--wait.";
		mes "Let's just do this later.";
		mes "You know, when you're carrying";
		mes "fewer items with you. Then,";
		mes "we can rumble in peace.";
		close;
	}
	if (zdan_edq == 17) {
		mes "[Louis]";
		mes "What happened?";
		mes "The big guns were";
		mes "supposed to come out!";
		mes "Why aren't we invisible";
		mes "anymore? What happened?";
		next;
		mes "[Catfoii]";
		mes "You pushed the button";
		mes "to turn off the invisibility,";
		mes "meow! Did you really have";
		mes "to ask?! It's your fault!";
		next;
		mes "[Martha]";
		mes "Argh! I should never";
		mes "have trusted that fool!";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "So... Z Gang...";
		mes "And cat. It's time";
		mes "to turn you in.";
		next;
		mes "[Louis]";
		mes "I... I can't accept";
		mes "our defeat! The Z Gang";
		mes "won't go down so easily!";
		mes "Come forth, my demon";
		mes "spawn minions of doom!";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "I don't think";
		mes "they're coming.";
		next;
		mes "[Louis]";
		mes "...............................";
		mes "We surrender. Argh, but";
		mes "we were so close to";
		mes "conquering the world!";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "Now if you'll just";
		mes "hand over the Book";
		mes "of Forbidden Mystery...";
		next;
		mes "[Catfoii]";
		mes "Louis, meow!";
		mes "Don't do it!";
		next;
		mes "[Martha]";
		mes "Let's just surrender,";
		mes "Louis. It looks like";
		mes "the Z Gang is finished.";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "Anyway, the Rogue Guild";
		mes "is on its way to come get";
		mes "you guys. You won't be able";
		mes "to escape this time.";
		next;
		mes "[Louis]";
		mes "Awwww, man!";
		mes "Fine, take the stupid";
		mes "ol' book. I couldn't";
		mes "use it that good anyway.";
		next;
		mes "[Louis]";
		mes "Catfoii, would you";
		mes "lead this adventurer guy";
		mes "to the exit? I can't bear";
		mes "to look at the face that's";
		mes "caused my tragic downfall...";
		next;
		mes "[Catfoii]";
		mes "Fine, meow.";
		mes "I understand.";
		mes "But how did we lose?!";
		close2;
		getitem 7724,1; //Forbidden_Secret_Art
		changequest 3133,3134;
		set zdan_edq,18;
		mapwarp "z_agit","moc_fild17",209,235;
		set $@monster_zgang,0;
		set $@door2,0;
		hideonnpc "Louis";
		hideonnpc "Martha";
		hideonnpc "Catfoii";
		stopnpctimer;
		close;
	}
	else {
		mes "[" + strcharinfo(0) + "]";
		mes "Where am I...?";
		mes "Something has gone";
		mes "terribly wrong, hasn't";
		mes "it? Let me go baaaack~! ";
		set zdan_edq,15;
		set $@monster_zgang,0;
		set $@door2,0;
		close2;
		warp "moc_fild17",209,235;
		end;
	}

OnInit:
	hideonnpc "Louis";
	end;
}

z_agit,99,101,3	script	Martha	101,{
	if (checkweight(907,200) == 0) {
		mes "[Martha]";
		mes "Hey, you're carrying";
		mes "too much stuff. You'd";
		mes "better put some of your";
		mes "items away first in Kafra";
		mes "Storage or something.";
		close;
	}
	if (zdan_edq == 17) {
		mes "[Martha]";
		mes "Argh...! What'd ";
		mes "you do, Louis?";
		next;
		mes "[Louis]";
		mes "What happened?";
		mes "The big guns were";
		mes "supposed to come out!";
		mes "Why aren't we invisible";
		mes "anymore? What happened?";
		next;
		mes "[Catfoii]";
		mes "You pushed the button";
		mes "to turn off the invisibility,";
		mes "meow! Did you really have";
		mes "to ask?! It's your fault!";
		next;
		mes "[Martha]";
		mes "Argh! I should never";
		mes "have trusted that fool!";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "So... Z Gang...";
		mes "And cat. It's time";
		mes "to turn you in.";
		next;
		mes "[Louis]";
		mes "I... I can't accept";
		mes "our defeat! The Z Gang";
		mes "won't go down so easily!";
		mes "Come forth, my demon";
		mes "spawn minions of doom!";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "I don't think";
		mes "they're coming.";
		next;
		mes "[Louis]";
		mes "...............................";
		mes "We surrender. Argh, but";
		mes "we were so close to";
		mes "conquering the world!";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "Now if you'll just";
		mes "hand over the Book";
		mes "of Forbidden Mystery...";
		next;
		mes "[Catfoii]";
		mes "Louis, meow!";
		mes "Don't do it!";
		next;
		mes "[Martha]";
		mes "Let's just surrender,";
		mes "Louis. It looks like";
		mes "the Z Gang is finished.";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "Anyway, the Rogue Guild";
		mes "is on its way to come get";
		mes "you guys. You won't be able";
		mes "to escape this time.";
		next;
		mes "[Louis]";
		mes "Awwww, man!";
		mes "Fine, take the stupid";
		mes "ol' book. I couldn't";
		mes "use it that good anyway.";
		next;
		mes "[Louis]";
		mes "Catfoii, would you";
		mes "lead this adventurer guy";
		mes "to the exit? I can't bear";
		mes "to look at the face that's";
		mes "caused my tragic downfall...";
		next;
		mes "[Catfoii]";
		mes "Fine, meow.";
		mes "I understand.";
		mes "But how did we lose?!";
		close2;
		getitem 7724,1; //Forbidden_Secret_Art
		changequest 3133,3134;
		set zdan_edq,18;
		mapwarp "z_agit","moc_fild17",209,235;
		set $@monster_zgang,0;
		set $@door2,0;
		hideonnpc "Louis";
		hideonnpc "Martha";
		hideonnpc "Catfoii";
		disablenpc "#zdan_broad";
		stopnpctimer;
		close;
	}
	else {
		mes "[" + strcharinfo(0) + "]";
		mes "Where am I...?";
		mes "Something has gone";
		mes "terribly wrong, hasn't";
		mes "it? Let me go baaaack~! ";
		set zdan_edq,15;
		set $@monster_zgang,0;
		set $@door2,0;
		close2;
		warp "moc_fild17",209,235;
		end;
	}

OnInit:
	hideonnpc "Martha";
	end;
}

z_agit,95,101,3	script	Catfoii	876,{
	if (checkweight(907,200) == 0) {
		mes "[Catfoii]";
		mes "You're carrying too";
		mes "many items: get rid of";
		mes "your extra stuff, put it in";
		mes "Kafra Storage, sell it, or";
		mes "whatever, before coming back.";
		close;
	}
	if (zdan_edq == 17) {
		mes "[Louis]";
		mes "What happened?";
		mes "The big guns were";
		mes "supposed to come out!";
		mes "Why aren't we invisible";
		mes "anymore? What happened?";
		next;
		mes "[Catfoii]";
		mes "You pushed the button";
		mes "to turn off the invisibility,";
		mes "meow! Did you really have";
		mes "to ask?! It's your fault!";
		next;
		mes "[Martha]";
		mes "Argh! I should never";
		mes "have trusted that fool!";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "So... Z Gang...";
		mes "And cat. It's time";
		mes "to turn you in.";
		next;
		mes "[Louis]";
		mes "I... I can't accept";
		mes "our defeat! The Z Gang";
		mes "won't go down so easily!";
		mes "Come forth, my demon";
		mes "spawn minions of doom!";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "I don't think";
		mes "they're coming.";
		next;
		mes "[Louis]";
		mes "...............................";
		mes "We surrender. Argh, but";
		mes "we were so close to";
		mes "conquering the world!";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "Now if you'll just";
		mes "hand over the Book";
		mes "of Forbidden Mystery...";
		next;
		mes "[Catfoii]";
		mes "Louis, meow!";
		mes "Don't do it!";
		next;
		mes "[Martha]";
		mes "Let's just surrender,";
		mes "Louis. It looks like";
		mes "the Z Gang is finished.";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "Anyway, the Rogue Guild";
		mes "is on its way to come get";
		mes "you guys. You won't be able";
		mes "to escape this time.";
		next;
		mes "[Louis]";
		mes "Awwww, man!";
		mes "Fine, take the stupid";
		mes "ol' book. I couldn't";
		mes "use it that good anyway.";
		next;
		mes "[Louis]";
		mes "Catfoii, would you";
		mes "lead this adventurer guy";
		mes "to the exit? I can't bear";
		mes "to look at the face that's";
		mes "caused my tragic downfall...";
		next;
		mes "[Catfoii]";
		mes "Fine, meow.";
		mes "I understand.";
		mes "But how did we lose?!";
		close2;
		getitem 7724,1; //Forbidden_Secret_Art
		changequest 3133,3134;
		set zdan_edq,18;
		mapwarp "z_agit","moc_fild17",209,235;
		set $@monster_zgang,0;
		set $@door2,0;
		hideonnpc "Louis";
		hideonnpc "Martha";
		hideonnpc "Catfoii";
		disablenpc "#zdan_broad";
		stopnpctimer;
		close;
	}
	else {
		mes "[" + strcharinfo(0) + "]";
		mes "Where am I...?";
		mes "Something has gone";
		mes "terribly wrong, hasn't";
		mes "it? Let me go baaaack~! ";
		set zdan_edq,15;
		set $@monster_zgang,0;
		set $@door2,0;
		close2;
		warp "moc_fild17",209,235;
		end;
	}

OnInit:
	hideonnpc "Catfoii";
	end;
}

// Unlucky Emerald
//============================================================
morocc,143,63,3	script	Ragged Man#nd	929,{
	if (treasure_nd == 0) {
		mes "[Ragged Man]";
		mes "H-hello? Would you help me?";
		mes "You'd be saving this poor man's";
		mes "life if you would just do me";
		mes "this favor. Please...";
		next;
		switch(select("How may I help you?:Ignore Him")) {
		case 1:
			mes "[" + strcharinfo(0) + "]";
			mes "Okay, what can I do to help?";
			next;
			mes "[Ragged Man]";
			mes "Oh, thank you so much!";
			mes "It feels so good to talk to";
			mes "someone after being ignored";
			mes "all this time! I might appear to";
			mes "be a beggar, but I'm actually a";
			mes "treasure hunter from Prontera.";
			next;
			switch(select("Umm... So?")) {
			case 1:
				mes "[Ragged Man]";
				mes "I was so sure I could find";
				mes "the treasure here in Morocc";
				mes "when I first started... I did";
				mes "all my research and prep work.";
				mes "But after hiring teams of other";
				mes "hunters and researchers...";
				next;
				mes "[Ragged Man]";
				mes "I've spent my entire fortune,";
				mes "and I'm still not any closer";
				mes "to finding that treasure.";
				mes "I... I don't even have enough";
				mes "money to go back home!";
				emotion ET_CRY;
				next;
				mes "[Ragged Man]";
				mes "Won't you spare some";
				mes "money, so that I can finally";
				mes "go back home to Prontera?";
				next;
				switch(select("Why don't you just walk?:I can give you some money.")) {
				case 1:
					mes "[Ragged Man]";
					mes "What? You don't think that";
					mes "if I could walk to Prontera,";
					mes "I'd have done it already?!";
					mes "Why would I even bother to";
					mes "beg for money? Fine, I don't";
					mes "need your brand of sympathy.";
					next;
					mes "[Ragged Man]";
					mes "You know, I had a hot tip";
					mes "that I was going to let you";
					mes "in on, but if you don't want";
					mes "to share, then neither do I!";
					set treasure_nd,1;
					close;
				case 2:
					mes "[Ragged Man]";
					mes "Oh, thank you!";
					mes "Anything you can spare,";
					mes "I'll gladly appreciate!";
					mes "God bless you, bless you!";
					next;
					if (Zeny < 1200) {
						mes "[Ragged Man]";
						mes "Umm... Oh, dear...";
						mes "You're just as bad off";
						mes "as I am. I appreciate it,";
						mes "but I can't take your money.";
						next;
						mes "[Ragged Man]";
						mes "It's a shame, really.";
						mes "If you had given me 1,200";
						mes "zeny, I would have tipped";
						mes "you off to something big...";
						set treasure_nd,1;
						close;
					}
					else {
						set Zeny, Zeny-1200;
						mes "[Ragged Man]";
						mes "Let's see... I just need";
						mes "1,200 zeny. That's enough";
						mes "for me to go back home.";
						mes "Now, I can't let you just";
						mes "give me money and let you";
						mes "leave empty handed, can I?";
						next;
						mes "[Ragged Man]";
						mes "Remember that hidden";
						mes "treasure I was looking for?";
						mes "There's a rumor that it's";
						mes "location has finally been";
						mes "confirmed. Why don't you";
						mes "try finding it for yourself?";
						next;
						mes "[Ragged Man]";
						mes "I'd rather that it end up in";
						mes "the hands of someone like";
						mes "you than a rival treasure";
						mes "hunter. This hunter that";
						mes "stays north in town apparently confirmed the treasure's location.";
						next;
						mes "[Ragged Man]";
						mes "Now, I hear that this guy";
						mes "hasn't returned home yet,";
						mes "and he still hasn't found";
						mes "the treasure. He's probably";
						mes "run into a pretty bad snag.";
						next;
						mes "[Ragged Man]";
						mes "I doubt that he'll let you";
						mes "know the treasure's location";
						mes "easily, but who knows? Maybe";
						mes "he might slip, and accidentally";
						mes "give you some kind of clue.";
						set treasure_nd,2;
						close;
					}
				}
			}
		case 2:
			mes "[Ragged Man]";
			mes "Please! Please, wait!";
			mes "You're an adventurer, aren't";
			mes "you? I have some valuable";
			mes "information to share if you'd";
			mes "just spare me some... Hey!";
			mes "Come back! No... I just...";
			close;
		}
	}
	else if (treasure_nd == 1) {
		mes "[Ragged Man]";
		mes "Oh, I remember you.";
		mes "You must really want that";
		mes "treasure now, do you? Just";
		mes "give me 1,200 zeny for me";
		mes "to go back home, and I'll";
		mes "tell you everything I know.";
		next;
		switch(select("Give Money:Don't Give Money")) {
		case 1:
			mes "[Ragged Man]";
			mes "Ah, good choice!";
			mes "And thank you so much.";
			mes "It'll be great to finally be";
			mes "back home in Prontera.";
			next;
			if (Zeny < 1200) {
				mes "[Ragged Man]";
				mes "What th--?! This isn't";
				mes "enough money for me to go";
				mes "back home! Well, just come";
				mes "back later once you scrounge";
				mes "up the funds. I'll be waiting.";
				close;			}
			else {
				set Zeny, Zeny-1200;
				mes "[Ragged Man]";
				mes "Remember that hidden";
				mes "treasure I was looking for?";
				mes "There's a rumor that it's";
				mes "location has finally been";
				mes "confirmed. Why don't you";
				mes "try finding it for yourself?";
				next;
				mes "[Ragged Man]";
				mes "I'd rather that it end up in";
				mes "he hands of someone like";
				mes "you than a rival treasure";
				mes "hunter. This hunter that";
				mes "stays north in town apparently confirmed the treasure's location.";
				next;
				mes "[Ragged Man]";
				mes "Now, I hear that this guy";
				mes "hasn't returned home yet,";
				mes "and he still hasn't found";
				mes "the treasure. He's probably";
				mes "run into a pretty big snag.";
				next;
				mes "[Ragged Man]";
				mes "I doubt that he'll let you";
				mes "know the treasure's location";
				mes "easily, but who knows? Maybe";
				mes "he might slip, and accidentally";
				mes "give you some kind of clue.";
				set treasure_nd,2;
				close;
			}
		case 2:
			mes "[Ragged Man]";
			mes "Fine, have it your way.";
			mes "I admit that I'm a little";
			mes "disappointed in you. Aren't";
			mes "you adventurers supposed";
			mes "to be heroes to the people?";
			close;
		}
	}
	else if (treasure_nd == 2) {
		mes "[Ragged Man]";
		mes "Oh, you're back?";
		mes "Ah, you must be having";
		mes "trouble finding that guy";
		mes "I was talking about. He's";
		mes "just in the northern part of this town. It can't be that hard.";
		close;
	}
	else if (treasure_nd > 10) {
		mes "[Ragged Man]";
		mes "You found the treasure?!";
		mes "It... It really exists...";
		mes "I'm sorry, it's just been";
		mes "my dream to... I feel like";
		mes "crying... Oh, God... I don't";
		mes "know what this feeling is...";
		next;
		mes "[Ragged Man]";
		mes "I mean... I feel so";
		mes "damned happy, but...";
		mes "I spent my entire fortune...";
		mes "Ruined my whole life just";
		mes "to see that treasure...";
		close;
	}
	else {
		mes "[Ragged Man]";
		mes "So how's the treasure";
		mes "hunt coming along? I hope";
		mes "you have better luck than";
		mes "I did. Still, I've got a pretty good feeling that you'll find it.";
		close;
	}
}

morocc,102,298,3	script	Man#zgang	98,{
	if (treasure_nd < 2) {
		mes "[Man]";
		mes "What do you want?";
		mes "Just leave me alone.";
		close;
	}
	else if (treasure_nd == 2) {
		mes "[Man]";
		mes "What do you want?";
		mes "Just leave me alone.";
		next;
		switch(select("Tell me about the treasure.")) {
		case 1:
			emotion ET_SURPRISE;
			mes "[Man]";
			mes "What? So you're the one";
			mes "I was warned about! I've";
			mes "been wondering when you'd";
			mes "show up. Prepare to die!";
			next;
			mes "[" + strcharinfo(0) + "]";
			mes "Umm... Excuse me?";
			mes "I've just heard that you";
			mes "confirmed the location of";
			mes "some amazing treasure,";
			mes "and I just wanted to ask";
			mes "you more about it. That's all!";
			next;
			mes "[Man]";
			mes "What? If you're just here";
			mes "for that, then how'd you";
			mes "find out about me and";
			mes "the treasure? Not just";
			mes "everyone knows about it.";
			next;
			mes "[" + strcharinfo(0) + "]";
			mes "Well, I was talking to";
			mes "this other treasure hunter";
			mes "who's... Well, he's kind";
			mes "of a beggar now...";
			next;
			mes "[Man]";
			mes "Oh. That old man?";
			mes "Well, I don't know if the";
			mes "treasure he was looking";
			mes "for and the one I've found";
			mes "are the same one. But";
			mes "yeah, I know that guy.";
			next;
			mes "[Man]";
			mes "Alright. So I found this";
			mes "treasure, but I have no way";
			mes "of digging it up. Maybe we";
			mes "can help each other. I need";
			mes "money, and you want to know";
			mes "more, right? Let's cut a deal.";
			next;
			switch(select("No, thanks.:What'd you have in mind?")) {
			case 1:
				mes "[Man]";
				mes "No? Well...";
				mes "There's no reason for";
				mes "you to trust me now,";
				mes "I suppose. But what can";
				mes "you gain by walking away?";
				set treasure_nd,3;
				close;
			case 2:
				mes "[Man]";
				mes "Let me lay it out for you.";
				mes "I've been hired to find this";
				mes "treasure by some people:";
				mes "they said I can take whatever";
				mes "I find, but they specifically";
				mes "want one item for themselves.";
				next;
				mes "[Man]";
				mes "They pay me on delivery";
				mes "so I haven't seen a dime yet.";
				mes "Now, my agreement with them";
				mes "is conditional. I can't dig";
				mes "up the treasure so they'll";
				mes "understand if I say I failed.";
				next;
				mes "[Man]";
				mes "But if you dig it up, you can";
				mes "keep everything. Sound good?";
				mes "All I want is some stuff from";
				mes "you to make all my efforts up";
				mes "till now worth my while.";
				next;
				mes "[Man]";
				mes "In other words,";
				mes "if you bring me,";
				mes "^FF000020 Mementos^000000,";
				mes "^FF00002 Pearls^000000, and";
				mes "^FF00002 Zargons^000000, we can talk";
				mes "business further. Got it?";
				set treasure_nd,4;
				next;
				mes "[Man]";
				mes "Hey, I might be losing out";
				mes "by giving up my share of that";
				mes "treasure, but hey, a bird in";
				mes "the hand's always worth more.";
				close;
			}
		}
	}
	else if (treasure_nd == 3) {
		mes "[Man]";
		mes "You again? Lemme guess...";
		mes "You're reconsidering that";
		mes "deal I had in mind, aren't you?";
		next;
		switch(select("No, not really.:I guess so.")) {
		case 1:
			mes "[Man]";
			mes "You sure? No one would";
			mes "be losing in this deal, you";
			mes "know. Well, except the guys";
			mes "that hired me to find that";
			mes "treasure. It's pretty much";
			mes "a win-win situation.";
			next;
			mes "[Man]";
			mes "Way I figure it,";
			mes "someone ought to get";
			mes "that treasure. I can't";
			mes "dig it up, but maybe";
			mes "someone like you can...";
			close;
		case 2:
			mes "[Man]";
			mes "Let me lay it out for you.";
			mes "I've been hired to find this";
			mes "treasure by some people:";
			mes "they said I can take whatever";
			mes "I find, but they specifically";
			mes "want one item for themselves.";
			next;
			mes "[Man]";
			mes "They pay me on delivery";
			mes "so I haven't seen a dime yet.";
			mes "Now, my agreement with them";
			mes "is conditional. I can't dig";
			mes "up the treasure so they'll";
			mes "understand if I say I failed.";
			next;
			mes "[Man]";
			mes "But if you dig it up, you can";
			mes "keep everything. Sound good?";
			mes "All I want is some stuff from";
			mes "you to make all my efforts up";
			mes "till now worth my while.";
			next;
			mes "[Man]";
			mes "In other words,";
			mes "if you bring me,";
			mes "^FF000020 Mementos^000000,";
			mes "^FF00002 Pearls^000000, and";
			mes "^FF00002 Zargons^000000, we can talk";
			mes "business further. Got it?";
			set treasure_nd,4;
			next;
			mes "[Man]";
			mes "Hey, I might be losing out";
			mes "by giving up my share of that";
			mes "treasure, but hey, a bird in";
			mes "the hand's always worth more.";
			close;
		}
	}
	else if (treasure_nd == 4) {
		mes "[Man]";
		mes "Back again, huh?";
		mes "So did you bring";
		mes "the stuff I asked?";
		next;
		if ((countitem(722) > 1) && (countitem(912) > 1) && (countitem(934) > 19)) {
			mes "[Man]";
			mes "Nice. You did your part.";
			mes "And now it's time that";
			mes "I fulfilled my part of";
			mes "this bargain. Listen up.";
			delitem 722,2; //Scarlet_Jewel
			delitem 912,2; //Zargon
			delitem 934,20; //Mementos
			set treasure_nd,5;
			next;
			mes "[Man]";
			mes "The treasure is buried";
			mes "south of Morocc. Use the";
			mes "south gate to leave town,";
			mes "and then head to the next";
			mes "field to the south. From";
			mes "there, you're on your own.";
			next;
			mes "[Man]";
			mes "I don't know exactly";
			mes "where the treasure is";
			mes "buried, but you should";
			mes "be able to find it within";
			mes "that general area. Good luck.";
			close;
		}
		else {
			mes "[Man]";
			mes "Huh, you must have";
			mes "forgotten what I wanted.";
			mes "I'm not really asking you";
			mes "for extremely valuable stuff";
			mes "here. Anyway, listen up.";
			next;
			mes "[Man]";
			mes "Just bring me,";
			mes "^FF000020 Mementos^000000,";
			mes "^FF00002 Pearls^000000, and";
			mes "^FF00002 Zargons^000000 so we can talk";
			mes "business further. Got it?";
			close;
		}
	}
	else if (treasure_nd == 5) {
		mes "[Man]";
		mes "What are you still doing";
		mes "here? Shouldn't you be";
		mes "looking for the treasure?";
		close;
	}
	else if (treasure_nd == 6) {
		mes "[Man]";
		mes "What are you still doing";
		mes "here? Shouldn't you be";
		mes "looking for the treasure?";
		next;
		select("I already found it, but...");
		mes "[" + strcharinfo(0) + "]";
		mes "I already found it, but...";
		mes "It won't budge at all!";
		mes "I think it's protected by";
		mes "magic. Do you know";
		mes "anything about that?";
		next;
		mes "[Man]";
		mes "Wow, you actually found";
		mes "it? Heh, you're better than";
		mes "I thought. Sorry about that.";
		mes "I didn't explain the magic";
		mes "part since... Well, I didn't";
		mes "think you'd get that far.";
		next;
		mes "[Man]";
		mes "Anyway, I had problems";
		mes "with that magic too. Some";
		mes "guy was supposed to help me";
		mes "with that, but he didn't come.";
		mes "I've been so frustrated!";
		next;
		mes "[Man]";
		mes "I mean, I have no clue";
		mes "when it comes to magic, and";
		mes "I don't understand how I'm";
		mes "supposed to use this thing";
		mes "my clients gave to me.";
		next;
		while (1) {
			switch(select("Who was going to help you?:Your clients gave you something?")) {
			case 1:
				mes "[Man]";
				mes "Oh, yeah. The guys that";
				mes "hired me said that if I waited";
				mes "at the treasure site, someone";
				mes "would eventually come to help";
				mes "me out on the magic end.";
				mes "I don't know who he is, though.";
				next;
				mes "[Man]";
				mes "Oh, yeah. They mentioned";
				mes "something about him coming";
				mes "a bit from the west. Maybe";
				mes "he's over in Comodo?";
				next;
				set .@joho1,1;
				break;
			case 2:
				if (treasure_nd == 7) {
					mes "[Man]";
					mes "Oh, yeah. They gave me";
					mes "this weird document labeled";
					mes "''^FF0000[Open^000000.'' I don't know how";
					mes "to use it at all, though.";
					next;
				}
				else if (treasure_nd == 8) {
					mes "[Man]";
					mes "Oh, yeah. They gave me";
					mes "this weird document labeled";
					mes "''^FF0000[Unlock^000000.'' I don't know how";
					mes "to use it at all, though.";
					next;
				}
				else {
					mes "[Man]";
					mes "Oh, yeah. Let's see here.";
					mes "Ah, here's the document";
					mes "that they gave me. It's kind";
					mes "of a bit torn, though.";
					next;
					if (rand(1,2) == 1) {
						mes "^3355FFThe torn document";
						mes "is labeled ''^FF0000[Open^3355FF.''^000000";
						next;
						set treasure_nd,7;
						set .@joho2,1;
					}
					else {
						mes "^3355FFThe torn document";
						mes "is labeled ''^FF0000[Unlock^3355FF.''^000000";
						next;
						set treasure_nd,8;
						set .@joho2,1;
					}
				}
			}
			if ((.@joho1 == 1) && (.@joho2 == 1)) {
				break;
			}

		}
		mes "[" + strcharinfo(0) + "]";
		mes "Wait, how could you not";
		mes "know how to use this?";
		mes "Didn't you get a chance";
		mes "to talk to your clients?";
		next;
		mes "[Man]";
		mes "I haven't even met them.";
		mes "I just get their instructions";
		mes "through the mail. Everything";
		mes "is through a medium with these";
		mes "guys. I guess they've got their";
		mes "eyes on me too, but whatever.";
		next;
		mes "[Man]";
		mes "I guess they hired me because";
		mes "they really want the treasure,";
		mes "but don't want to tip anyone";
		mes "off that they're looking for ";
		mes "it, or that they have it.";
		next;
		mes "[Man]";
		mes "Anyway, all I know is that";
		mes "this piece of paper is supposed";
		mes "to do something. But I guess";
		mes "I'm not smart enough to figure";
		mes "it out. Maybe you'll have";
		mes "better luck with them.";
		next;
		mes "[Man]";
		mes "You know, if you can actually";
		mes "find that guy who was supposed";
		mes "to help me but never showed up,";
		mes "maybe he'll understand how to";
		mes "use that document. Go ahead";
		mes "and take 'em. Good luck, now.";
		close;
	}
	else if ((treasure_nd == 7) || (treasure_nd == 8)) {
		mes "[Man]";
		mes "I already told you everything";
		mes "I know. You'd be better off";
		mes "finding that guy over in";
		mes "Comodo, or the rest of";
		mes "that torn document.";
		close;
	}
	else if ((treasure_nd == 9) || (treasure_nd == 10)) {
		mes "[Man]";
		mes "Now that I think about it,";
		mes "maybe the treasure can be";
		mes "obtained by two people?";
		mes "I mean, they planned for";
		mes "me to go with that guy. Eh,";
		mes "best not to think about it.";
		close;
	}
	else {
		mes "[Man]";
		mes "Heh! Looks like I'll be";
		mes "headed home soon~";
		close;
	}
}

comodo,339,224,3	script	Man in Hiding#nd	881,{
	if (treasure_nd < 7) {
		mes "[Man in Hiding]";
		mes "Whoa, don't get so close!";
		close;
	}
	else if ((treasure_nd == 7) || (treasure_nd == 8)) {
		if (zdan_edq > 12) {
			mes "[Man in Hiding]";
			mes "Whoa, don't get so close!";
			next;
			select("Why are you hiding here?");
			mes "[" + strcharinfo(0) + "]";
			mes "Why are you hiding here?";
			mes "Didn't the Z Gang hire you";
			mes "to dig up some treasure?";
			next;
			mes "[Man in Hiding]";
			mes "Huh? Z Gang? Treasure?";
			next;
			mes "[Man in Hiding]";
			mes "Huh? Wait, tell me the";
			mes "truth. Did you actually";
			mes "meet the Z Gang?";
			next;
			mes "[" + strcharinfo(0) + "]";
			mes "I not only met the Z Gang,";
			mes "I also captured them. I've";
			mes "come looking for you to";
			mes "find out more about this";
			mes "buried treasure.";
			next;
			mes "[Man in Hiding]";
			mes "I get it now. You must";
			mes "have met that other guy";
			mes "they hired to get the";
			mes "treasure. Huh. He's got one";
			mes "half of the spell to open it,";
			mes "and I've got the other.";
			next;
			mes "[Man in Hiding]";
			mes "Well, you can have this,";
			mes "the rest of the magic spell.";
			mes "Let me warn you, though,";
			mes "there's something weird";
			mes "about the spell, and the";
			mes "treasure itself.";
			next;
			if (treasure_nd == 7) {
				mes "^3355FFYou received a torn";
				mes "document that reads";
				mes "''^FF0000Seseame]^000000.''";
				set treasure_nd,10;
				next;
			}
			else {
				mes "^3355FFYou received a torn";
				mes "document that reads";
				mes "''^FF0000Treasure]^000000.''";
				set treasure_nd,9;
				next;
			}
			mes "[Man in Hiding]";
			mes "I don't understand why they";
			mes "went through the hassle of";
			mes "splitting this spell in two";
			mes "parts. There must be some";
			mes "reason. Whatever it is, it";
			mes "must be pretty important.";
			next;
			mes "[Man in Hiding]";
			mes "I guess maybe you need";
			mes "two people to move the";
			mes "treasure? Well, whatever.";
			mes "Good luck finding that";
			mes "treasure. Farewell now!";
			close;
		}
		else {
			mes "[Man in Hiding]";
			mes "Whoa, don't get so close!";
			next;
			select("Ask About Treasure");
			mes "[" + strcharinfo(0) + "]";
			mes "I've come looking";
			mes "for information";
			mes "about the treasure.";
			next;
			emotion ET_HUK;
			mes "[Man in Hiding]";
			mes "You... You've come";
			mes "looking for me?!";
			mes "Ugh, I'm cornered!";
			mes "I'm not going down";
			mes "without a fight!";
			next;
			mes "[" + strcharinfo(0) + "]";
			mes "What? No, it's not like";
			mes "that. I met this treasure";
			mes "hunter in Morocc that said";
			mes "I could find you around here.";
			next;
			mes "[Man in Hiding]";
			mes "Treasure hunter...?";
			mes "Oh, then you already";
			mes "know about the treasure...";
			next;
			mes "[Man in Hiding]";
			mes "If I'm right, then you";
			mes "need the other half of";
			mes "the spell to open the";
			mes "treasure. Here, you can";
			mes "take it. I have no use";
			mes "for it, anyway.";
			next;
			if (treasure_nd == 7) {
				mes "^3355FFYou received a torn";
				mes "document that reads";
				mes "''^FF0000Sesame]^000000.''";
				set treasure_nd,10;
				next;
			}
			else {
				mes "^3355FFYou received a torn";
				mes "document that reads";
				mes "''^FF0000Treasure]^000000.''";
				set treasure_nd,9;
				next;
			}
			mes "[Man in Hiding]";
			mes "Now would you just get";
			mes "out of here? If you couldn't";
			mes "already tell, I'm hiding from";
			mes "some people. I told you all";
			mes "I know, so do me a favor,";
			mes "and don't tell anyone I'm here!";
			close;
		}
	}
	else if (treasure_nd == 9) {
		mes "[Man in Hiding]";
		mes "Aren't you going to";
		mes "dig up that treasure?";
		mes "You have both parts of";
		mes "the spell that you need:";
		mes "I already gave you the";
		mes "''^FF0000Treasure]^000000'' part, right?";
		next;
		if (zdan_edq > 12) {
			mes "[Man in Hiding]";
			mes "Anyway, now that the";
			mes "Z Gang is captured,";
			mes "it might be safe enough";
			mes "for me to go back home.";
			close;
		}
		else {
			mes "[Man in Hiding]";
			mes "Would you do me a favor,";
			mes "and just get out of here";
			mes "before someone finds";
			mes "my hiding place?";
			close;
		}
	}
	else if (treasure_nd == 10) {
		mes "[Man in Hiding]";
		mes "Aren't you going to";
		mes "dig up that treasure?";
		mes "You have both parts of";
		mes "the spell that you need:";
		mes "I already gave you the";
		mes "''^FF0000Sesame]^000000'' part, right?";
		next;
		if (zdan_edq > 12) {
			mes "[Man in Hiding]";
			mes "Anyway, now that the";
			mes "Z Gang is captured,";
			mes "it might be safe enough";
			mes "for me to go back home.";
			close;
		}
		else {
			mes "[Man in Hiding]";
			mes "Would you do me a favor,";
			mes "and just get out of here";
			mes "before someone finds";
			mes "my hiding place?";
			close;
		}
	}
	else {
		mes "[Man in Hiding]";
		mes "Heh! Looks like it's";
		mes "safe for me to come out";
		mes "of hiding, and just go home!";
		mes "But maybe I should stick";
		mes "around here a little longer,";
		mes "just to be on the safe side.";
		close;
	}
}

moc_fild18,108,116,0	script	#treasure	844,{
	if (checkweight(907,300) == 0) {
		mes "^3355FFYou're carrying too many";
		mes "items: there's no way you";
		mes "can carry the Treasure";
		mes "Chest with you for now.^000000";
		close;
	}
	if (treasure_nd == 5) {
		mes "^3355FFThere's something";
		mes "here, buried just";
		mes "beneath the ground.^000000";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "Unngh! I-it won't...!";
		next;
		mes "^3355FFNo matter how hard";
		mes "you try, you can't dig";
		mes "out the chest or open it.";
		mes "There's some magical";
		mes "aura surrounding the chest";
		mes "that might be stopping you.^000000";
		set treasure_nd,6;
		close;
	}
	else if (treasure_nd == 6) {
		mes "^3355FFThat Treasure Hunter in";
		mes "Morocc must know something";
		mes "about the chest's magical";
		mes "protection. It might be";
		mes "a good idea to ask him.^000000";
		close;
	}
	else if ((treasure_nd == 7) || (treasure_nd == 8)) {
		mes "^3355FFYou tried casting the";
		mes "spell on the document";
		mes "you received, but nothing";
		mes "happened. You should probably";
		mes "find the other man over in";
		mes "Comodo to see what he knows.^000000";
		close;
	}
	else if (treasure_nd == 9 || treasure_nd == 10) {
		mes "^3355FFYou'll need to combine";
		mes "the two halves of the spell";
		mes "on the document you received";
		mes "to remove the chest's magical";
		mes "protection to get the treasure.^000000";
		next;
		input .@input$;
		getpartymember(getcharid(1));
		set .@partymembercount,$@partymembercount;
		if (.@partymembercount > 1) {
			if (((.@input$ == "OpenSesame") && (treasure_nd == 9)) || ((.@input$ == "UnlockTreasure") && (treasure_nd == 10))) {
				mes "^3355FFThe Z Gang must have split";
				mes "the spell document in two";
				mes "parts because one person";
				mes "isn't enough to open this";
				mes "chest. It's a good thing";
				mes "you brought a friend.^000000";
				next;
				mes "^3355FFYou cast the spell,";
				mes "the chest opens, and";
				mes "you grab all the loot inside.^000000";
				if (zdan_edq == 0) {
					set treasure_nd,11;
				}
				else {
					set treasure_nd,12;
				}
				getitem 7725,1; //Unlucky_Emerald
				getitem 604,1; //Branch_Of_Dead_Tree
				getitem 1157,1; //Two_Hand_Sword
				sc_start SC_Curse,10000,0,10000;
				next;
				mes "[" + strcharinfo(0) + "]";
				mes "N-no! What's going on?";
				mes "This Emerald... It must";
				mes "be cursed by evil magic!";
				close;
			}
			else {
				mes "^3355FFNothing happened.";
				mes "You might not be";
				mes "combining the spell";
				mes "correctly. Perhaps if";
				mes "you made it all one word...^000000";
				close;
			}
		}
		else {
			mes "^3355FFThe treasure chest still";
			mes "won't open. Then again,";
			mes "this doesn't seem to be";
			mes "a one man job...^000000";
			close;
		}
	}
	else if (treasure_nd == 11) {
		if (zdan_edq > 0) {
			mes "^3355FFSomone must have";
			mes "checked the treasure";
			mes "site after you left:";
			mes "whoever it was left";
			mes "telltale signs of";
			mes "his presence.^000000";
			set treasure_nd,12;
			close;
		}
	}
}

comodo,139,184,3	script	Scholar#zgang	742,{
	if (jewel_nd < 9) {
		mes "[Scholar]";
		mes "I'm an antique appraiser.";
		mes "You may think of me as similar";
		mes "to jewel appraisers, but I can";
		mes "appraise the historical value";
		mes "of antiques in addition to their quality and monetary value.";
		close;
	}
	else if (jewel_nd == 9) {
		mes "[Scholar]";
		mes "Oooh. You must be the";
		mes "one sent by Ibrahim, right?";
		mes "You're the one that found";
		mes "that rare emerald, one of";
		mes "the accursed jewels?";
		next;
		mes "[Scholar]";
		mes "I'm very interested in the";
		mes "Cursed Jewels, and have been";
		mes "researching them. You came";
		mes "to just the right person if you";
		mes "wanted to learn more about";
		mes "the emerald that you found.";
		set jewel_nd,11;
		close;
	}
	else if (jewel_nd == 10) {
		mes "[Scholar]";
		mes "Hello, how may";
		mes "I help you today?";
		next;
		select("Ask About Cursed Jewel");
		mes "[" + strcharinfo(0) + "]";
		mes "I came to ask you";
		mes "about a Cursed Jewel.";
		next;
		mes "[Scholar]";
		mes "Let's see... Ah, did";
		mes "you come to ask about";
		mes "the Diamond of Destruction?";
		next;
		select("No, it's the Unlucky Emerald.");
		mes "[Scholar]";
		mes "Ah, the Unlucky Emerald?";
		mes "Yes, that was the most recent";
		mes "Cursed Jewel to be discovered.";
		mes "Let's see, where did I put all";
		mes "of my notes about that?";
		next;
		mes "[Scholar]";
		mes "Would you mind coming";
		mes "back later? I'm not sure";
		mes "where I misplaced my notes...";
		set jewel_nd,11;
		close;
	}
	else if (jewel_nd == 11) {
		mes "[Scholar]";
		mes "Ah, let's see now.";
		mes "There's a record here";
		mes "entitled, ''The Story of the";
		mes "Cursed Jewel.'' According";
		mes "to this, no one knows where";
		mes "these jewels were first found.";
		next;
		mes "[Scholar]";
		mes "The first incident involving";
		mes "the jewel involved a Comodo";
		mes "resident that used the jewel";
		mes "to pay a gambling debt. He";
		mes "committed suicide after he";
		mes "paid his debt, which is a pity.";
		next;
		mes "[Scholar]";
		mes "He didn't tell anyone";
		mes "where he found the jewel";
		mes "before he died, so we have";
		mes "no way of determining the";
		mes "jewel's true origins.";
		next;
		mes "[Scholar]";
		mes "After it was sold, the";
		mes "jewel came into a rich man's";
		mes "possession. However, he lost";
		mes "his fortune and was completely";
		mes "ruined after obtaining it. Hence the moniker, ''Unlucky Emerald.''";
		next;
		mes "[Scholar]";
		mes "Then the emerald came into";
		mes "the hands of a noble lady";
		mes "who mysteriously died at";
		mes "a young age. The emerald's";
		mes "had thousands of owners";
		mes "who just... Died.";
		next;
		mes "[Scholar]";
		mes "What's disturbing is that";
		mes "the emerald's curse seems";
		mes "to grow stronger with each";
		mes "life it takes away. Um, and";
		mes "your jewel perfectly matches";
		mes "the descriptions, so, uh...";
		next;
		mes "[Scholar]";
		mes "You are the new owner";
		mes "of the Unlucky Emerald.";
		mes "No one will buy that from";
		mes "you while it's still cursed.";
		mes "For the sake of your life,";
		mes "you must break that curse.";
		next;
		mes "[Scholar]";
		mes "Now, holy spells won't work";
		mes "on this kind of curse. You'll";
		mes "need to talk to a shaman.";
		mes "I think he lives in... Alberta?";
		mes "He can summon the dead";
		mes "and act as a medium for you.";
		next;
		mes "[Scholar]";
		mes "The idea is for you to talk";
		mes "to the spirits killed by this";
		mes "emerald through that shaman.";
		mes "And I'm sure he'll be pretty";
		mes "excited to see that you have";
		mes "the Unlucky Emerald with you.";
		set jewel_nd,12;
		close;
	}
	else if (jewel_nd == 12) {
		mes "[Scholar]";
		mes "Please find that shaman";
		mes "in Alberta, and ask if he can";
		mes "help you break the curse";
		mes "on that Unlucky Emerald.";
		close;
	}
	else if (jewel_nd > 28) {
		mes "[Scholar]";
		mes "Wait, you're telling me";
		mes "that the emerald was cursed";
		mes "by the vindictive spirit of";
		mes "some warrior? Interesting...";
		mes "I should record this in my";
		mes "research notes. Thank you!";
		close;
	}
	else {
		mes "[Scholar]";
		mes "Some jewels are said to";
		mes "be possessed by ghosts";
		mes "or evil spirits that will bring";
		mes "misfortune to their owners,";
		mes "drive them to death or";
		mes "insanity, that sort of thing.";
		close;
	}
}

alberta_in,70,109,5	script	Shaman	788,{
	if (jewel_nd == 0) {
		mes "[Shaman]";
		mes "The spiritual realm is";
		mes "the source of my sorcery.";
		mes "Do not understimate the";
		mes "power of the spirits!";
		close;
	}
	else if ((jewel_nd > 0) && (jewel_nd < 12)) {
		mes "[Shaman]";
		mes "Oh? Heh heh!";
		mes "You're carrying";
		mes "something very";
		mes "valuable, aren't you?";
		close;
	}
	else if (jewel_nd == 12) {
		mes "[Shaman]";
		mes "You! Yes... I can sense";
		mes "them... The angry spirits of";
		mes "your ancestors! You haven't";
		mes "made any offerings to them,";
		mes "have you? You must appease";
		mes "them before they torment you!";
		next;
		mes "[Shaman]";
		mes "Quickly, now! It is";
		mes "imperative that you give me";
		mes "500,000 zeny immediately";
		mes "so that I can comfort their";
		mes "souls before they wreck";
		mes "havoc on your life!";
		next;
		select("Actually, I have a Cursed Jewel.");
		mes "[Shaman]";
		mes "Jewel...? Oh, a Cursed";
		mes "Jewel! Hahaha! So that's";
		mes "what that evil aura was!";
		mes "My mistake! Say, do you";
		mes "mind if I take a look at it?";
		next;
		if (countitem(7725) > 0) {
			emotion ET_THINK;
			mes "[Shaman]";
			mes "So this bauble's the";
			mes "Unlucky Emerald? Yes,";
			mes "it's definitely cursed.";
			mes "If you don't break the";
			mes "curse soon, it'll consume";
			mes "your soul. Not a good thing.";
			next;
			mes "[Shaman]";
			mes "I need to perform";
			mes "a ritual to break the";
			mes "curse. I'll charge you";
			mes "200,000 zeny for the service.";
			mes "It's expensive, but I'm saving";
			mes "your life and risking mine.";
			set jewel_nd,13;
			close;
		}
		else {
			mes "[Shaman]";
			mes "Hm? Did you forget to";
			mes "bring the jewel? Hurry,";
			mes "and bring it back to me.";
			mes "You need to break that";
			mes "curse before the jewel";
			mes "can devour your soul!";
			close;
		}
	}
	else if (jewel_nd == 13) {
		mes "[Shaman]";
		mes "Did you bring the money?";
		mes "We must appease the spirits";
		mes "possessing the soul, and to";
		mes "do that, I will need to perform";
		mes "a ritual that will be taxing";
		mes "on my body and spirit.";
		next;
		if (Zeny > 199999) {
			mes "[Shaman]";
			mes "Good, good. I see that";
			mes "you've brought the money.";
			mes "I can now begin the ritual.";
			mes "If all goes well, you'll be";
			mes "free of your curse, and I can";
			mes "buy some Prontera real estate.";
			set Zeny, Zeny-200000;
			set jewel_nd,14;
			next;
			mes "[Shaman]";
			mes "Please come back soon";
			mes "after I've completed the";
			mes "preparations for the ritual.";
			close;
		}
		else {
			mes "[Shaman]";
			mes "You... You don't have";
			mes "200,000 zeny? I'm sorry,";
			mes "but I cannot perform the";
			mes "ritual for free, even if";
			mes "your soul is at stake.";
			close;
		}
	}
	else if (jewel_nd == 14) {
		mes "[Shaman]";
		mes "Hmm... You know the";
		mes "money you gave me?";
		mes "Apparently, it wasn't";
		mes "enough. One of the spirits";
		mes "was about to enter me, but...";
		next;
		mes "[Shaman]";
		mes "It left in disgust after";
		mes "it learned that I was trying";
		mes "to break the curse for only";
		mes "200,000 zeny. I think...";
		mes "I think I need 100,000 zeny";
		mes "more for this to be effective.";
		set jewel_nd,15;
		close;
	}
	else if (jewel_nd == 15) {
		mes "[Shaman]";
		mes "Good, you've returned!";
		mes "Did you bring the 100,000";
		mes "zeny like I asked? It's a small";
		mes "price considering that I'm";
		mes "saving your very soul.";
		next;
		if (Zeny > 99999) {
			mes "[Shaman]";
			mes "Ah, perfect!";
			mes "Hopefully, the";
			mes "spirits will be more";
			mes "cooperative this time.";
			set Zeny, Zeny-100000;
			set jewel_nd,16;
			next;
			mes "[Shaman]";
			mes "Yes, the vindictive";
			mes "spirit in this jewel";
			mes "will be most impressed";
			mes "by your sincere efforts.";
			close;
		}
		else {
			mes "[Shaman]";
			mes "You didn't? Do you know";
			mes "what it's like to have";
			mes "your soul consumed? Let";
			mes "me tell you, it's a lot";
			mes "more painful than parting";
			mes "with a paltry 100,000 zeny!";
			close;
		}
	}
	else if (jewel_nd == 16) {
		mes "[Shaman]";
		mes "At long last, the";
		mes "spirits and I are one.";
		mes "The ritual may now begin.";
		mes "Ohhhhhhhhhhhmmmmm... ";
		next;
		mes "[Shaman]";
		mes "Hhhkkk... Arrrgh...!";
		emotion ET_HUK;
		next;
		mes "[" + strcharinfo(0) +"]";
		mes ".....?";
		next;
		mes "[Shaman]";
		mes "Th-this emerald!";
		mes "So many lives! Money";
		mes "alone won't break this";
		mes "curse! It's time to get";
		mes "serious and suppress";
		mes "this jewel's power!";
		next;
		mes "[Shaman]";
		mes "Remember the items";
		mes "I tell you to bring, and";
		mes "come back with them";
		mes "as quickly as you can!";
		mes "Both of our lives are in";
		mes "danger until you do!";
		next;
		mes "[Shaman]";
		mes "Bring me";
		mes "^FF00001 Holy Water^000000,";
		mes "^FF00001 Red Blood^000000,";
		mes "^FF00001 Witherless Rose^000000,";
		mes "^FF00001 Crystal Blue^000000, and";
		mes "^FF00001 Wind of Verdure^000000.";
		next;
		mes "[Shaman]";
		mes "Hurry! Although the spirits";
		mes "are eternal, they don't like";
		mes "to be kept waiting! Don't";
		mes "question this great irony,";
		mes "and get those items now!";
		set jewel_nd,17;
		close;
	}
	else if (jewel_nd == 17) {
		mes "[Shaman]";
		mes "You brought the items?!";
		mes "Quickly! Place them in";
		mes "my hands! We can't afford";
		mes "to waste any more time!";
		next;
		if (countitem(990) > 0) && (countitem(991) > 0) && (countitem(992) > 0) && (countitem(748) > 0) && (countitem(523) > 0) {
			mes "[Shaman]";
			mes "Thank goodness.";
			mes "Now... Let me begin...";
			mes "Uhhkk... Argh! Wait!";
			mes "I... I see someone...";
			mes "Covered in blood? Yes...";
			mes "He's staring at something.";
			delitem 990,1; //Boody_Red
			delitem 991,1; //Crystal_Blue
			delitem 992,1; //Wind_Of_Verdure
			delitem 748,1; //Witherless_Rose
			delitem 523,1; //Holy_Water
			next;
			mes "[Shaman]";
			mes "There's a sword behind it.";
			mes "He's... He's just been in";
			mes "a fight? I can smell...";
			mes "His bloodlust. I can't tell";
			mes "if he cursed the jewel of";
			mes "if he's another victim...";
			next;
			mes "[Shaman]";
			mes "Damn it! It's not clear";
			mes "to me! We need to figure";
			mes "out if what I'm seeing is";
			mes "the root of the curse...";
			mes "What can we do to--Right!";
			next;
			mes "[Shaman]";
			mes "I'm not sure how helpful";
			mes "he'll be, but there is an";
			mes "archaeologist in Juno that";
			mes "might know more about this";
			mes "specific jewel. Hopefully,";
			mes "he will have the answer.";
			set jewel_nd,18;
			next;
			mes "[Shaman]";
			mes "For now, the jewel's";
			mes "curse is beyond my power.";
			mes "However, if you can determine";
			mes "the source of the curse, then";
			mes "I will be able to help you.";
			close;		}
		else {
			mes "[Shaman]";
			mes "You... Forgot?!";
			mes "Your body and soul are in";
			mes "jeopardy and you forgot?!";
			next;
			mes "[Shaman]";
			mes "Bring me";
			mes "^FF00001 Holy Water^000000,";
			mes "^FF00001 Red Blood^000000,";
			mes "^FF00001 Witherless Rose^000000,";
			mes "^FF00001 Crystal Blue^000000, and";
			mes "^FF00001 Wind of Verdure^000000!";
			close;
		}
	}
	else if (jewel_nd == 18) {
		mes "[Shaman]";
		mes "That antique appraiser...";
		mes "He wouldn't know anything";
		mes "more about the jewel, but";
		mes "that archaeologist in Juno";
		mes "will hopefully have some";
		mes "new information we can use.";
		close;
	}
	else if (jewel_nd == 19) {
		mes "[Shaman]";
		mes "Ah, you're back.";
		mes "So did you learn";
		mes "anything new about";
		mes "the Unlucky Emerald?";
		next;
		select("The archaeologist said...");
		mes "["+ strcharinfo(0) +"]";
		mes "The archaeologist said...";
		mes "Well, he said that there";
		mes "is no curse on the jewel.";
		next;
		mes "[Shaman]";
		mes "What? How dare he refute";
		mes "what I just told you?!";
		mes "Is he calling me a liar?";
		mes "There was a vindictive";
		mes "warrior's soul inside";
		mes "that very emerald!";
		next;
		mes "[Shaman]";
		mes "Then again, there's no";
		mes "point fighting someone";
		mes "that doesn't believe in";
		mes "the spirit realm. However,";
		mes "I wasn't lying. My honor";
		mes "as a shaman is at stake!";
		set jewel_nd,20;
		next;
		mes "[Shaman]";
		mes "In fact, I'll prove it to you";
		mes "by reading the message";
		mes "inside this emerald. Bring";
		mes "me the jewel, and I'll summon";
		mes "the warrior spirit inside it.";
		mes "Just let me rest a bit first.";
		close;
	}
	else if (jewel_nd == 20) {
		if (countitem(7725) > 0) {
			mes "[Shaman]";
			mes "Alright. By my pride";
			mes "as a shaman, I'll provide";
			mes "this service to you free";
			mes "of charge! Can't have";
			mes "anyone thinking I'm";
			mes "some kind of quack...";
			next;
			mes "[Shaman]";
			mes "Hmmmm...";
			mes "Hmmmm...";
			mes "I... I see.";
			next;
			mes "[Shaman]";
			mes "Huh. Well, what do you";
			mes "know? There is no curse.";
			mes "However, it's not true that";
			mes "only Comodo's prodigal son";
			mes "was killed over this jewel.";
			mes "There are many more...";
			next;
			mes "[Shaman]";
			mes "Go yonder to the town";
			mes "of Geffen, head west, and";
			mes "seek the high ground that";
			mes "is over the bridge. You know...";
			mes "Leave Geffen through the west";
			mes "gate and find a high place.";
			next;
			mes "[Shaman]";
			mes "Trust me. You'll find";
			mes "something there that will";
			mes "react to the power of that";
			mes "emerald you possess.";
			next;
			mes "[Shaman]";
			mes "Hmpf! That arrogant";
			mes "archaeologist! He doesn't";
			mes "know what he's talking about!";
			set jewel_nd,21;
			close;
		}
		else {
			mes "[Shaman]";
			mes "You don't have the";
			mes "emerald with you?";
			mes "I hope you didn't";
			mes "lose it somewhere.";
			close;
		}
	}
	else if (jewel_nd == 21) {
		mes "[Shaman]";
		mes "Go yonder to the town";
		mes "of Geffen, head west, and";
		mes "seek the high ground that";
		mes "is over the bridge. You know...";
		mes "Leave Geffen through the west";
		mes "gate and find a high place.";
		next;
		mes "[Shaman]";
		mes "Trust me. You'll find";
		mes "something there that will";
		mes "react to the power of that";
		mes "emerald you possess.";
		mes "Don't forget to bring";
		mes "that emerald with you!";
		close;
	}
	else if ((jewel_nd == 22) || (jewel_nd == 23)) {
		mes "[Shaman]";
		mes "What did I tell you?";
		mes "That jewel brings very";
		mes "bad fortune. Though, I'm";
		mes "not sure why the warrior";
		mes "spirit inside wouldn't talk";
		mes "to me. What's with that?";
		close;
	}
	else {
		mes "[Shaman]";
		mes "It's been a long time";
		mes "since I've done these";
		mes "rituals. I feel thoroughly";
		mes "drained of my spiritual energy.";
		close;
	}
}

yuno_in04,38,48,3	script	Archeologist	55,{
	if (jewel_nd < 18) {
		mes "[Archeologist]";
		mes "There's so much to do!";
		mes "How am I going to handle";
		mes "all of this research? Don't";
		mes "panic, don't panic, just";
		mes "one thing at a time!";
		close;
	}
	else if (jewel_nd == 18) {
		mes "[Archeologist]";
		mes "Oh, I've heard about you";
		mes "from that antique appraiser";
		mes "in Comodo... You're the one";
		mes "that dig up the Unlucky";
		mes "Emerald, right? Would";
		mes "you mind if I take a look?";
		next;
		if (countitem(7725) > 0) {
			mes "[Archeologist]";
			mes "Ah, it's no wonder so";
			mes "many people want it.";
			mes "This emerald is so huge";
			mes "and... It's beautiful! So...";
			mes "I'm guessing you needed";
			mes "my help with something?";
			next;
			switch(select("Ask About the Jewel's Curse:Show Off Jewel")) {
			case 1:
				emotion ET_SMILE;
				mes "[Archeologist]";
				mes "The jewel's curse?";
				mes "Hah! You believe in that";
				mes "old superstition? Oh, so";
				mes "you wanted to break the";
				mes "jewel's curse? Mmpf. Hahah!";
				mes "You're not dead yet, right?";
				next;
				mes "[Archeologist]";
				mes "It's all a load of bunk!";
				mes "Only one man died while";
				mes "keeping this jewel in his";
				mes "possession, that gambler in";
				mes "Comodo that used it to pay";
				mes "off his gambling debts.";
				next;
				mes "[Archeologist]";
				mes "This jewel happened to be";
				mes "lost for a long time, and it";
				mes "just reappeared. That's all.";
				mes "Lots of myths and legends";
				mes "surround extremely valuable";
				mes "gems like this one.";
				next;
				mes "[Archeologist]";
				mes "Anyway, there's no curse.";
				mes "Believe what you want to,";
				mes "but that happens to be my";
				mes "professional opinion,";
				mes "based on my research.";
				set jewel_nd,19;
				close;
			case 2:
				mes "[Archeologist]";
				mes "Wow...";
				mes "That jewel sure";
				mes "is freakin' huge...";
				close;
			}
		}
		else {
			mes "[Archeologist]";
			mes "You lost the jewel?";
			mes "Well, maybe it's not";
			mes "all bad. History shows";
			mes "that many people were";
			mes "ruined by suddenly coming";
			mes "upon vast fortunes, you know?";
			close;
		}
	}
	else if (jewel_nd == 22) {
		mes "[Archeologist]";
		mes "The vindictive soul";
		mes "of a warrior is just";
		mes "drifting around in the";
		mes "world of the living?";
		mes "I don't think I want to";
		mes "believe what you're saying.";
		next;
		mes "[Archeologist]";
		mes "I personally feel that";
		mes "old shaman's a fraud, but...";
		mes "You should have the freedom";
		mes "to decide what you believe.";
		mes "But don't let it bother you.";
		close;
	}
	else if (jewel_nd == 23) {
		mes "[Archeologist]";
		mes "Are you talking about";
		mes "ghosts again? Fine. For";
		mes "the sake of the argument,";
		mes "let's say the jewel actually";
		mes "possesses the soul of";
		mes "some fallen warrior.";
		next;
		mes "[Archeologist]";
		mes "It's not unfathomable for";
		mes "people with huge debts to";
		mes "kill themselves, or for";
		mes "warriors to perish while";
		mes "fighting Cobolds. Still...";
		mes "I'll see what I can learn.";
		next;
		mes "[Archeologist]";
		mes "That reminds me...";
		mes "I've got some records about";
		mes "an adventurer group that died";
		mes "in the field west of Geffen";
		mes "that you were talking about.";
		mes "Let me see if I can find them.";
		set jewel_nd,24;
		close;
	}
	else if (jewel_nd == 24) {
		mes "[Archeologist]";
		mes "Ah, you're back! I found";
		mes "those records I told you";
		mes "about. It's a witness's";
		mes "account of what happened";
		mes "when those people died";
		mes "west of Geffen.";
		next;
		mes "[Archeologist]";
		mes "What's interesting is that";
		mes "the warrior you keep talking";
		mes "about wasn't killed by Cobolds";
		mes "at all, according to what this";
		mes "guy wrote. Let's see...";
		mes "Ah, it's over here.";
		next;
		mes "[Archeologist]";
		mes "''^666666When the chief and his";
		mes "soldiers arrived, the battle";
		mes "was over. Dead bodies lay";
		mes "everywhere, but there was";
		mes "one warrior covered in blood,";
		mes "half conscious, but living.^000000''";
		next;
		mes "[Archeologist]";
		mes "Strange, isn't it?";
		mes "This is from a journal";
		mes "written by a retired soldier";
		mes "that lives in Prontera now.";
		mes "It might be a good idea to";
		mes "see if you can talk to him.";
		set jewel_nd,25;
		close;
	}
	else if (jewel_nd == 25) {
		mes "[Archeologist]";
		mes "There's a retired soldier";
		mes "that actually witnessed what";
		mes "happened in the field west";
		mes "of Geffen. You should try";
		mes "to find him in Prontera, and";
		mes "see what you can learn.";
		close;
	}
	else if (jewel_nd > 28) {
		mes "[Archeologist]";
		mes "Ah, that's... That's";
		mes "very profound. That one";
		mes "warrior went berserk when";
		mes "he realized he was too weak";
		mes "to protect his comrades?";
		mes "He lost total control...";
		next;
		mes "[Archeologist]";
		mes "I better talk to that";
		mes "old soldier and get the";
		mes "full story on record.";
		mes "It'll be a really good history";
		mes "lesson for Swordmen everywhere.";
		mes "Thank you for letting me know.";
		close;
	}
	else {
		mes "[Archeologist]";
		mes "I'm so busy! Listen,";
		mes "would you mind coming";
		mes "back later when I'm not";
		mes "so swamped with work?";
		close;
	}
}

prontera,140,304,5	script	Old Soldier	901,{
	if (checkweight(907,200) == 0) {
		mes "[Retired Soldier]";
		mes "Why are you carrying";
		mes "so much stuff with you?";
		mes "Isn't there a Kafra Storage";
		mes "service for keeping your junk?";
		close;
	}
	if (jewel_nd == 25) {
		mes "[Retired Soldier]";
		mes "It's strange that so";
		mes "many people have been";
		mes "wanting to talk to me for";
		mes "some reason, but I'll";
		mes "admit that it's kind of nice.";
		next;
		switch(select("Ask About Past Battles:Ask About Fallen Warrior")) {
		case 1:
			mes "[Retired Soldier]";
			mes "Don't get me started.";
			mes "Once I start talking about";
			mes "the old days, I'll go on all";
			mes "night. Those were real hard";
			mes "times to live through, but good";
			mes "memories to remember...";
			close;
		case 2:
			mes "[Retired Soldier]";
			mes "The fallen warrior...?";
			mes "There's only one that brings";
			mes "a chill down my spine whenever";
			mes "I think of him. He was about";
			mes "your age when it happened...";
			next;
			mes "[Retired Soldier]";
			mes "We received a report about";
			mes "a Cobold threat, so we were";
			mes "dispatched to the fields";
			mes "west of Geffen. But the";
			mes "battle was over once we";
			mes "arrived. We were too late.";
			next;
			mes "[Retired Soldier]";
			mes "All the Cobolds were slain,";
			mes "but... There was one warrior";
			mes "left, covered in blood. When";
			mes "he saw us, he just started";
			mes "attacking everyone! He";
			mes "had completely lost it.";
			next;
			mes "[Retired Soldier]";
			mes "We had no choice but to";
			mes "kill him. It was self defense,";
			mes "but I can't help but pity him.";
			mes "All his comrades had been";
			mes "killed: I can't imagine how";
			mes "that must have felt.";
			next;
			mes "[Retired Soldier]";
			mes "We arrived too late to";
			mes "see that battle, but I'm";
			mes "pretty sure it was horrific.";
			mes "You must be pretty interested";
			mes "in learning about this soldier.";
			next;
			mes "[Retired Soldier]";
			mes "Well, I'm just glad that you";
			mes "were able to know more";
			mes "about this story. There's";
			mes "much you can learn from";
			mes "other people's experiences.";
			set jewel_nd,26;
			close;
		}
	}
	else if ((jewel_nd > 25) && (jewel_nd < 29)) {
		mes "[Retired Soldier]";
		mes "I don't regret that we";
		mes "had to kill that warrior.";
		mes "We did had we had to";
		mes "back in those days.";
		close;
	}
	else if (jewel_nd == 29) {
		mes "[Retired Soldier]";
		mes "Oh, it's you again.";
		mes "Did you want to ask me";
		mes "more about that warrior?";
		mes "I'm afraid I already told";
		mes "you everything I know.";
		next;
		switch(select("Sorry...:I met the warrior's spirit.")) {
		case 1:
			mes "[Retired Soldier]";
			mes "It's fine.";
			close;
		case 2:
			mes "[Retired Soldier]";
			mes "What? How is that";
			mes "even possible?";
			next;
			mes "^3355FFYou explain to the";
			mes "retired soldier everything";
			mes "that has happened so far.^000000";
			next;
			mes "[Retired Soldier]";
			mes "That's quite a story.";
			mes "Still, I'm surprised that";
			mes "he didn't want to talk to";
			mes "you about what happened.";
			mes "I guess he still harbors";
			mes "bad feelings about that day...";
			next;
			mes "[Retired Soldier]";
			mes "Well, I guess it doesn't";
			mes "really matter now. Chalk";
			mes "this all up to a really good";
			mes "learning experience.";
			next;
			switch(select("I feel so sorry for him.:Do you believe the jewel is really cursed?")) {
			case 1:
				mes "[Retired Soldier]";
				mes "Well...";
				mes "I don't know what to tell";
				mes "you. I mean, you can't really";
				mes "put the blame on anybody.";
				mes "He just lost his mind...";
				close;
			case 2:
				mes "[Retired Soldier]";
				mes "I'm not really sure if the";
				mes "jewel was cursed, if that";
				mes "warrior started the curse,";
				mes "or if the jewel isn't cursed";
				mes "at all. I'm just an old,";
				mes "simple soldier.";
				next;
				mes "[Retired Soldier]";
				mes "One thing's for sure:";
				mes "if there was a curse, that";
				mes "soldier should have been";
				mes "able to overcome it if his will";
				mes "was strong enough. It's harsh";
				mes "to say it like this, but...";
				next;
				mes "[Retired Soldier]";
				mes "True warriors have the";
				mes "strength to endure, to";
				mes "fight temptation, to keep";
				mes "their focus. That guy...";
				mes "He wasn't cut out to fight.";
				set jewel_nd,30;
				next;
				if (countitem(7725) > 0) {
					mes "[Retired Soldier]";
					mes "You know what?";
					mes "If you're so worried";
					mes "about a curse, why don't";
					mes "you leave that jewel with me?";
					mes "Don't worry, I'll make sure";
					mes "to compensate you for it.";
					next;
					mes "[Retired Soldier]";
					mes "I'd rather keep it to myself,";
					mes "rather than risk it messing";
					mes "around with other people.";
					mes "I know my will's tough enough";
					mes "to resist its curse, you know,";
					mes "if it actually exists.";
					delitem 7725,1; //Unlucky_Emerald
					set jewel_nd,31;
					getitem 603,1; //Old_Blue_Box
					if (checkre(3)) {
						if (BaseLevel < 66) getitem 607,1; //Yggdrasilberry
						else if ((BaseLevel > 65) && (BaseLevel < 75)) getexp 5000,0;
						else if ((BaseLevel > 74) && (BaseLevel < 81)) getexp 18000,0;
						else if ((BaseLevel > 80) && (BaseLevel < 86)) getexp 36000,0;
						else if ((BaseLevel > 85) && (BaseLevel < 91)) getexp 50000,0;
						else if ((BaseLevel > 90) && (BaseLevel < 96)) getexp 80000,0;
						else if ((BaseLevel > 95) && (BaseLevel < MAX_LEVEL)) getexp 100000,0;
						else getitem 617,1; //Old_Violet_Box
					} else {
						if (BaseLevel < 66) getitem 607,1; //Yggdrasilberry
						else if ((BaseLevel > 65) && (BaseLevel < 75)) getexp 50000,0;
						else if ((BaseLevel > 74) && (BaseLevel < 81)) getexp 180000,0;
						else if ((BaseLevel > 80) && (BaseLevel < 86)) getexp 360000,0;
						else if ((BaseLevel > 85) && (BaseLevel < 91)) getexp 500000,0;
						else if ((BaseLevel > 90) && (BaseLevel < 96)) getexp 800000,0;
						else if ((BaseLevel > 95) && (BaseLevel < 99)) getexp 1000000,0;
						else getitem 617,1; //Old_Violet_Box
					}
					next;
					mes "[Retired Soldier]";
					mes "If there's anything";
					mes "you learn from all this,";
					mes "it's that you should never";
					mes "succumb to your desperation.";
					mes "Rage gives you nothing:";
					mes "its power is an illusion.";
					close;
				}
				else {
					mes "[Retired Soldier]";
					mes "You know what?";
					mes "If you're so worried";
					mes "about a curse, why don't";
					mes "you leave that jewel with me?";
					mes "Don't worry, I'll make sure";
					mes "to compensate you for it.";
					next;
					mes "[Retired Soldier]";
					mes "I'd rather keep it to myself,";
					mes "rather than risk it messing";
					mes "around with other people.";
					mes "I know my will's tough enough";
					mes "to resist its curse, you know,";
					mes "if it actually exists.";
					close;
				}
			}
		}
	}
	else if (jewel_nd == 30) {
		if (countitem(7725) > 0) {
			delitem 7725,1; //Unlucky_Emerald
			set jewel_nd,31;
			getitem 603,1; //Old_Blue_Box
			if (checkre(3)) {
				if (BaseLevel < 66) getitem 607,1; //Yggdrasilberry
				else if ((BaseLevel > 65) && (BaseLevel < 75)) getexp 5000,0;
				else if ((BaseLevel > 74) && (BaseLevel < 81)) getexp 8000,0;
				else if ((BaseLevel > 80) && (BaseLevel < 86)) getexp 15000,0;
				else if ((BaseLevel > 85) && (BaseLevel < 91)) getexp 20000,0;
				else if ((BaseLevel > 90) && (BaseLevel < 96)) getexp 40000,0;
				else if ((BaseLevel > 95) && (BaseLevel < MAX_LEVEL)) getexp 50000,0;
				else getitem 617,1; //Old_Violet_Box
			} else {
				if (BaseLevel < 66) getitem 607,1; //Yggdrasilberry
				else if ((BaseLevel > 65) && (BaseLevel < 75)) getexp 50000,0;
				else if ((BaseLevel > 74) && (BaseLevel < 81)) getexp 80000,0;
				else if ((BaseLevel > 80) && (BaseLevel < 86)) getexp 150000,0;
				else if ((BaseLevel > 85) && (BaseLevel < 91)) getexp 200000,0;
				else if ((BaseLevel > 90) && (BaseLevel < 96)) getexp 400000,0;
				else if ((BaseLevel > 95) && (BaseLevel < 99)) getexp 500000,0;
				else getitem 617,1; //Old_Violet_Box
			}
			next;
			mes "[Retired Soldier]";
			mes "If there's anything";
			mes "you learn from all this,";
			mes "it's that you should never";
			mes "succumb to your desperation.";
			mes "Rage gives you nothing:";
			mes "its power is an illusion.";
			close;
		}
		else {
			mes "[Retired Soldier]";
			mes "You know what?";
			mes "If you're so worried";
			mes "about a curse, why don't";
			mes "you leave that jewel with me?";
			mes "Don't worry, I'll make sure";
			mes "to compensate you for it.";
			next;
			mes "[Retired Soldier]";
			mes "I'd rather keep it to myself,";
			mes "rather than risk it messing";
			mes "around with other people.";
			mes "I know my will's tough enough";
			mes "to resist its curse, you know,";
			mes "if it actually exists.";
			close;
		}
	}
	else if (jewel_nd == 31) {
		mes "[Retired Soldier]";
		mes "Well, you learned all there";
		mes "is to know about the Unlucky";
		mes "Emerald. It's time you forget";
		mes "about the curse or whatever,";
		mes "and find new adventures.";
		mes "Ah, to be young again...";
		close;
	}
	else {
		mes "[Retired Soldier]";
		mes "Ah, how I long for";
		mes "the old days. Being";
		mes "retired is just so...";
		mes "It's so boring! I guess";
		mes "it's time to find a hobby.";
		close;
	}
}

gef_fild08,182,185,0	script	The Wanderer	739,{
	if (jewel_nd < 21) {
		mes "[The Wanderer]";
		mes "I'm just a wanderer";
		mes "drifting through this";
		mes "world. Please just";
		mes "leave me in peace.";
		close;
	}
	else if (jewel_nd == 21) {
		if (countitem(7725) > 0) {
			mes "[The Wanderer]";
			mes "That... That looks";
			mes "just like the emerald";
			mes "I used to own. Hmmm...";
			mes "Did you come looking for me?";
			next;
			mes "[The Wanderer]";
			mes "Get rid of that thing right";
			mes "now. It's an evil jewel that";
			mes "causes tragedy wherever";
			mes "it goes. I remember... I got";
			mes "that jewel from monsters...";
			next;
			mes "[The Wanderer]";
			mes "I was just a simple man,";
			mes "dreaming of becoming the";
			mes "world's greatest warrior.";
			mes "I had that emerald on me";
			mes "when we encountered a huge";
			mes "group of vicious Cobolds.";
			next;
			mes "[The Wanderer]";
			mes "No matter how many";
			mes "we killed, there was";
			mes "just no end to them...";
			mes "And then... We... We...";
			mes "I... It hurts too much";
			mes "to recall what happened...";
			set jewel_nd,22;
			hideonnpc "The Wanderer";
			stopnpctimer;
			close;
		}
		else {
			mes "[The Wanderer]";
			mes "Why have you summoned me?";
			mes "I'm just the soul of a fallen";
			mes "warrior, seeking peace.";
			mes "Please don't bother me";
			mes "if you can avoid it.";
			close;
		}
	}
	else if (jewel_nd == 22) {
		mes "[The Wanderer]";
		mes "You again? Why do you still";
		mes "hold the jewel?! Didn't I just";
		mes "what happened to me and";
		mes "my comrades! We all died";
		mes "that day, that day the";
		mes "Cobolds overpowered us!";
		next;
		mes "[The Wanderer]";
		mes "Just let me return";
		mes "to my slumber.";
		set jewel_nd,23;
		close;
	}
	else if (jewel_nd == 25) {
		mes "[The Wanderer]";
		mes "Why have you called";
		mes "me here again? What";
		mes "is it you want from me?";
		mes "Can't you leave me alone?";
		next;
		select("Were you really killed by Cobolds?");
		mes "[The Wanderer]";
		mes "Argh! What is it you're";
		mes "trying to say?! Leave now!";
		hideonnpc "The Wanderer";
		stopnpctimer;
		close;
	}
	else if (jewel_nd == 26) {
		mes "[The Wanderer]";
		mes "Still you pester me.";
		mes "Unfortunately, the power";
		mes "of the jewel compels me";
		mes "to return to this plane...";
		mes "What is it this time?";
		next;
		select("You weren't killed by Cobolds, were you?");
		mes "[The Wanderer]";
		mes "How many times must I tell";
		mes "you? Are you saying that";
		mes "I'm lying about the deaths";
		mes "of my comrades? Forget it.";
		mes "The dead shouldn't talk.";
		mes "It's a waste of my time.";
		next;
		mes "[The Wanderer]";
		mes "Take care, young";
		mes "adventurer, and don't";
		mes "reopen the wounds of";
		mes "the past anymore.";
		set jewel_nd,27;
		next;
		while(1) {
			if (select("You killed your comrades!:Do you remember your last moment?") == 1) {
				mes "[The Wanderer]";
				mes "Did I...? Ha. Haha!";
				mes "adventurer, but that sounds";
				mes "like something from a trashy";
				mes "novel. Hah hah hah hah!";
				next;
			}
			else {
				mes "[The Wanderer]";
				mes "It's a little hazy...";
				mes "I... We tried to kill as";
				mes "many Cobolds as we ";
				mes "could... Everyone was...";
				mes "Everyone was dead.";
				mes "Everyone but me.";
				next;
				mes "[The Wanderer]";
				mes "I was all alone.";
				mes "We killed all the";
				mes "Cobolds, but I was...";
				mes "Then... The next thing...";
				mes "I was covered in blood.";
				mes "Then, something appeared?";
				next;
				mes "[The Wanderer]";
				mes "No, it can't have been.";
				mes "A human soldier like me?";
				set jewel_nd,28;
				next;
				break;
			}
		}
		mes "[The Wanderer]";
		mes "I was killed... No, wait.";
		mes "I survived the battle with";
		mes "the Cobolds. Then, some";
		mes "coward killed me from";
		mes "behind, and stole my";
		mes "emerald. That must be it!";
		next;
		select("How did your comrades die?");
		mes "[The Wanderer]";
		mes "The Cobolds killed them!";
		mes "I was too weak to protect";
		mes "my comrades. Too weak...";
		mes "When I came back to my";
		mes "senses, they were all...";
		next;
		emotion ET_THINK;
		mes "[The Wanderer]";
		mes "I'm not sure what";
		mes "happened. There was";
		mes "blood coming into my eyes...";
		mes "I remember slashing, and";
		mes "stabbing, and... And slashing.";
		next;
		select("You slashed everything...");
		mes "["+ strcharinfo(0) +"]";
		mes "You slashed everything...";
		mes "Even the comrades you";
		mes "were supposed to protect.";
		next;
		mes "[The Wanderer]";
		mes "I... I did...?";
		mes "I... No! Shut up!";
		mes "Stop it! I couldn't...";
		mes "Get out of here now!";
		mes "You're trying to trick me!";
		set jewel_nd,29;
		hideonnpc "The Wanderer";
		stopnpctimer;
		close;
	}
	else if (jewel_nd == 27) {
		mes "[The Wanderer]";
		mes "What is it?";
		mes "What could you";
		mes "possibly have to";
		mes "say to me this time?";
		next;
		while(1) {
			if (select("You killed your comrades!:Do you remember your last moment?") == 1) {
				mes "[The Wanderer]";
				mes "Did I...? Ha. Haha!";
				mes "adventurer, but that sounds";
				mes "like something from a trashy";
				mes "novel. Hah hah hah hah!";
				next;
			}
			else {
				mes "[The Wanderer]";
				mes "It's a little hazy...";
				mes "I... We tried to kill as";
				mes "many Cobolds as we ";
				mes "could... Everyone was...";
				mes "Everyone was dead.";
				mes "Everyone but me.";
				next;
				mes "[The Wanderer]";
				mes "I was all alone.";
				mes "We killed all the";
				mes "Cobolds, but I was...";
				mes "Then... The next thing...";
				mes "I was covered in blood.";
				mes "Then, something appeared?";
				next;
				mes "[The Wanderer]";
				mes "No, it can't have been.";
				mes "A human soldier like me?";
				set jewel_nd,28;
				next;
				break;
			}
		}
		mes "[The Wanderer]";
		mes "I was killed... No, wait.";
		mes "I survived the battle with";
		mes "the Cobolds. Then, some";
		mes "coward killed me from";
		mes "behind, and stole my";
		mes "emerald. That must be it!";
		next;
		select("How did your comrades die?");
		mes "[The Wanderer]";
		mes "The Cobolds killed them!";
		mes "I was too weak to protect";
		mes "my comrades. Too weak...";
		mes "When I came back to my";
		mes "senses, they were all...";
		next;
		emotion ET_THINK;
		mes "[The Wanderer]";
		mes "I'm not sure what";
		mes "happened. There was";
		mes "blood coming into my eyes...";
		mes "I remember slashing, and";
		mes "stabbing, and... And slashing.";
		next;
		select("You slashed everything...");
		mes "["+ strcharinfo(0) +"]";
		mes "You slashed everything...";
		mes "Even the comrades you";
		mes "were supposed to protect.";
		next;
		mes "[The Wanderer]";
		mes "I... I did...?";
		mes "I... No! Shut up!";
		mes "Stop it! I couldn't...";
		mes "Get out of here now!";
		mes "You're trying to trick me!";
		set jewel_nd,29;
		hideonnpc "The Wanderer";
		stopnpctimer;
		close;
	}
	else if (jewel_nd == 28) {
		mes "[The Wanderer]";
		mes "Why you...";
		next;
		select("How did your comrades die?");
		mes "[The Wanderer]";
		mes "The Cobolds killed them!";
		mes "I was too weak to protect";
		mes "my comrades. Too weak...";
		mes "When I came back to my";
		mes "senses, they were all...";
		next;
		emotion ET_THINK;
		mes "[The Wanderer]";
		mes "I'm not sure what";
		mes "happened. There was";
		mes "blood coming into my eyes...";
		mes "I remember slashing, and";
		mes "stabbing, and... And slashing.";
		next;
		select("You slashed everything...");
		mes "["+ strcharinfo(0) +"]";
		mes "You slashed everything...";
		mes "Even the comrades you";
		mes "were supposed to protect.";
		next;
		mes "[The Wanderer]";
		mes "I... I did...?";
		mes "I... No! Shut up!";
		mes "Stop it! I couldn't...";
		mes "Get out of here now!";
		mes "You're trying to trick me!";
		set jewel_nd,29;
		hideonnpc "The Wanderer";
		stopnpctimer;
		close;
	}
	else if (jewel_nd == 29) {
		mes "[The Wanderer]";
		mes "Leave me alone!";
		close;
	}
	else if (jewel_nd > 29) {
		mes "[The Wanderer]";
		mes "Shut up! Stop harassing";
		mes "me! I can't... I don't...!";
		close;
	}
	else {
		mes "[The Wanderer]";
		mes ".........";
		close;
	}

OnInit:
	hideonnpc "The Wanderer";
	end;

OnEnable:
	hideoffnpc "The Wanderer";
	initnpctimer;
	end;

OnTimer60000:
	hideonnpc "The Wanderer";
	stopnpctimer;
	end;
}

gef_fild08,182,182,0	script	#CallGhost	-1,2,2,{
	end;

OnTouch:
	if (jewel_nd == 21) {
		if (countitem(7725) > 0) {
			mes "[" + strcharinfo(0) + "]";
			mes "This must be the place";
			mes "that shaman was talking";
			mes "about. Let's see now...";
			donpcevent "The Wanderer::OnEnable";
			close;
		}
		else {
			mes "[" + strcharinfo(0) + "]";
			mes "This must be the place";
			mes "that shaman was talking";
			mes "about. Let's see now...";
			mes "Wait. I need that emerald...";
			close;
		}
	}
	else if ((jewel_nd > 21) && (jewel_nd < 29)) {
		donpcevent "The Wanderer::OnEnable";
	}
	end;
}

// Rogue Guild Investigation
//============================================================
prontera,163,204,3	script	Man from Morocc	99,{
	if (rumour_nd == 0) {
		mes "[Morocc Traveler]";
		mes "Ah, it's very nice and";
		mes "refreshing to be near this";
		mes "fountain. It's much cooler";
		mes "here than back in Morocc.";
		next;
		switch(select("Nice day today, huh?:How's Morocc lately?")) {
		case 1:
			mes "[Morocc Traveler]";
			mes "Oh, yes. It's a wonderful";
			mes "day! Prontera is so sunny";
			mes "and cool. No wonder so";
			mes "many people live over here!";
			close;
		case 2:
			mes "[Morocc Traveler]";
			mes "You're an adventurer, and";
			mes "you haven't heard the crazy";
			mes "rumors spreading all over";
			mes "the Rune-Midgarts kingdom?";
			mes "I thought you guys traveled";
			mes "around and heard things.";
			next;
		}
		select("What crazy rumors?");
		mes "[Morocc Traveler]";
		mes "Uh... Rogue agents!";
		mes "They're all over the place!";
		mes "Why don't you talk to some";
		mes "of the Rogue agents dispatched";
		mes "in the other towns? Then you'll";
		mes "know what I'm talking about!";
		set rumour_nd,1;
		close;
	}
	else if (rumour_nd == 1) {
		mes "[Morocc Traveler]";
		mes "Shouldn't you be";
		mes "looking for a Rogue";
		mes "agent to talk to?";
		mes "I mean, maybe you";
		mes "can pitch in and";
		mes "help them out.";
		close;
	}
	else if (rumour_nd > 1) {
		if (zdan_edq > 12) {
			mes "[Morocc Traveler]";
			mes "So the Z Gang was what";
			mes "those crazy rumors were";
			mes "all about. Good to hear";
			mes "that they're all in jail.";
			close;
		}
		else {
			mes "[Morocc Traveler]";
			mes "Hey, uh...";
			mes "I'm just trying to";
			mes "relax on my vacation.";
			close;
		}
	}
}

in_rogue,355,28,5	script	Rogue Agent	118,{
	if (checkweight(907,200) == 0) {
		mes "[Guildsman]";
		mes "You're carrying too";
		mes "much stuff on you.";
		mes "Take a load off, put";
		mes "your extra stuff in";
		mes "Kafra Storage: that's";
		mes "what's it's for~";
		close;
	}
	if (rumour_nd == 0) {
		if (zdan_edq == 0) {
			if (BaseJob == Job_Rogue) {
				mes "[Guildsman]";
				mes "Man, it feels much safer";
				mes "hanging out in an alley";
				mes "than some crowded joint.";
				mes "Us Rogues... I guess you";
				mes "can say we got a bit of";
				mes "agoraphobia, naturally.";
				close;
			}
			else {
				mes "[Guildsman]";
				mes "Hey, I don't think";
				mes "you're supposed to be";
				mes "here. You better get";
				mes "going on your way.";
				mes "It's not a threat, just";
				mes "a friendly word of advice.";
				close;
			}
		}
		else if ((zdan_edq > 0) && (zdan_edq < 13)) {
			mes "[Guildsman]";
			mes "Hey, I don't think";
			mes "I can help you out.";
			mes "You're pretty much";
			mes "on your own from here.";
			close;
		}
		else {
			mes "[Guildsman]";
			mes "Huh. I hate clean-up";
			mes "duty. It's always better";
			mes "to make messes than to";
			mes "hang around afterwards.";
			close;
		}
	}
	else if (rumour_nd == 1) {
		mes "[Guildsman]";
		mes "Yeah, we're investigating";
		mes "the strange crimes that are";
		mes "going on around the kingdom.";
		mes "How'd you know about that?";
		next;
		switch(select("I was just...:Can I help?")) {
		case 1:
			mes "[Guildsman]";
			mes "Just what...?";
			mes "Speak up, pal.";
			close;
		case 2:
			if (zdan_edq > 10) {
				mes "[Guildsman]";
				mes "So you wanted to help";
				mes "us out? Heck, we could";
				mes "use the help, I guess.";
				mes "I need you to go on a bit";
				mes "of a trip for me, so get";
				mes "some provisions ready, yeah?";
				set rumour_nd,2;
			}
			close;
		}
	}
	else if (rumour_nd == 2) {
		mes "[Guildsman]";
		mes "You all ready and";
		mes "everything? Good.";
		mes "Now, I need you to";
		mes "deliver some intel to";
		mes "the other Rogues for me.";
		mes "Think you can do that?";
		next;
		mes "[Guildsman]";
		mes "First, I want you to";
		mes "go to Al De Baran.";
		mes "Find our guy, and help";
		mes "him out any way you can.";
		mes "Us Rogues are hard to";
		mes "find, but you'll figure it out.";
		next;
		mes "[Guildsman]";
		mes "I know that's probably";
		mes "not enough instruction,";
		mes "but that's just the first";
		mes "step, anyway. Good luck.";
		set rumour_nd,3;
		close;
	}
	else if (rumour_nd == 3) {
		mes "[Guildsman]";
		mes "Hey, you gotta get";
		mes "goin' to Al De Baran,";
		mes "and find one of us Rogues";
		mes "working on the investigation.";
		mes "I thought you wanted to help?";
		next;
		mes "[Guildsman]";
		mes "Here's a bit of a tip~";
		mes "Us Rogue agents usually";
		mes "try to hide out in the";
		mes "south side of towns.";
		mes "Bit of a tradition.";
		close;
	}
	else if (rumour_nd == 24) {
		mes "[Guildsman]";
		mes "What's this, a report";
		mes "from Al De Baran? 'Kay,";
		mes "let me give it a read...";
		next;
		mes "[Guildsman]";
		mes "Huh, okay. Looks like";
		mes "your next stop is gonna";
		mes "be in Geffen. Look for our";
		mes "Geffen agent somewhere in";
		mes "the South Geffen area, okay?";
		mes "Ah, and take this note too.";
		next;
		mes "^3355FFYou received";
		mes "another folded note.^000000";
		set rumour_nd,25;
		close;
	}
	else if (rumour_nd == 25) {
		mes "[Guildsman]";
		mes "Hey, why haven't you";
		mes "left for Geffen? Our";
		mes "agent oughta be in the";
		mes "south side of town.";
		mes "It's not far, so don't";
		mes "throw a hissy fit.";
		close;
	}
	else if (rumour_nd == 21) {
		if (zdan_edq > 12) {
			mes "[Guildsman]";
			mes "Finally, the Z Gang";
			mes "case is closed. Those";
			mes "guys really gave us some";
			mes "trouble. Why the hell did";
			mes "they stir up so much ruckus";
			mes "in the kingdom, anyhow?";
			next;
			mes "[Guildsman]";
			mes "Listen, I wanna thank";
			mes "you for pitching in and";
			mes "helping out. It's a small";
			mes "gift, but maybe you'll";
			mes "like it. If not, don't";
			mes "lemme know. Got it?";
			set rumour_nd,22;
			getitem 644,1; //Gift_Box
			close;
		}
		else {
			mes "[Guildsman]";
			mes "Listen, I wanna thank";
			mes "you for pitching in and";
			mes "helping out. It's a small";
			mes "gift, but maybe you'll";
			mes "like it. If not, don't";
			mes "lemme know. Got it?";
			set rumour_nd,22;
			getitem 644,1; //Gift_Box
			close;
		}
	}
	else if (rumour_nd == 22) {
		mes "[Guildsman]";
		mes "Hey, you were really";
		mes "a big help earlier.";
		mes "Can we count on you";
		mes "again sometime? The";
		mes "Rogues can always use";
		mes "another friend, you know?";
		close;
	}
	else {
		mes "[Guildsman]";
		mes "I don't know who you're";
		mes "lookin' for, but you";
		mes "got the wrong guy.";
		mes "Listen, I got work";
		mes "to do, so...";
		close;
	}
}

aldebaran,114,56,5	script	Rogue Agent#nd0	828,{
	if (rumour_nd < 3) {
		mes "[Agent]";
		mes "Listen, I'm working on";
		mes "an important assignment";
		mes "for the Rogue Guild.";
		mes "Would let lemme alone";
		mes "so I can do what I gotta?";
		close;
	}
	else if (rumour_nd == 3) {
		if (zdan_edq > 12) {
			mes "[Agent]";
			mes "They sent you to help me?";
			mes "Eh, thanks for comin' so";
			mes "far. I've been collecting";
			mes "intel here in Al De Baran.";
			mes "I'm pretty sure the Z Gang's";
			mes "spreading rumors around here.";
			next;
			mes "[Agent]";
			mes "They've been telling";
			mes "people there's gonna be";
			mes "a war comin', but... It";
			mes "seems like a load of huey.";
			mes "The Z Gang's captured so";
			mes "the rumor'll just die out...";
			next;
			mes "[Agent]";
			mes "Do me a favor, and";
			mes "get this report over";
			mes "the agent in Geffen?";
			mes "Thanks, I'd appreciate it.";
			next;
			mes "^3355FFYou received a";
			mes "tightly folded note.^000000";
			set rumour_nd,4;
			close;
		}
		else {
			mes "[Agent]";
			mes "They sent you to help me?";
			mes "Eh, thanks for comin' so";
			mes "far. I've been collecting";
			mes "intel here in Al De Baran.";
			next;
			mes "[Agent]";
			mes "There's this rumor goin'";
			mes "around that war's comin'";
			mes "but... I don't see any real";
			mes "signs of it. Trains, teleports,";
			mes "airships, they still keep";
			mes "on doin' what they do.";
			next;
			mes "[Agent]";
			mes "I'm not sure who, but";
			mes "someone's probably just";
			mes "spreading this rumor to";
			mes "get people to panick.";
			mes "It's kinda workin', though...";
			next;
			mes "[Agent]";
			mes "Do me a favor, and";
			mes "get this report over";
			mes "the agent in Geffen?";
			mes "Thanks, I'd appreciate it.";
			next;
			mes "^3355FFYou received a";
			mes "tightly folded note.^000000";
			set rumour_nd,24;
			close;
		}
	}
	else if ((rumour_nd == 4) || (rumour_nd == 24)) {
		mes "[Agent]";
		mes "Hey...";
		if (rumour_nd == 4) {
			mes "Would you please get";
			mes "my report over to the";
			mes "Rogue agent in Geffen?";
			mes "You'd better hurry since";
			mes "we gotta work quick.";
			close;
		}
		else {
			mes "Would you please get";
			mes "my report over to the";
			mes "Rogue Guild? They";
			mes "need to read it A.S.A.P.";
			mes "You know, pronto.";
			close;
		}
	}
	else if (rumour_nd == 16) {
		mes "[Agent]";
		mes "What's this, a report";
		mes "from the Payon agent?";
		mes "Um, listen, I don't think";
		mes "this is for me. You might";
		mes "wanna try one of the other";
		mes "Rogue agents around, okay?";
		set rumour_nd,17;
		close;
	}
	else if (rumour_nd > 21) {
		if (zdan_edq > 12) {
			mes "[Agent]";
			mes "Hey, thanks for";
			mes "helping out. I sure";
			mes "hope the kingdom can";
			mes "enjoy a little peace";
			mes "with the Z Gang out";
			mes "of the picture.";
			close;
		}
		else {
			mes "[Agent]";
			mes "Well, we've tracked";
			mes "down the Z Gang. All we";
			mes "gotta do now is get them!";
			close;
		}
	}
	else {
		mes "[Agent]";
		mes "I still got some";
		mes "things to do around";
		mes "here. Them's the breaks.";
		close;
	}
}

geffen,91,36,5	script	Rogue Guild Agent#nd1	828,{
	if (rumour_nd < 4) {
		if (zdan_edq > 12) {
			mes "[Rogue Guild Agent]";
			mes "Are you the one that's";
			mes "been helping to take";
			mes "down the Z Gang? Nice!";
			close;
		}
		else {
			mes "[Rogue Guild Agent]";
			mes "If you got nothing";
			mes "to do with me, then...";
			mes "I got nothing to do";
			mes "with you. Later!";
			close;
		}
	}
	else if (rumour_nd == 4) {
		if (zdan_edq > 12) {
			mes "[Rogue Guild Agent]";
			mes "This is from the";
			mes "Al De Baran agent?";
			mes "Thanks! It musta been";
			mes "kinda hard to find me, eh?";
			next;
			mes "[Rogue Guild Agent]";
			mes "The Z Gang's been";
			mes "spreading rumors about";
			mes "ghosts pouring out of";
			mes "Geffen Tower. Just to be";
			mes "sure, I want you to check";
			mes "it out. Will you do that?";
			next;
			mes "[Rogue Guild Agent]";
			mes "I want you to capture";
			mes "some monsters, and get";
			mes "some proof that shows that";
			mes "the ghosts stay inside the";
			mes "tower, and they're not";
			mes "coming out. Yeah, bring...";
			next;
			mes "[Rogue Guild Agent]";
			mes "^FF00001 Fabric^000000,";
			mes "^FF00001 Golden Hair^000000,";
			mes "^FF00001 Little Evil Horn^000000,";
			mes "^FF00001 Horseshoe^000000, and";
			mes "^FF00001 Jack O' Pumpkin^000000.";
			mes "That oughta do it.";
			next;
			mes "[Rogue Guild Agent]";
			mes "We just need to get";
			mes "a little concrete proof";
			mes "so that the people can";
			mes "calm down, and not panick.";
			mes "Hurry it up before a riot";
			mes "breaks out or somethin'.";
			set rumour_nd,5;
			close;
		}
		else {
			mes "[Rogue Guild Agent]";
			mes "This report is from the";
			mes "Rogue agent in Al De Baran?";
			mes "Hey, this says that this is";
			mes "for the Rogue Guild. You";
			mes "better take this over there.";
			close;
		}
	}
	else if (rumour_nd == 5) {
		mes "[Rogue Guild Agent]";
		mes "Hey, you done hunting";
		mes "down the monsters in";
		mes "Geffen Tower yet?";
		mes "I hope you brought";
		mes "the stuff...";
		next;
		switch(select("Give Items:I forgot what you need.")) {
		case 1:
			if ((countitem(1062) > 0) && (countitem(944) > 0) && (countitem(1038) > 0) && (countitem(1060) > 0) && (countitem(1059) > 0)) {
				delitem 1062,1; //Pumpkin_Head
				delitem 944,1; //Horseshoe
				delitem 1038,1; //Petite_DiablOfs_Horn
				delitem 1060,1; //Golden_Hair
				delitem 1059,1; //Transparent_Cloth
				set rumour_nd,6;
				mes "[Agent]";
				mes "Thanks. This stuff";
				mes "should help the people";
				mes "in Geffen calm down a bit.";
				mes "The Z Gang's been doin'";
				mes "all these things to make them";
				mes "believe the monsters are loose.";
				next;
				mes "[Agent]";
				mes "They've been leavin'";
				mes "horseprints that look like";
				mes "they belong to Nightmares,";
				mes "dressing up in Fabric and";
				mes "pretending they're Whispers...";
				mes "They're freakin' hooligans.";
				next;
				mes "[Agent]";
				mes "I think the Rogue agent";
				mes "in Payon should know about";
				mes "all this. Take this report";
				mes "over to him, will you?";
				mes "Thanks a bundle, pal.";
				close;
			}
			else {
				mes "[Agent]";
				mes "Hold on, you're";
				mes "missing some stuff.";
				mes "You mind checking if";
				mes "you left some of it";
				mes "behind somewhere?";
				close;
			}
		case 2:
			mes "[Agent]";
			mes "^FF00001 Fabric^000000,";
			mes "^FF00001 Golden Hair^000000,";
			mes "^FF00001 Little Evil Horn^000000,";
			mes "^FF00001 Horseshoe^000000, and";
			mes "^FF00001 Jack O' Pumpkin^000000.";
			mes "Don't forget this time!";
			close;
		}
	}
	else if (rumour_nd == 6) {
		mes "[Agent]";
		mes "Would you deliver";
		mes "my report over to";
		mes "the Rogue agent";
		mes "in Payon? Thanks.";
		close;
	}
	else if (rumour_nd == 25) {
		mes "[Agent]";
		mes "Hey, the guild sent";
		mes "you over to see me?";
		mes "Oh, hey, you brought";
		mes "me a message. Let's";
		mes "take a look-see~";
		next;
		mes "[Agent]";
		mes "Umm...";
		next;
		if (zdan_edq > 12) {
			mes "[Agent]";
			mes "So... The Z Gang's";
			mes "responsible for those";
			mes "rumors about the ghosts";
			mes "running loose in Geffen.";
			mes "Huh. Those rascally guys.";
			mes "Okay, this is what we do.";
			next;
			mes "[Rogue Guild Agent]";
			mes "I want you to capture";
			mes "some monsters, and get";
			mes "some proof that shows that";
			mes "the ghosts stay inside the";
			mes "tower, and they're not";
			mes "coming out. Yeah, bring...";
			next;
			mes "[Rogue Guild Agent]";
			mes "^FF00001 Fabric^000000,";
			mes "^FF00001 Golden Hair^000000,";
			mes "^FF00001 Little Evil Horn^000000,";
			mes "^FF00001 Horseshoe^000000, and";
			mes "^FF00001 Jack O' Pumpkin^000000.";
			mes "That oughta do it.";
			next;
			mes "[Rogue Guild Agent]";
			mes "We just need to get";
			mes "a little concrete proof";
			mes "so that the people can";
			mes "calm down, and not panick.";
			mes "Hurry it up before a riot";
			mes "breaks out or somethin'.";
			set rumour_nd,5;
			close;
		}
		else {
			mes "[Rogue Guild Agent]";
			mes "The Z Gang's been";
			mes "spreading rumors about";
			mes "ghosts pouring out of";
			mes "Geffen Tower. Just to be";
			mes "sure, I want you to check";
			mes "it out. Will you do that?";
			next;
			mes "[Rogue Guild Agent]";
			mes "I want you to capture";
			mes "some monsters, and get";
			mes "some proof that shows that";
			mes "the ghosts stay inside the";
			mes "tower, and they're not";
			mes "coming out. Yeah, bring...";
			next;
			mes "[Rogue Guild Agent]";
			mes "^FF00001 Fabric^000000,";
			mes "^FF00001 Golden Hair^000000,";
			mes "^FF00001 Little Evil Horn^000000,";
			mes "^FF00001 Horseshoe^000000, and";
			mes "^FF00001 Jack O' Pumpkin^000000.";
			mes "That oughta do it.";
			next;
			mes "[Rogue Guild Agent]";
			mes "We just need to get";
			mes "a little concrete proof";
			mes "so that the people can";
			mes "calm down, and not panick.";
			mes "Hurry it up before a riot";
			mes "breaks out or somethin'.";
			set rumour_nd,26;
			close;
		}
	}
	else if (rumour_nd == 26) {
		if (zdan_edq > 12) {
			mes "[Agent]";
			mes "Hey, while you were";
			mes "gone, I heard the Z Gang";
			mes "was finally captured.";
			mes "Big relief to hear it.";
			next;
			mes "[Agent]";
			mes "But I still want to";
			mes "reassure the people in";
			mes "Geffen that the monsters";
			mes "in Geffen Tower are safely";
			mes "locked up inside. You";
			mes "bring the stuff?";
			next;
			switch(select("Give Items:I forgot what you need.")) {
			case 1:
				if ((countitem(1062) > 1) && (countitem(944) > 2) && (countitem(1038) > 1) && (countitem(1060) > 2) && (countitem(1059) > 0)) {
					delitem 1062,2; //Pumpkin_Head
					delitem 944,3; //Horseshoe
					delitem 1038,2; //Petite_DiablOfs_Horn
					delitem 1060,3; //Golden_Hair
					delitem 1059,1; //Transparent_Cloth
					set rumour_nd,6;
					mes "[Agent]";
					mes "Thanks. This stuff";
					mes "should help the people";
					mes "in Geffen calm down a bit.";
					mes "The Z Gang's been doin'";
					mes "all these things to make them";
					mes "believe the monsters are loose.";
					next;
					mes "[Agent]";
					mes "They've been leavin'";
					mes "horseprints that look like";
					mes "they belong to Nightmares,";
					mes "dressing up in Fabric and";
					mes "pretending they're Whispers...";
					mes "They're freakin' hooligans.";
					next;
					mes "[Agent]";
					mes "I think the Rogue agent";
					mes "in Payon should know about";
					mes "all this. Take this report";
					mes "over to him, will you?";
					mes "Thanks a bundle, pal.";
					close;
				}
				else {
					mes "[Agent]";
					mes "Hold on, you're";
					mes "missing some stuff.";
					mes "You mind checking if";
					mes "you left some of it";
					mes "behind somewhere?";
					close;
				}
			case 2:
				mes "[Rogue Guild Agent]";
				mes "^FF00001 Fabric^000000,";
				mes "^FF00001 Golden Hair^000000,";
				mes "^FF00001 Little Evil Horn^000000,";
				mes "^FF00001 Horseshoe^000000, and";
				mes "^FF00001 Jack O' Pumpkin^000000.";
				mes "Don't forget this time!";
				close;
			}
		}
		else {
			mes "[Agent]";
			mes "I asked you to bring";
			mes "me items that prove that";
			mes "the monsters in Geffen";
			mes "Tower are safely locked";
			mes "up in there. You do that yet?";
			next;
			switch(select("Give Items:I forgot what you need.")) {
			case 1:
				if ((countitem(1062) > 1) && (countitem(944) > 2) && (countitem(1038) > 1) && (countitem(1060) > 2) && (countitem(1059) > 0)) {
					delitem 1062,2; //Pumpkin_Head
					delitem 944,3; //Horseshoe
					delitem 1038,2; //Petite_DiablOfs_Horn
					delitem 1060,3; //Golden_Hair
					delitem 1059,1; //Transparent_Cloth
					set rumour_nd,6;
					mes "[Agent]";
					mes "Thanks. This stuff";
					mes "should help the people";
					mes "in Geffen calm down a bit.";
					mes "The Z Gang's been doin'";
					mes "all these things to make them";
					mes "believe the monsters are loose.";
					next;
					mes "[Agent]";
					mes "They've been leavin'";
					mes "horseprints that look like";
					mes "they belong to Nightmares,";
					mes "dressing up in Fabric and";
					mes "pretending they're Whispers...";
					mes "They're freakin' hooligans.";
					next;
					mes "[Agent]";
					mes "I think the Rogue agent";
					mes "in Payon should know about";
					mes "all this. Take this report";
					mes "over to him, will you?";
					mes "Thanks a bundle, pal.";
					close;
				}
				else {
					mes "[Agent]";
					mes "Hold on, you're";
					mes "missing some stuff.";
					mes "You mind checking if";
					mes "you left some of it";
					mes "behind somewhere?";
					close;
				}
			case 2:
				mes "[Agent]";
				mes "^FF00001 Fabric^000000,";
				mes "^FF00001 Golden Hair^000000,";
				mes "^FF00001 Little Evil Horn^000000,";
				mes "^FF00001 Horseshoe^000000, and";
				mes "^FF00001 Jack O' Pumpkin^000000.";
				mes "Don't forget this time!";
			}
		}
	}
	else if (rumour_nd == 22) {
		if (zdan_edq > 12) {
			mes "[Agent]";
			mes "Looks like there'll";
			mes "be some peace and quiet";
			mes "with the Z Gang all locked up.";
			mes "Those guys were a double";
			mes "heaping of trouble if";
			mes "you ask me. Huh.";
		}
		else {
			mes "[Agent]";
			mes "Keep up the good work!";
			mes "With guys like you on";
			mes "our side, the Z Gang'll";
			mes "caught sooner than later.";
		}
		close;
	}
	else {
		mes "[Agent]";
		mes "So busy!";
		close;
	}
}

payon,209,44,5	script	Rogue Guild Agent#nd2	828,{
	if (rumour_nd < 3) {
		mes "[Agent]";
		mes "What? You want somethin'?";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "?????????????????";
		next;
		mes "[Agent]";
		mes "Heh. Thought so.";
		close;
	}
	else if ((rumour_nd > 2) && (rumour_nd < 6)) {
		mes "[Agent]";
		mes "What's up? I got";
		mes "my hands full, so";
		mes "make it quick.";
		next;
		mes "[Agent]";
		mes "Nothin', huh?";
		mes "Guess you should";
		mes "be talking a Rogue";
		mes "agent in one of the";
		mes "other towns.";
		emotion ET_ANGER;
		close;
	}
	else if (rumour_nd == 6) {
		mes "[Agent]";
		mes "Hey. What are you";
		mes "doin', just loitering";
		mes "around here? You lookin'";
		mes "for somebody? Huh?";
		next;
		select("I'm here to help a Rogue Agent!");
		mes "[Agent]";
		mes "Oh, yeah?";
		next;
		if (BaseJob == Job_Rogue) {
			mes "[Agent]";
			mes "Heya guy. Always good";
			mes "to see a fellow Rogue.";
			mes "You know I'm just messin'";
			mes "around with you, right?";
		}
		else {
			mes "[Agent]";
			mes "I heard o' you guys,";
			mes "Rogues workin' undercover.";
			mes "Don't try to fool me: under";
			mes "those clothes, you are one";
			mes "hundred percent Rogue. Heh!";
		}
		next;
		mes "^3355FFYou deliver the";
		mes "folded note to him.^000000";
		next;
		mes "[Agent]";
		mes "Awwww, man!";
		emotion ET_HUK;
		next;
		mes "[Agent]";
		mes "I keep telling that";
		mes "guy to just send me";
		mes "pictures! I've told him";
		mes "so many times that I've";
		mes "got trouble reading!";
		mes "That guy's a moron!";
		next;
		mes "[Agent]";
		mes "Uh...";
		mes "You mind reading";
		mes "this note to me?";
		set rumour_nd,7;
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "Let's see,";
		mes "the note says...";
		next;
		switch(select("Cooperate with this adventurer.:......")) {
		case 1:
			mes "[Agent]";
			mes "So... We're partners?";
			mes "Cool. Nice to meetcha.";
			mes "I'm the Payon agent.";
			mes "I guess we gotta work";
			mes "together from now on.";
			set rumour_nd,8;
			close;
		case 2:
			mes "[Agent]";
			mes "You...";
			mes "You can't read either?";
			next;
			mes "[Agent]";
			mes "Hahaha! Right on, right on.";
			mes "Well, I'm pretty sure they";
			mes "sent you to work with me.";
			mes "When you're ready to get";
			mes "to business, come back";
			mes "and we'll talk, okay?";
			set rumour_nd,8;
			close;
		}
	}
	else if (rumour_nd == 7) {
		mes "[Agent]";
		mes "Hey, I asked you";
		mes "to read the note,";
		mes "not run away! Now...";
		mes "What's it say?";
		next;
		mes "[" + strcharinfo(0) + "]";
		mes "Let's see,";
		mes "the note says...";
		next;
		switch(select("Cooperate with this adventurer.:......")) {
		case 1:
			mes "[Agent]";
			mes "So... We're partners?";
			mes "Cool. Nice to meetcha.";
			mes "I'm the Payon agent.";
			mes "I guess we gotta work";
			mes "together from now on.";
			set rumour_nd,8;
			close;
		case 2:
			mes "[Agent]";
			mes "You...";
			mes "You can't read either?";
			next;
			mes "[Agent]";
			mes "Hahaha! Right on, right on.";
			mes "Well, I'm pretty sure they";
			mes "sent you to work with me.";
			mes "When you're ready to get";
			mes "to business, come back";
			mes "and we'll talk, okay?";
			set rumour_nd,8;
			close;
		}
	}
	else if (rumour_nd == 8) {
		mes "[Agent]";
		mes "Good, you're back.";
		mes "We got a pretty big";
		mes "job to take care of.";
		mes "Lives are at stake,";
		mes "all that jazz. Ready?";
		next;
		if (zdan_edq > 12) {
			mes "[Agent]";
			mes "The Z Gang poisoned";
			mes "one of the water wells";
			mes "here in Payon. All the";
			mes "water's black now. We gotta";
			mes "use Red Herbs to counter";
			mes "that poison, okay?";
			next;
			mes "[Agent]";
			mes "So remember to";
			mes "collect some ^FF0000Red Herbs^000000.";
			mes "Don't forget: ^FF0000Red herbs^000000.";
			next;
			mes "[Agent]";
			mes "Wait, that doesn't";
			mes "sound right. Did I say";
			mes "Red Herbs? I'm sorry,";
			mes "I meant to say ^0000FFGreen";
			mes "Herbs^000000. Bring me ^0000FF1 Green";
			mes "Herb^000000, okay? *Phew~*";
			set rumour_nd,9;
			close;
		}
		else {
			mes "[Agent]";
			mes "Someone's poisoned";
			mes "Payon's well water...";
			mes "It's all black and";
			mes "deadly now. I want you";
			mes "to bring me a ^FF0000Red Herb^000000";
			mes "so that we can fix this!";
			set rumour_nd,10;
			close;
		}
	}
	else if (rumour_nd == 9) {
		mes "[Agent]";
		mes "Hey, you're back!";
		mes "You're much quicker";
		mes "that I thought you'd be~";
		next;
		if (countitem(511) > 0) {
			delitem 511,1; //Green_Herb
			set rumour_nd,11;
			mes "[Agent]";
			mes "Alright, it's gonna take";
			mes "a while to neutralize all";
			mes "this poison, but I think";
			mes "we did it. Hey, gimmie";
			mes "some time to draw a report,";
			mes "and then come back later, 'kay?";
			close;
		}
		else {
			mes "[Agent]";
			mes "Err?";
			mes "Where's that";
			mes "Green Herb?";
			close;
		}
	}
	else if (rumour_nd == 10) {
		mes "[Agent]";
		mes "Hey, you're back!";
		mes "You're much quicker";
		mes "that I thought you'd be~";
		next;
		if (countitem(507) > 0) {
			if (countitem(511) > 0) {
				mes "[Agent]";
				mes "Ah, a Green Herb!";
				mes "Is that what I told";
				mes "you to bring? 'Cause";
				mes "I'm sure this is the";
				mes "right one. Absolutely~";
				next;
				mes "[Agent]";
				mes "Alright, it's gonna take";
				mes "a while to neutralize all";
				mes "this poison, but I think";
				mes "we did it. Hey, gimmie";
				mes "some time to draw a report,";
				mes "and then come back later, 'kay?";
				delitem 511,1; //Green_Herb
				set rumour_nd,11;
				close;
			}
			else {
				mes "[Agent]";
				mes "Hey, what are you";
				mes "doin' with the Red Herb?";
				next;
				select("You said a Red Herb...");
				mes "[Agent]";
				mes "Oh, what?! Huh...";
				mes "I guess I did. Eh, no";
				mes "biggie. Hey, bring me";
				mes "a Green Herb, will you?";
				mes "That's the one I wanted.";
				set rumour_nd,9;
				close;
			}
		}
		else {
			if (countitem(511) > 0) {
				mes "[Agent]";
				mes "Alright, it's gonna take";
				mes "a while to neutralize all";
				mes "this poison, but I think";
				mes "we did it. Hey, gimmie";
				mes "some time to draw a report,";
				mes "and then come back later, 'kay?";
				delitem 511,1; //Green_Herb
				set rumour_nd,11;
				close;
			}
			else {
				mes "[Agent]";
				mes "Err?";
				mes "Where's that";
				mes "Green Herb?";
				close;
			}
		}
	}
	else if (rumour_nd == 11) {
		mes "[Agent]";
		mes "Hey, you know what?";
		mes "The well water is still";
		mes "black. That means...";
		mes "If the herb we used is";
		mes "good... Then it's just";
		mes "dyed black to scare people.";
		next;
		mes "[Agent]";
		mes "So to clean up the";
		mes "water, we gotta use";
		mes "a Counteragent, right?";
		mes "Will you help me out?";
		mes "I got a Karvodailnirol...";
		next;
		mes "[Agent]";
		mes "So if you bring me";
		mes "^0000FF1 Alcohol^000000, we should";
		mes "be totally good to";
		mes "make a Counteragent.";
		next;
		if (BaseJob == Job_Alchemist) {
			select("Wait, Karvodailnirol?!");
			mes "[Agent]";
			mes "Yeah. Karvodailnirol.";
			mes "What about it?";
			next;
			mes "[" + strcharinfo(0) + "]";
			mes "You need Detrimindexta";
			mes "to make a Counteragent,";
			mes "not Karvodailnirol.";
			next;
			mes "[Agent]";
			mes "Oh, really? Heh!";
			mes "You're a lifesaver!";
			mes "Detrimindexta, huh?";
			next;
			emotion ET_KIK;
			mes "[Agent]";
			mes "Don't worry, I got";
			mes "that one too! All you";
			mes "gotta do is bring Alcohol~";
			set rumour_nd,12;
			close;
		}
		else {
			mes "[Agent]";
			mes "Alright, just bring";
			mes "me an Alcohol, and";
			mes "I'll take care of the rest.";
			set rumour_nd,13;
			close;
		}
	}
	else if (rumour_nd == 12) {
		mes "[Agent]";
		mes "Everything's ready";
		mes "except for the Alcohol.";
		mes "Did you bring it?";
		next;
		if (countitem(970) > 0) {
			mes "[Agent]";
			mes "Great, now all we";
			mes "have to do is wait.";
			mes "Nice work, partner.";
			delitem 970,1; //Alchol
			set rumour_nd,15;
			close;
		}
		else {
			mes "[Agent]";
			mes "You don't have it?";
			mes "You didn't drink";
			mes "the alcohol, did you?";
			close;
		}
	}
	else if (rumour_nd == 13) {
		mes "[Agent]";
		mes "Everything's ready";
		mes "except for the Alcohol.";
		mes "Did you bring it?";
		next;
		if (countitem(970) > 0) {
			mes "[Agent]";
			mes "Great, now all we";
			mes "have to do is wait.";
			mes "Nice work, partner.";
			delitem 970,1; //Alchol
			set rumour_nd,14;
			close;
		}
		else {
			mes "[Agent]";
			mes "You don't have it?";
			mes "You didn't drink";
			mes "the alcohol, did you?";
			close;
		}
	}
	else if (rumour_nd == 14) {
		mes "[Agent]";
		mes "Um, I made a mistake";
		mes "making the Counteragent...";
		mes "I was supposed to use this";
		mes "Detrimindexta, not the other";
		mes "thing, Karvodailnirol, to";
		mes "make the Counteragent.";
		next;
		mes "[Agent]";
		mes "Sorry, they both look";
		mes "the same to me. And, well,";
		mes "so do all the letters of";
		mes "the alphabet. You mind";
		mes "trying this out again?";
		mes "Just 1 more Alcohol...";
		next;
		mes "[Agent]";
		mes "Hey, I promise!";
		mes "I won't screw it up";
		mes "this time. We'll";
		mes "do this for sure~";
		set rumour_nd,12;
		close;
	}
	else if (rumour_nd == 15) {
		emotion ET_SMILE;
		mes "[Agent]";
		mes "Heh heh! Looks like";
		mes "we solved the case!";
		mes "The water wasn't really";
		mes "poisoned... Just colored";
		mes "to look suspicious. See?";
		mes "Completely safe to drink.";
		next;
		mes "[Agent]";
		mes "Anyway, looks like";
		mes "we're done here. You";
		mes "mind taking my report over";
		mes "to the next Rogue agent?";
		next;
		if (rand(1,2) == 1) {
			mes "[Agent]";
			mes "Make sure to take";
			mes "this to the guy in";
			mes "Comodo, okay?";
			next;
			mes "^3355FFYou received a note that";
			mes "communicates solely";
			mes "through doodles.^000000";
			set rumour_nd,18;
			close;
		}
		else {
			mes "[Agent]";
			mes "Make sure to take";
			mes "this to the guy in";
			mes "Al De Baran, okay?";
			next;
			mes "^3355FFYou received a note that";
			mes "communicates solely";
			mes "through doodles.^000000";
			set rumour_nd,16;
			emotion ET_SMILE;
			close;
		}
	}
	else if (rumour_nd == 16) {
		mes "[Agent]";
		mes "Be sure that the";
		mes "Rogue agent over";
		mes "in Al De Baran gets";
		mes "my report, okay?";
		close;
	}
	else if (rumour_nd == 17) {
		mes "[Agent]";
		mes "Huh, the guy in";
		mes "Al De Baran wouldn't";
		mes "take it? Oh! That's why!";
		mes "I meant Comodo! You gotta";
		mes "go to Comodo! Heh, my bad~";
		set rumour_nd,18;
		close;
	}
	else if (rumour_nd == 18) {
		mes "[Agent]";
		mes "Be sure that the";
		mes "Rogue agent over";
		mes "in Comodo gets";
		mes "my report, okay?";
		close;
	}
	else {
		mes "[Agent]";
		mes "Hey, partner, how's";
		mes "it going? I'm sorry";
		mes "if I'm a bit of a hassle";
		mes "to work with. I might lack";
		mes "book smarts, but I make";
		mes "up for it some with my saavy.";
		close;
	}
}

comodo,233,199,5	script	Rogue Guild Agent#nd3	810,{
	if (checkweight(907,200) == 0) {
		mes "[Agent]";
		mes "You've got too many";
		mes "things on you. Why";
		mes "don't you put some";
		mes "of it in Kafra Storage";
		mes "before talking to me, eh?";
		close;
	}
	if (rumour_nd < 1) {
		mes "[Agent]";
		mes "......";
		mes "......";
		close;
	}
	else if ((rumour_nd > 0) && (rumour_nd < 16)) {
		if (zdan_edq > 12) {
			mes "[Agent]";
			mes "You're the one";
			mes "that helped take";
			mes "down the Z Gang?";
			mes "Heh! Good work~";
			close;
		}
		else {
			mes "[Agent]";
			mes "Who are you? Hm.";
			close;
		}
	}
	else if ((rumour_nd == 16) || (rumour_nd == 18)) {
		mes "[Agent]";
		mes "You know, Rogues are";
		mes "tricky and hard to catch,";
		mes "so I'm gonna teach you to";
		mes "carry yourself through some";
		mes "self-discipline. Got it?";
		mes "Discipline! You need it!";
		next;
		mes "[Agent]";
		mes "From here on out,";
		mes "I want you to yell";
		mes "''Yes, sir!'' when you";
		mes "acknowledge my instructions.";
		mes "You got that! Loud and clear!";
		next;
		switch(select("Yes, Sir!:...")) {
		case 1:
			emotion ET_THINK;
			mes "[Agent]";
			mes "Right on, right on.";
			mes "Hm. Let's try that again...";
			mes "Let me hear you say it louder!";
			next;
			select("Yes, Sir!");
			mes "[Agent]";
			mes "Nice!";
			next;
			mes "[Agent]";
			mes "Okay, now for your";
			mes "training instructions...";
			mes "There's a rumor that a monster";
			mes "has invaded the village, but";
			mes "I'm sure it's just someone";
			mes "trying to scare the public.";
			next;
			mes "[Agent]";
			mes "I'm pretty sure the, uh,";
			mes "perp is wearing a mask to";
			mes "fool people into thinkin'";
			mes "fhe's monster. This means...";
			mes "Your mission is to bring";
			mes "me some monsters masks.";
			next;
			mes "[Agent]";
			mes "Heh...";
			mes "What's your mission?!";
			next;
			select("Bring monster masks!");
			mes "[Agent]";
			mes "Groove.";
			next;
			mes "[Agent]";
			mes "Alright, bring me";
			mes "^FF00002 scary lookin'";
			mes "monster masks^000000.";
			mes "Just 2, and make";
			mes "sure they're scary.";
			set rumour_nd,19;
			close;
		case 2:
			mes "[Agent]";
			mes "What do you think";
			mes "you're doin'? I'm";
			mes "pretty insulted, seeing";
			mes "as you're all defiant like";
			mes "this. How do you expect";
			mes "to become a good Rogue?!";
			close;
		}
	}
	else if (rumour_nd == 19) {
		mes "[Agent]";
		mes "So... You bring";
		mes "the masks like";
		mes "a good little soldier?";
		next;
		switch(select("Uh huh.:Yes, Sir!")) {
		case 1:
			mes "[Agent]";
			mes "Hey, remember our";
			mes "little rule? I even";
			mes "clued you off when";
			mes "I said ''soldier!'' Heh~";
			close;
		case 2:
			mes "[Agent]";
			mes "Okay, let me check.";
			next;
			if ((countitem(2278) > 0) || (countitem(2281) > 0) || (countitem(5043) > 0) || (countitem(2288) > 0) || 
			(countitem(2292) > 0) || (countitem(2297) > 0) || (countitem(5005) > 0) || (countitem(5086) > 0) || 
			(countitem(5087) > 0) || (countitem(5088) > 0) || (countitem(5089) > 0) || (countitem(5090) > 0) || 
			(countitem(5176) > 0) || (countitem(5203) > 0) || (countitem(5098) > 0) || (countitem(5121) > 0) || 
			(countitem(5130) > 0) || (countitem(5177) > 0) || (countitem(5169) > 0) || (countitem(5143) > 0)) {
				mes "[Agent]";
				mes "Excellent!";
				next;
				mes "[Agent]";
				mes "Look, I know I gave you";
				mes "a hard time. And don't worry,";
				mes "I'm not gonna take your masks.";
				mes "I just wanted to train you";
				mes "to be the best Rogue that";
				mes "you can be. Understand?";
				next;
				mes "[Agent]";
				mes "Eh? So what's this";
				mes "note you keep carrying";
				mes "around in your hand?";
				next;
				mes "[Agent]";
				mes "Hmmm... Mmmhmmm...";
				mes "Hmmm... Mmmhmmm...";
				mes "Hmmm... Mmmhmmm... Oh!";
				next;
				mes "[Agent]";
				mes "Aw, nuts! Why didn't";
				mes "you say something?";
				mes "I thought you were";
				mes "here to take the Job";
				mes "change test for Rogues!";
				emotion ET_HUK;
				next;
				emotion ET_SORRY;
				mes "[Agent]";
				mes "Aw man, you came all this";
				mes "way to help me out, and";
				mes "I thought I--I'm really,";
				mes "really sorry! It's just";
				mes "that I got you mixed up";
				mes "with a trainee I'm expecting...";
				next;
				mes "[Agent]";
				mes "Do me a favor, and";
				mes "not tell anyone, yeah?";
				mes "Consider this... I dunno,";
				mes "an insider look into the";
				mes "Rogue world if you would.";
				emotion ET_CRY;
				next;
				switch(select("Yes, sir!:I won't tell anyone.")) {
				case 1:
					mes "[Agent]";
					mes "Whoa! Whoa!";
					mes "You can knock";
					mes "that off now.";
					mes "Thanks, I really";
					mes "appreciate it.";
					next;
					break;
				case 2:
					mes "[Agent]";
					mes "Thanks. I knew I could";
					mes "trust you somehow!";
					next;
					break;
				}
				mes "[Agent]";
				mes "Anyway, you've got one";
				mes "last place to go, Morocc.";
				mes "Check in with the Rogue";
				mes "agent over there, will you?";
				mes "Oh, and take this food";
				mes "with my apologies, 'kay?";
				set .@sorry_item,rand(1,6);
				if (.@sorry_item == 1) {
					getitem 12044,1; //Str_Dish04
				}
				else if (.@sorry_item == 2) {
					getitem 12059,1; //Agi_Dish04
				}
				else if (.@sorry_item == 3) {
					getitem 12064,1; //Dex_Dish04
				}
				else if (.@sorry_item == 4) {
					getitem 12049,1; //Int_Dish04
				}
				else if (.@sorry_item == 5) {
					getitem 12054,1; //Vit_Dish04
				}
				else if (.@sorry_item == 6) {
					getitem 12069,1; //Luk_Dish04
				}
				else {
					mes "[Agent]";
					mes "Wait, something";
					mes "isn't quite right. You";
					mes "mind coming back";
					mes "a little later?";
					close;
				}
				next;
				mes "[Agent]";
				mes "Hey, be safe on your";
				mes "way to Morocc, alright?";
				mes "Good luck finding that";
				mes "Rogue agent over there.";
				mes "Oh, and mum's the word";
				mes "on my little mistake~";
				set rumour_nd,20;
				close;
			}
			else {
				mes "[Agent]";
				mes "Hey, didja even bother";
				mes "to bring the masks?";
				mes "Go back and find them!";
				close;
			}
		}
	}
	else if (rumour_nd == 20) {
		mes "[Agent]";
		mes "Oh, hey, you're back?";
		mes "Weren't you already on";
		mes "your way to Morocc?";
		close;
	}
	else if (rumour_nd == 22) {
		mes "[Agent]";
		mes "Heh, looks like we";
		mes "solved this case~";
		next;
		if (zdan_edq > 12) {
			mes "[Agent]";
			mes "Thanks to you,";
			mes "we don't have to";
			mes "worry about that";
			mes "crummy ol' Z Gang.";
			close;
		}
		else {
			mes "[Agent]";
			mes "Until the world is";
			mes "free of evil and all";
			mes "that, the Rogues'll";
			mes "always have work to do.";
			close;
		}
	}
	else {
		mes "[Agent]";
		mes "Why are you still";
		mes "sticking around?";
		mes "Don't you have";
		mes "something more";
		mes "important to do?";
		close;
	}
}

morocc,116,39,3	script	Rogue Guild Agent#nd4	779,{
	if (rumour_nd == 1) {
		mes "[Agent]";
		mes "A case? What are you...?";
		mes "Ask somebody else. I don't";
		mes "know what you're saying.";
		mes "Leave me alone, and";
		mes "just let me relax~";
		close;
	}
	else if ((rumour_nd > 1) && (rumour_nd < 20)) {
		mes "[Agent]";
		mes "Ahhh, what'd I do";
		mes "for a nice jug of";
		mes "frosty beer now~";
		mes "...What?";
		close;
	}
	else if (rumour_nd == 20) {
		mes "[Agent]";
		mes "Oh, yeah, I'm almost";
		mes "done with my investigation.";
		next;
		if (zdan_edq > 12) {
			mes "[Agent]";
			mes "The Z Gang has been";
			mes "causing trouble everywhere.";
			mes "It's just mischief, but...";
			mes "If I got my hands on them...";
			specialeffect EF_BEGINSPELL;
			next;
			mes "[Agent]";
			mes "Anyway, that gang is";
			mes "drawing a lot of flak";
			mes "to Morocc. I mean, they";
			mes "kind of live here, so...";
			next;
			mes "[Agent]";
			mes "God, I want a drink.";
			mes "Listen, would you just";
			mes "go report to the Rogue";
			mes "Guild for me? I know it's";
			mes "my responsibility, but...";
			mes "It's something for you to do.";
			next;
			mes "[Agent]";
			mes "Heh! Keep up";
			mes "the good work~";
			set rumour_nd,21;
			close;
		}
		else {
			mes "[Agent]";
			mes "I don't know who these";
			mes "pranksters are, but once";
			mes "I get my hands on them...";
			mes "I'm gonna--! I'm gonna--!";
			specialeffect EF_BEGINSPELL;
			next;
			mes "[Agent]";
			mes "God, I want a drink.";
			mes "Listen, would you just";
			mes "go report to the Rogue";
			mes "Guild for me? I know it's";
			mes "my responsibility, but...";
			mes "It's something for you to do.";
			next;
			mes "[Agent]";
			mes "Heh! Keep up";
			mes "the good work~";
			set rumour_nd,21;
			close;
		}
	}
	else if (rumour_nd == 21) {
		mes "[Agent]";
		mes "Hurry up, and report";
		mes "for me to the Rogue";
		mes "Guild. While you do that,";
		mes "I think I'll enjoy a drink~";
		close;
	}
	else {
		mes "[Agent]";
		mes "*Hiccup*";
		mes "Ahahaha,";
		mes "this feels good!";
		mes "Good ol' beeeer~";
		next;
		mes "[Agent]";
		mes "*Hiccup*";
		next;
		mes "[Agent]";
		mes "What are you?";
		mes "Get lost!";
		close;
	}
}
