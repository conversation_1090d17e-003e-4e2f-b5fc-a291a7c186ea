//===== rAthena Script ============================================
//= Quest Warper Script
//===== By: =======================================================
//= DZeroX, Darkchild, Neouni
//===== Current Version: ==========================================
//= 2.3
//===== Compatible With: ==========================================
//= rAthena Project
//===== Description: ==============================================
//= Warper that works only after locations are unlocked.
//===== Additional Comments: ======================================
//= 1.0 - NPCs created
//= 1.1 - Add Dungeons by sturm
//= 1.2 - Add All char in account unlocked by ace_killer
//= 1.3 - Add new 7 towns and 7 dungeons by escoteiro
// - Correct bug jawaii town by escoteiro
// - Remove some excessive warpras by escoteiro
//= 1.4 - Rewrite of the Warpa system [Neouni]
// - Corrected some bugs caused by autoconverting the old script
//= 1.4a - Rewrite of menu to be custom for each player [Neouni]
// - Dungeon listing rewrite
// - Town listing rewrite
// - GameMaster can Customise Main menu
//= 1.4b - Pricing round [Neouni]
// - GM menu added for pricing
// - Town Warp pricing tags added
// - Dungeon Warp pricing tags added
//= 1.5 - storage functions pricing [Neouni]
// - storage pricing added
// - kafra points setting added
// - kafra storage code security added
// - Healing scripts pricing added
// - Heal Part script added
//= 1.6 - Dungeon warp [Neouni] (beta only)
// - added option to add a extra fee for going down deeper into dungeon
// partly by rebuilding the dungeon warping into variable menu's
//= 1.6a - a lot of fixes for beta release [Neouni] (public release)
// - fixed syntax problems
// - fixed missing pyramid gats in @DGat$ array
// - removed the culver level 5 that didn't exist
//= 1.7 - Readability of Dungeon menu building increased [Neouni]
// - Thanatos Tower & Luoyang Dungeon added
//= 1.8 - changed around unlock variables [Neouni]
// - Moved warp variables to mark what you have unlocked to an binary array in login based permanent variable
// (warp variables are saved in login database, so all charservers have these unlocked)
// - Town Warp unlock array max 21/(unknown max) items used instead of max account #variables 32 !
// - Dungeon Warp unlock array 28/(unknown max) items used instead of max account #variables 32 !
// - (max login ##variables = 16, i used 3)
// - old variables are cleared on next save
// - Extra Variable clear added for every character, just in case (request by Terces)
//= 1.8a - Show amount of users on map [Neouni]
//= 1.8b - Small typo fixed in stampcard script [Neouni]
//= 1.9 - Making it more edit friendly [Neouni]
// - Made Dungeon warp arrays more readable & editable (QWS_Darray)
// - Made town warp arrays more readable & editable (QWS_Tarray)
//= 2.0 - Special Warp menu added [Neouni]
// - when all towns and dungeons are collected a new option on the main menu will show
// - it will only show when you setup the mapname of the warp !
//= 2.0a - Dungeon Level Limit & Split dungeon fees
// - Limits dungeon based on Depth, access special setup menu thru GM-Menu
// - Dungeon fees split up based on  Basic , Advanced & Overseas
// - Old Dungeon fee system removed, all dungeon fees now set to 0
//= 2.0b - Special warpname menu option name bug fixed
//= 2.1 - Updated names to fall within retrictions. [L0ne_W0lf]
//= 2.2 - #kafra_code is now stored as is. [brianluau]
//= 2.3 - Added new town maps, cleanup, and removed pre-1.8 backwards fix. [Euphy]
//=================================================================

//========================Function=&=Script========================

function	script	Q_Warpra	{
	mes "[Warpra]";
	mes "Hello,";
	mes "I can warp you to any town or dungeon, but you need to unlock them first by visiting us.";
	mes "What do you need?";
	
	if(getarg(0) == 0) callfunc "QWS_MMarray",0;
	if(getarg(0) == 1) callfunc "QWS_MMarray",1;
	
	set @MMenu,select(@Mmenulist$[0],@Mmenulist$[1],@Mmenulist$[2],@Mmenulist$[3],@Mmenulist$[4],@Mmenulist$[5],@Mmenulist$[6],@Mmenulist$[7],@Mmenulist$[8],@Mmenulist$[9],@Mmenulist$[10],@Mmenulist$[11]);

	switch(@Mmenuref[@MMenu-1]+1){
	case 1: goto GM_Menu;
	case 2:
		warp $QW_SP_WarpMap$, $QW_SP_WarpX, $QW_SP_WarpY;
		close2;
		debugmes "Please check your special warp menu settings on the Warpra.";
		end;
	case 3: goto L_town;
	case 4: goto L_dungeon;
	case 5: goto L_FewWarps;
	case 6: goto L_NoUnlock;
	case 7: goto L_heal_Full;
	case 8: goto L_heal_Part;
	case 9: goto L_Storage;
	case 10: goto L_GStorage;
	case 11:
	default:
		goto L_end;
	}

//=====================GM-Menu=Functions===========================

GM_Menu:
	next;
	mes "Town warping = "+(($QW_TW_OFF)?"^FF0000 Off":"^00FF00 On")+"^000000";
	mes "Dungeon warping = "+(($QW_DW_OFF)?"^FF0000 Off":"^00FF00 On")+"^000000";
	mes "Dungeon Depth limit is "+(($QW_DL)?"^00FF00 On":"^FF0000 Off")+"^000000";
	mes "If on Dungeon Depth limit is set to ^0000FF"+$QW_DDL+"^000000";
	mes "Show Map Users = "+(($QW_MapUserShow)?"^00FF00 On":"^FF0000 Off")+"^000000";

	if ($QW_HF) mes "Healing full = ^00FF00 On ^000000";
	if ($QW_HP) mes "Healing partly = ^00FF00 On ^000000";
	if (!$QW_HF && !$QW_HP) mes "Healing = ^FF0000 Off ^000000";

	mes "Storage = "+(($QW_Stor)?"^00FF00 On":"^FF0000 Off")+"^000000";
	mes "Guild Storage = "+(($QW_GStor)?"^00FF00 On":"^FF0000 Off")+"^000000";
	mes "Kafra points collect = "+(($QW_KPoint)?"^00FF00 On":"^FF0000 Off")+"^000000";

	switch(select("Town Warping","Dungeon Warping","Dungeon Level Limit","Show Map Users","Healing full","Healing partly","Storage","Guild Storage","Kafra points collect","Set Prices","Special Warp","Exit")) {
	case 1: set $QW_TW_OFF,($QW_TW_OFF)?0:1; goto GM_Menu;
	case 2: set $QW_DW_OFF,($QW_DW_OFF)?0:1; goto GM_Menu;
	case 3: goto DungeonLevelLimit;
	case 4: set $QW_MapUserShow,($QW_MapUserShow)?0:1; goto GM_Menu;
	case 5:
		if ($QW_HF == 0) {
			set $QW_HF,1;
			set $QW_HP,0;
		}
		else set $QW_HF,0;
		goto GM_Menu;
	case 6:
		if ($QW_HP == 0) {
			set $QW_HP,1;
			set $QW_HF,0;
		}
		else set $QW_HP,0;
		goto GM_Menu;
	case 7: set $QW_Stor,($QW_Stor)?0:1; goto GM_Menu;
	case 8: set $QW_GStor,($QW_GStor)?0:1; goto GM_Menu;
	case 9: set $QW_KPoint,($QW_KPoint)?0:1; goto GM_Menu;
	case 10: goto Setprice;
	case 11: goto SpecialWarpMenu;
	default:
		close;
		end;
	}

//======================GM-Menu=Pricing============================

Setprice:
	if ($QW_DW_FEE != 0) set $QW_DW_FEE,0;
	next;
	mes "Scroll through the list to see all the options.";

	mes "Basic - Warps are = "+(($QW_BW_PRICE)?"^00FF00"+$QW_BW_PRICE:"^FF0000Free")+"^000000";
	mes "Advanced - Warps are = "+(($QW_AW_PRICE)?"^00FF00"+$QW_AW_PRICE:"^FF0000Free")+"^000000";
	mes "Overseas - Warps are = "+(($QW_OW_PRICE)?"^00FF00"+$QW_OW_PRICE:"^FF0000Free")+"^000000";

	mes "Basic Dungeon level warp fee = "+(($QW_BW_FEE)?"^00FF00"+$QW_BW_FEE:"^FF0000NO")+"^000000 zeny per level";
	mes "Advanced Dungeon level warp fee = "+(($QW_AW_FEE)?"^00FF00"+$QW_AW_FEE:"^FF0000NO")+"^000000 zeny per level";
	mes "Overseas Dungeon level warp fee = "+(($QW_OW_FEE)?"^00FF00"+$QW_OW_FEE:"^FF0000NO")+"^000000 zeny per level";

	mes "Full Healing = "+(($QW_HF_PRICE)?"^00FF00"+$QW_HF_PRICE:"^FF0000Free")+"^000000";
	mes "^FF0000HP ^000000Part Healing = "+(($QW_HP_H_PRICE)?"^00FF00"+$QW_HP_H_PRICE+"^000000 a point":"^FF0000Free^000000");
	mes "^FF0000SP ^000000Part Healing = "+(($QW_HP_S_PRICE)?"^00FF00"+$QW_HP_S_PRICE+"^000000 a point":"^FF0000Free^000000");

	if ($QW_S_PRICE && $QW_S_PRICE != 60) mes "Storage = ^00FF00 "+$QW_S_PRICE+" ^000000";
	if (!$QW_S_PRICE) mes "Storage = ^FF0000 Free ^000000";
	if ($QW_S_PRICE == 60) mes "Storage = ^0000FF Kafra Mode ^000000";
	mes "Guild Storage = "+(($QW_GS_PRICE)?"^00FF00"+$QW_GS_PRICE:"^FF0000Free")+"^000000";
	
	switch(select("Basic - Warps","Advanced - Warps","Overseas - Warps","Basic Dungeon warp fee","Advanced Dungeon warp fee","Overseas Dungeon warp fee","Full Healing","Part Healing","Storage","Guild Storage","Back","Exit")) {
	case 1:
		next;
		mes "Basic - Warps are = "+(($QW_BW_PRICE)?"^00FF00"+$QW_BW_PRICE:"^FF0000Free")+"^000000";
		mes "Basic - Warps are starter towns and related dungeons.";
		input $QW_BW_PRICE;
		goto Setprice;
	case 2:
		next;
		mes "Advanced - Warps are = "+(($QW_AW_PRICE)?"^00FF00"+$QW_AW_PRICE:"^FF0000Free")+"^000000";
		mes "Advanced - Warps are towns and dungeons on the same island but not close to any starter town.";
		input $QW_AW_PRICE;
		goto Setprice;
	case 3:
		next;
		mes "Overseas - Warps are = "+(($QW_OW_PRICE)?"^00FF00"+$QW_OW_PRICE:"^FF0000Free")+"^000000";
		mes "Overseas - Warps are towns and dungeons overseas reachable by boat from Alberta.";
		input $QW_OW_PRICE;
		goto Setprice;
	case 4:
		next;
		mes "Basic Dungeon level warp fee = "+(($QW_BW_FEE)?"^00FF00"+$QW_BW_FEE:"^FF0000NO")+"^000000 zeny per level";
		mes "Basic - Warps are starter town related dungeons.";
		mes "Dungeon warp fees are calculated by maps away from entrance of the dungeon times Dungeon warp fee.";
		mes "These costs are on top of the regular Warp costs.";
		input $QW_BW_FEE;
		goto Setprice;
	case 5:
		next;
		mes "Advanced Dungeon level warp fee = "+(($QW_AW_FEE)?"^00FF00"+$QW_AW_FEE:"^FF0000NO")+"^000000 zeny per level";
		mes "Advanced - Warps are dungeons not close to any starter town.";
		mes "Dungeon warp fees are calculated by maps away from entrance of the dungeon times Dungeon warp fee.";
		mes "These costs are on top of the regular Warp costs.";
		input $QW_AW_FEE;
		goto Setprice;
	case 6:
		next;
		mes "Overseas Dungeon level warp fee = "+(($QW_OW_FEE)?"^00FF00"+$QW_OW_FEE:"^FF0000NO")+"^000000 zeny per level";
		mes "Overseas - Warps are dungeons related to towns overseas reachable by boat from Alberta.";
		mes "Dungeon warp fees are calculated by maps away from entrance of the dungeon times Dungeon warp fee.";
		mes "These costs are on top of the regular Warp costs.";
		input $QW_OW_FEE;
		goto Setprice;
	case 7:
		next;
		mes "Full Healing = "+(($QW_HF_PRICE)?"^00FF00"+$QW_HF_PRICE:"^FF0000Free")+"^000000";
		mes "Instant full healing 1 price.";
		input $QW_HF_PRICE;
		goto Setprice;
	case 8:
		next;
		mes "^FF0000HP ^000000Part Healing = "+(($QW_HP_H_PRICE)?"^00FF00"+$QW_HP_H_PRICE+"^000000 a point":"^FF0000Free^000000");
		mes "^FF0000SP ^000000Part Healing = "+(($QW_HP_S_PRICE)?"^00FF00"+$QW_HP_S_PRICE+"^000000 a point":"^FF0000Free^000000");
		mes "Healing price per 1 HP.";
		mes "Healing price per 1 SP.";
		mes "2 inputs, first HP then SP.";
		input $QW_HP_H_PRICE;
		input $QW_HP_S_PRICE;
		goto Setprice;
	case 9:
		next;
		if ($QW_S_PRICE && $QW_S_PRICE != 60) mes "Storage = ^00FF00 "+$QW_S_PRICE+" ^000000";
		if (!$QW_S_PRICE) mes "Storage = ^FF0000 Free ^000000";
		if ($QW_S_PRICE == 60) mes "Storage = ^0000FF Kafra Mode ^000000";
		mes "Storage cost, if set to 60 Kafra pricing will be handled.";
		input $QW_S_PRICE;
		goto Setprice;
	case 10:
		next;
		mes "Guild Storage = "+(($QW_GS_PRICE)?"^00FF00"+$QW_GS_PRICE:"^FF0000Free")+"^000000";
		mes "Guild Storage, free on Guild Kafras.";
		input $QW_GS_PRICE;
		goto Setprice;
	case 11:
		goto GM_Menu;
	default:
		close;
		end;
	}

//======================Special=Warp=Menu==========================

SpecialWarpMenu:
	if ($QW_SP_Warpname$ == "") set $QW_SP_Warpname$,"Special Warp";
	next;
	mes "Scroll down to see all the information";
	mes "The Special warp menu option will show in the main menu when all towns & dungeons are unlocked";
	mes "And when the map for special warping has been set";
	mes "menu option name set to ^0000FF"+$QW_SP_Warpname$+"^000000";
	if ($QW_SP_WarpMap$ == "") mes "map is currently ^FF0000not^000000 set, and Special warp menu is off";
	if ($QW_SP_WarpMap$ != "") mes "map is currently set to ^0000FF"+$QW_SP_WarpMap$+"^000000 and Special Warp menu is on";
	mes "coords are set to ^0000FF"+$QW_SP_WarpX+","+$QW_SP_WarpY+"^000000";
	switch(select("Set Special Warp name to show in menu", "Set WarpMap","Set Coords","Go Back to GM_Menu","Exit")){
	case 1:
		next;
		mes "Set the name to show in the menu as option.";
		input $QW_SP_Warpname$;
		goto SpecialWarpMenu;
	case 2:
		next;
		mes "Set the map in the ^0000FFmapname^000000 format.";
		mes "When this warpmap is set the option for players will show once they meet the requirements.";
		mes "To disable Special Warp Menu option, clear this!";
		input $QW_SP_WarpMap$;
		goto SpecialWarpMenu;
	case 3:
		next;
		mes "First input = Xcoord";
		mes "Second input = Ycoord";
		input $QW_SP_WarpX;
		input $QW_SP_WarpY;
		goto SpecialWarpMenu;
	case 4:
		goto GM_Menu;
	default:
		close;
		end;
	}

//======================Dungeon=Level=Limit========================

DungeonLevelLimit:
	next;
	mes "Dungeon Depth limit is "+(($QW_DL)?"^00FF00On":"^FF0000Off")+"^000000";
	mes "If^00FF00 On ^000000Dungeon Depth limit is set to ^0000FF"+$QW_DDL+"^000000";

	switch(select("Toggle Dungeon Depth Limit","Set Dungeon Depth Limit","Go Back to GM_Menu","Exit")){
	case 1: set $QW_DL,($QW_DL)?0:1; goto DungeonLevelLimit;
	case 2:
		next;
		mes "Set limit of Dungeon Depth 0 = entrance";
		mes "Depth 1 is a map connected to 0 and so on";
		mes "Shortest Route to map counts as depth";
		input $QW_DDL;
		goto DungeonLevelLimit;
	case 3: goto GM_Menu;
	default:
		close;
		end;
	}

//===========================Towns=================================

L_town:
	callfunc "QWS_Tarray";
	freeloop(1);
	set .@menu$,"";
	for(set .@i,0; .@i<35; set .@i,.@i+1)	// Expected maximum is 35
		set .@menu$,.@menu$+@Tmenulist$[.@i]+":";
	freeloop(0);
	set @TWMenu,select(.@menu$);

	if (@Tmenuref[@TWMenu-1] == 57005) goto L_end; // 57005='dead' in hex

	if(Zeny<@pTprice[@Tmenuref[@TWMenu-1]]) callsub L_Short_on_zeny,0;
	set Zeny, Zeny-@pTprice[@Tmenuref[@TWMenu-1]];
	if ($QW_KPoint == 1) set RESRVPTS, RESRVPTS + (@pTprice[@Tmenuref[@TWMenu-1]]/16);
	warp @pTmap$[@Tmenuref[@TWMenu-1]],@pTXcoords[@Tmenuref[@TWMenu-1]],@pTYcoords[@Tmenuref[@TWMenu-1]];
	close2;
	set Zeny, Zeny+@pTprice[@Tmenuref[@TWMenu-1]];
	end;

//=========================Dungeons================================

L_dungeon:
	callfunc "QWS_Darray";
	freeloop(1);
	set .@menu$,"";
	for(set .@i,0; .@i<35; set .@i,.@i+1)	// Expected maximum is 35
		set .@menu$,.@menu$+@Dmenulist$[.@i]+":";
	freeloop(0);
	set @DWMenu,select(.@menu$);

	if (@Dmenuref[@DWMenu-1] == 57005) goto L_end; // 57005='dead' in hex
	set @DwarpMenu, (@Dmenuref[@DWMenu-1]);
	callfunc "QWS_DLarray";

	next;
	mes "[Warpra]";
	mes "Please select where you want to go:";
	freeloop(1);
	set .@menu$,"";
	for(set .@i,0; .@i<18; set .@i,.@i+1)	// Expected maximum is 18
		set .@menu$,.@menu$+@DWLmenulist$[.@i]+":";
	freeloop(0);
	set @DWLMenu,select(.@menu$);

	if (@DWLmenuref[@DWLMenu-1] == 57005) goto L_end; // 57005='dead' in hex

	set @Darrayref, @DWLmenuref[@DWLMenu-1];
	set @warpprice, @pDprice[@Dmenuref[@DWMenu-1]]+(getd(@pDfee$[@Dmenuref[@DWMenu-1]])*(@DDepth[@Darrayref]));

	if(Zeny<@warpprice) callsub L_Short_on_zeny,1;
	set Zeny, Zeny-(@warpprice);
	if ($QW_KPoint == 1) set RESRVPTS, RESRVPTS + (@warpprice/16);
	warp @DGat$[@Darrayref],@DXcoords[@Darrayref],@DYcoords[@Darrayref];
	close2;
	set Zeny, Zeny+@pTprice[@Tmenuref[@DWMenu-1]];
	end;

//=============================Healing=============================

L_heal_Full:
	set @healfee, $QW_HF_PRICE;
	if(Zeny<@healfee) callsub L_Short_on_zeny,4;
	set Zeny, Zeny-@healfee;
	if ($QW_KPoint == 1) set RESRVPTS, RESRVPTS + (@healfee/500);
	next;
	mes "[Warpra]";
	mes "Close this window and I will heal you.";
	close2;
	percentheal 100,100;
	end;

L_heal_Part:
	set @healchoice,select("Full heal","Health Points only","Skill Points only","Exit");
	if (@healchoice == 1) callsub PHeal,1,1;
	if (@healchoice == 2) callsub PHeal,1,0;
	if (@healchoice == 3) callsub PHeal,0,1;
	goto L_end;
	
PHeal:
	next;
	set @Hp, MaxHp-Hp;
	set @Sp, MaxSp-Sp;
	set @HpPrice, @hp*$QW_HP_H_PRICE;
	set @SpPrice, @sp*$QW_HP_S_PRICE;
	mes "[Warpra]";
	if(getarg(0) == 1) mes ""+@HpPrice+" Zeny for "+@Hp+" health points";
	if(getarg(1) == 1) mes ""+@SpPrice+" Zeny for "+@Sp+" skill points";
	set @total, @HpPrice+@SpPrice;
	mes "for a total of "+@total+" zeny";
    	if (select("Heal me","Let me see the choices again")==2) goto L_heal_Part;
    	
    	if(getarg(0) == 1)set @HpPrice, (MaxHp-Hp)*$QW_HP_H_PRICE;
	if(getarg(1) == 1)set @SpPrice, (MaxSp-Sp)*$QW_HP_S_PRICE;
	set @healfee, @HpPrice+@SpPrice;
	if (getarg(0) == 1)&&(getarg(1) == 1)&&(Zeny<@healfee) goto Zeny_Short_Both;
	if (getarg(0) == 1)&&(Zeny<@healfee) goto Zeny_short_HP;
	if (getarg(1) == 1)&&(Zeny<@healfee) goto Zeny_short_SP;
	set Zeny, Zeny-@healfee;
	if (getarg(0) == 1)&&(getarg(1) == 1) percentheal 100,100;
	if (getarg(0) == 1) percentheal 100,0;
	if (getarg(1) == 1) percentheal 0,100;
	close;
	end;

Zeny_Short_Both:
	mes "[Warpra]";
	mes "Choose another option, you can afford both.";
	mes "I can heal as much as you can afford, too.";
	if (select("OK","Exit") == 2) goto L_end;
	goto PHeal;

Zeny_short_HP:
	mes "[Warpra]";
	mes "Do you want me to partly heal your HP?";
	if (select("Yes","No") == 2) goto L_end;
	set @Hp, Zeny/$QW_HP_H_PRICE;
    	set @HpPrice, @Hp*$QW_HP_H_PRICE;
    	if (@Hp == 1) mes "You're not worth the effort.";
    	if (@Hp == 1) goto L_end;
    	set Zeny, Zeny-@HpPrice;
	heal @Hp,0;
	close;
	end;

Zeny_short_SP:
	mes "[Warpra]";
	mes "Do you want me to partly heal your SP?";
	if (select("Yes","No") == 2) goto L_end;
	set @Sp, Zeny/$QW_HP_S_PRICE;
    	set @SpPrice, @Sp*$QW_HP_S_PRICE;
    	if (@Sp == 1) mes "You're not worth the effort.";
    	if (@Sp == 1) goto L_end;
    	set Zeny, Zeny-@SpPrice;
	heal 0,@Sp;
	close;
	end;

//=============================Storage=============================

L_Storage:
	next;
	if(!callfunc("F_CanOpenStorage")) goto L_StorageJBlow;
	set @fee, $QW_S_PRICE;
	if ($QW_S_PRICE == 60)&&(BaseJob == Job_Novice) set @fee, 30;
	if ($QW_S_PRICE == 60)&&(BaseJob != Job_Novice) set @fee, 60;
	if(Zeny<@fee) callsub L_Short_on_zeny,2;
	set Zeny, Zeny-@fee;
	if ($QW_KPoint == 1) set RESRVPTS, RESRVPTS + (@fee/5);

	mes "[Warpra]";
	if(#kafra_code) {
		mes "Enter your storage password:";
		set @code_,0;
		input @code_;
		if(@code_ != #kafra_code) {
			dispbottom "Wrong storage password.";
			close;
		}
		set @kafcode_try,0;
		set @code_,0;
	}
	mes "Close this window and I will open your storage.";
	close2;
	openstorage;
	end;

L_StorageJBlow:
	mes "[Warpra]";
	mes "I am sorry but you have to be at least Novice level 6 if you want to use the storage.";
	return;

L_GStorage:
	if (!@GID) {
		next;
		mes "[Warpra]";
		mes "You are not a part of a guild I can't help you.";
		close;
		end;
	}
	if (Zeny<$QW_GS_PRICE) callsub L_Short_on_zeny,3;
	set Zeny, Zeny-$QW_GS_PRICE;
	if ($QW_KPoint == 1) set RESRVPTS, RESRVPTS + ($QW_GS_PRICE/5);
	next;
	mes "[Warpra]";
	mes "Close this window and I will open the ^5533FF" + GetGuildName(@GID) + "^000000 storage.";
	close2;
	guildopenstorage;
	end;
	
L_end:
	close;
	end;

//============================Few=Warp=============================
L_FewWarps:
	next;
	mes "[Warpra]";
	mes "You need to unlock locations before they come available to you.";
	mes "To unlock a location talk to my colleagues all over the world.";
	mes "Each account has its own stamp card.";
	mes "Want me to check what stamps you have collected so far?";
	if (select("Yes","No")==1) callsub stampcard;
	close;
	end;

//============================No=Unlock============================
L_NoUnlock:
	next;
	mes "[Warpra]";
	mes "I don't unlock this location, my assistant deeper in the dungeon will unlock this place.";
	close;
	end;
	
//=========================Short=On=Zeny===========================
L_Short_on_zeny:
	next;
	switch(getarg(0)) {
	case 0: mes "You don't seem to have "+@pTprice[@Tmenuref[@TWMenu-1]]+" zeny to pay for the warp to "+@pTmenuitems$[@Tmenuref[@TWMenu-1]]+"."; break;
	case 1: mes "You don't seem to have "+@warpprice+" zeny to pay for the warp to "+@DLevelName$[@DWLmenuref[@DWLMenu-1]]+" at "+@pDmenuitems$[@Dmenuref[@DWMenu-1]]+"."; break;
	case 2: mes "You don't seem to have "+@fee+" zeny to pay the storage fee."; break;
	case 3: mes "You don't seem to have "+$QW_GS_PRICE+" zeny to pay the guild storage fee."; break;
	case 4: mes "You don't seem to have "+@healfee+" zeny to pay for your healing."; break;
	}
	close;
	end;
	
//===========================Stamp=Card============================
stampcard:
// Counting of the amount of places you have unlocked
	next;
	mes "Let me check what Towns you have.";
	callfunc "QWS_TownStamps";
	mes "You collected^00FF00 "+@Tstamp+" of "+@MaxTstamp+" ^000000Towns.";
	if (@Tstamp == 15) {
		mes "They say there is an island you can only get to when married...";
		emotion ET_SMILE;
	}
	next;
	mes "Let me check what dungeons you have.";
	callfunc "QWS_DungeonStamps";
	mes "You collected^00FF00 "+@Dstamp+" of "+@MaxDstamp+" ^000000Dungeons.";
	mes "To unlock a dungeon, search for my colleagues.";
	mes "You can usually find them near the middle or end of the dungeon.";
	return;
}

function	script	QWS_TownStamps	{
	set @Tstamp,0;
	set @MaxTstamp,30; //maximum number of towns
	set @binvalue,1;
	set @Tstamploop,0;
	do {
		if ((@binvalue & ##QWS_T_Unlock) == @binvalue) set @Tstamp,@Tstamp+1;
		set @binvalue, @binvalue *2;
		set @Tstamploop, @Tstamploop + 1;
	} while (@Tstamploop < @MaxTstamp);
	return;
}

function	script	QWS_DungeonStamps	{
	set @Dstamp,0;
	set @MaxDstamp,29; //maximum number of dungeons
	set @binvalue,1;
	set @Dstamploop,0;
	do {
		if ((@binvalue & ##QWS_D_Unlock) == @binvalue) set @Dstamp,@Dstamp+1;
		set @binvalue, @binvalue *2;
		set @Dstamploop, @Dstamploop + 1;
	} while (@Dstamploop < @MaxDstamp);
	return;
}

//======================Main=Menu=Array============================

function	script	QWS_MMarray	{

	// Currently 9 items
	setarray @pMmenuitems$[0], "GameMaster Menu", $QW_SP_Warpname$, "Warp to Towns", "Warp to Dungeons", "Why so few warps?", "Why don't you unlock this location?", "Heal", "Heal", "Storage", "Guild Storage", "Cancel";
	set @Mi,0; // loop counter
	set @Mj,0; // menu line counter

//----------------GameMaster-Menu
	if (getgmlevel()>= 80) {
		set @Mmenulist$[@Mj],@pMmenuitems$[@Mi];
		set @Mmenuref[@Mj],@Mi;
		set @Mj,@Mj+1;
	}
	set @Mi,@Mi+1;
//----------------Special-Warp
	callfunc "QWS_TownStamps";
	callfunc "QWS_DungeonStamps";
	if (@Tstamp == @MaxTstamp)&&(@Dstamp == @MaxDstamp)&&($QW_SP_WarpMap$ != "") {
		set @Mmenulist$[@Mj],@pMmenuitems$[@Mi];
		set @Mmenuref[@Mj],@Mi;
		set @Mj,@Mj+1;
	}
	set @Mi,@Mi+1;
//----------------Town-Warp
	if ($QW_TW_OFF == 0) {
		set @Mmenulist$[@Mj],@pMmenuitems$[@Mi];
		set @Mmenuref[@Mj],@Mi;
		set @Mj,@Mj+1;
	}
	set @Mi,@Mi+1;
//----------------Dungeon-Warp
	if ($QW_DW_OFF == 0) {
		set @Mmenulist$[@Mj],@pMmenuitems$[@Mi];
		set @Mmenuref[@Mj],@Mi;
		set @Mj,@Mj+1;
	}
	set @Mi,@Mi+1;
//----------------Why-So-Few-Warps
	set @Mmenulist$[@Mj],@pMmenuitems$[@Mi];
	set @Mmenuref[@Mj],@Mi;
	set @Mj,@Mj+1;
	set @Mi,@Mi+1;
//----------------No-Unlock
	if (getarg(0) == 1) {
		set @Mmenulist$[@Mj],@pMmenuitems$[@Mi];
		set @Mmenuref[@Mj],@Mi;
		set @Mj,@Mj+1;
	}
	set @Mi,@Mi+1;
//----------------Healfull
	if ($QW_HF == 1) {
		set @Mmenulist$[@Mj],@pMmenuitems$[@Mi];
		set @Mmenuref[@Mj],@Mi;
		set @Mj,@Mj+1;
	}
	set @Mi,@Mi+1;
//----------------Healpart
	if ($QW_HP == 1) {
		set @Mmenulist$[@Mj],@pMmenuitems$[@Mi];
		set @Mmenuref[@Mj],@Mi;
		set @Mj,@Mj+1;
	}
	set @Mi,@Mi+1;
//----------------Storage
	if ($QW_Stor == 1) {
		set @Mmenulist$[@Mj],@pMmenuitems$[@Mi];
		set @Mmenuref[@Mj],@Mi;
		set @Mj,@Mj+1;
	}
	set @Mi,@Mi+1;
//----------------GuildStorage
	if ($QW_GStor == 1) {
		set @Mmenulist$[@Mj],@pMmenuitems$[@Mi];
		set @Mmenuref[@Mj],@Mi;
		set @Mj,@Mj+1;
	}
	set @Mi,@Mi+1;
//----------------Cancel
	set @Mmenulist$[@Mj],@pMmenuitems$[@Mi];
	set @Mmenuref[@Mj],@Mi;
	return;
}

//======================Town=Menu=Array============================
// Adding a town:
//	setarray @pTmenuitems$[@Ti], "Prontera";// Name of Town shown in Town select Menu
//	setarray @pTprice[@Ti], $QW_BW_PRICE;	// Warp prices: Basic ($QW_BW_PRICE), Advanced ($QW_AW_PRICE), Overseas ($QW_OW_PRICE)
//
//	setarray @pTmap$[@Ti], "prontera";	// Map name
//	setarray @pTXcoords[@Ti], 156;		// X warp coords
//	setarray @pTYcoords[@Ti], 187;		// Y warp coords
//
//	QWS_Make_Town_Menu 0;	// Use a free number (last used is 30, Eclage)
//
// Adding a warpra:
//	map,x,y,facing	script	Warpra#example	113,{
//		callfunc "QWS_Town_Warpra",<town number>,"Your Town";
//		close;
//	}
//	Town number is the same as "QWS_Make_Town_Menu".
//==================================================================

function	script	QWS_Tarray	{
	function QWS_Make_Town_Menu;

	set @Ti,0; // loop counter
	set @Tj,0; // menu lines counter

//----------------Prontera
	setarray @pTmenuitems$[@Ti], "Prontera";
	setarray @pTprice[@Ti], $QW_BW_PRICE;

	setarray @pTmap$[@Ti], "prontera";
	setarray @pTXcoords[@Ti], 156;
	setarray @pTYcoords[@Ti], 187;

	QWS_Make_Town_Menu 0;
//----------------Alberta
	setarray @pTmenuitems$[@Ti], "Alberta";
	setarray @pTprice[@Ti], $QW_BW_PRICE;

	setarray @pTmap$[@Ti], "alberta";
	setarray @pTXcoords[@Ti], 27;
	setarray @pTYcoords[@Ti], 236;

	QWS_Make_Town_Menu 1;
//----------------Aldebaran
	setarray @pTmenuitems$[@Ti], "Aldebaran";
	setarray @pTprice[@Ti], $QW_AW_PRICE;

	setarray @pTmap$[@Ti], "aldebaran";
	setarray @pTXcoords[@Ti], 145;
	setarray @pTYcoords[@Ti], 120;

	QWS_Make_Town_Menu 2;
//----------------Amatsu:
	setarray @pTmenuitems$[@Ti], "Amatsu";
	setarray @pTprice[@Ti], $QW_OW_PRICE;

	setarray @pTmap$[@Ti], "amatsu";
	setarray @pTXcoords[@Ti], 197;
	setarray @pTYcoords[@Ti], 86;

	QWS_Make_Town_Menu 3;
//----------------Ayothaya:
	setarray @pTmenuitems$[@Ti], "Ayothaya";
	setarray @pTprice[@Ti], $QW_OW_PRICE;

	setarray @pTmap$[@Ti], "ayothaya";
	setarray @pTXcoords[@Ti], 150;
	setarray @pTYcoords[@Ti], 57;

	QWS_Make_Town_Menu 4;
//----------------Brasilis:
	setarray @pTmenuitems$[@Ti], "Brasilis";
	setarray @pTprice[@Ti], $QW_OW_PRICE;

	setarray @pTmap$[@Ti], "brasilis";
	setarray @pTXcoords[@Ti], 195;
	setarray @pTYcoords[@Ti], 220;

	QWS_Make_Town_Menu 21;
//----------------Comodo:
	setarray @pTmenuitems$[@Ti], "Comodo";
	setarray @pTprice[@Ti], $QW_AW_PRICE;

	setarray @pTmap$[@Ti], "comodo";
	setarray @pTXcoords[@Ti], 188;
	setarray @pTYcoords[@Ti], 161;

	QWS_Make_Town_Menu 5;
//----------------Dewata:
	setarray @pTmenuitems$[@Ti], "Dewata";
	setarray @pTprice[@Ti], $QW_OW_PRICE;

	setarray @pTmap$[@Ti], "dewata";
	setarray @pTXcoords[@Ti], 199;
	setarray @pTYcoords[@Ti], 179;

	QWS_Make_Town_Menu 29;
//----------------Eclage:
	setarray @pTmenuitems$[@Ti], "Eclage";
	setarray @pTprice[@Ti], $QW_OW_PRICE;

	setarray @pTmap$[@Ti], "eclage";
	setarray @pTXcoords[@Ti], 111;
	setarray @pTYcoords[@Ti], 39;

	QWS_Make_Town_Menu 30;
//----------------Einbech:
	setarray @pTmenuitems$[@Ti], "Einbech";
	setarray @pTprice[@Ti], $QW_AW_PRICE;

	setarray @pTmap$[@Ti], "einbech";
	setarray @pTXcoords[@Ti], 172;
	setarray @pTYcoords[@Ti], 126;

	QWS_Make_Town_Menu 6;
//----------------Einbroch:
	setarray @pTmenuitems$[@Ti], "Einbroch";
	setarray @pTprice[@Ti], $QW_AW_PRICE;

	setarray @pTmap$[@Ti], "einbroch";
	setarray @pTXcoords[@Ti], 230;
	setarray @pTYcoords[@Ti], 191;

	QWS_Make_Town_Menu 7;
//----------------El Dicastes:
	setarray @pTmenuitems$[@Ti], "El Dicastes";
	setarray @pTprice[@Ti], $QW_OW_PRICE;

	setarray @pTmap$[@Ti], "dicastes01";
	setarray @pTXcoords[@Ti], 197;
	setarray @pTYcoords[@Ti], 187;

	QWS_Make_Town_Menu 22;
//----------------Geffen:
	setarray @pTmenuitems$[@Ti], "Geffen";
	setarray @pTprice[@Ti], $QW_BW_PRICE;

	setarray @pTmap$[@Ti], "geffen";
	setarray @pTXcoords[@Ti], 119;
	setarray @pTYcoords[@Ti], 66;

	QWS_Make_Town_Menu 8;
//----------------Kunlun:
	setarray @pTmenuitems$[@Ti], "Kunlun";
	setarray @pTprice[@Ti], $QW_OW_PRICE;

	setarray @pTmap$[@Ti], "gonryun";
	setarray @pTXcoords[@Ti], 150;
	setarray @pTYcoords[@Ti], 130;

	QWS_Make_Town_Menu 9;
//----------------Hugel:
	setarray @pTmenuitems$[@Ti], "Hugel";
	setarray @pTprice[@Ti], $QW_AW_PRICE;

	setarray @pTmap$[@Ti], "hugel";
	setarray @pTXcoords[@Ti], 95;
	setarray @pTYcoords[@Ti], 121;

	QWS_Make_Town_Menu 10;
//----------------Izlude:
	setarray @pTmenuitems$[@Ti], "Izlude";
	setarray @pTprice[@Ti], $QW_BW_PRICE;

	setarray @pTmap$[@Ti], "izlude";
	setarray @pTXcoords[@Ti], 128;
	setarray @pTYcoords[@Ti], 111;

	QWS_Make_Town_Menu 11;
//----------------Jawaii:
	setarray @pTmenuitems$[@Ti], "Jawaii";
	setarray @pTprice[@Ti], $QW_AW_PRICE;

	setarray @pTmap$[@Ti], "jawaii";
	setarray @pTXcoords[@Ti], 243;
	setarray @pTYcoords[@Ti], 115;

	QWS_Make_Town_Menu 12;
//----------------Lighthalzen:
	setarray @pTmenuitems$[@Ti], "Lighthalzen";
	setarray @pTprice[@Ti], $QW_AW_PRICE;

	setarray @pTmap$[@Ti], "lighthalzen";
	setarray @pTXcoords[@Ti], 158;
	setarray @pTYcoords[@Ti], 110;

	QWS_Make_Town_Menu 13;
//----------------Luoyang:
	setarray @pTmenuitems$[@Ti], "Luoyang";
	setarray @pTprice[@Ti], $QW_OW_PRICE;

	setarray @pTmap$[@Ti], "louyang";
	setarray @pTXcoords[@Ti], 210;
	setarray @pTYcoords[@Ti], 108;

	QWS_Make_Town_Menu 14;
//----------------Lutie
	setarray @pTmenuitems$[@Ti], "Lutie";
	setarray @pTprice[@Ti], $QW_AW_PRICE;

	setarray @pTmap$[@Ti], "xmas";
	setarray @pTXcoords[@Ti], 148;
	setarray @pTYcoords[@Ti], 131;

	QWS_Make_Town_Menu 15;
//----------------Manuk
	setarray @pTmenuitems$[@Ti], "Manuk";
	setarray @pTprice[@Ti], $QW_OW_PRICE;

	setarray @pTmap$[@Ti], "manuk";
	setarray @pTXcoords[@Ti], 260;
	setarray @pTYcoords[@Ti], 175;

	QWS_Make_Town_Menu 23;
//----------------Mora
	setarray @pTmenuitems$[@Ti], "Mora";
	setarray @pTprice[@Ti], $QW_OW_PRICE;

	setarray @pTmap$[@Ti], "mora";
	setarray @pTXcoords[@Ti], 111;
	setarray @pTYcoords[@Ti], 97;

	QWS_Make_Town_Menu 24;
//----------------Morocc:
	setarray @pTmenuitems$[@Ti], "Morocc";
	setarray @pTprice[@Ti], $QW_BW_PRICE;

	setarray @pTmap$[@Ti], "morocc";
	setarray @pTXcoords[@Ti], 159;
	setarray @pTYcoords[@Ti], 93;

	QWS_Make_Town_Menu 16;
//----------------Moscovia
	setarray @pTmenuitems$[@Ti], "Moscovia";
	setarray @pTprice[@Ti], $QW_OW_PRICE;

	setarray @pTmap$[@Ti], "moscovia";
	setarray @pTXcoords[@Ti], 219;
	setarray @pTYcoords[@Ti], 193;

	QWS_Make_Town_Menu 25;
//----------------Niflheim:
	setarray @pTmenuitems$[@Ti], "Niflheim";
	setarray @pTprice[@Ti], $QW_AW_PRICE;

	setarray @pTmap$[@Ti], "niflheim";
	setarray @pTXcoords[@Ti], 195;
	setarray @pTYcoords[@Ti], 186;

	QWS_Make_Town_Menu 17;
//----------------Payon:
	setarray @pTmenuitems$[@Ti], "Payon";
	setarray @pTprice[@Ti], $QW_BW_PRICE;

	setarray @pTmap$[@Ti], "payon";
	setarray @pTXcoords[@Ti], 152;
	setarray @pTYcoords[@Ti], 75;

	QWS_Make_Town_Menu 18;
//----------------Rachel
	setarray @pTmenuitems$[@Ti], "Rachel";
	setarray @pTprice[@Ti], $QW_AW_PRICE;

	setarray @pTmap$[@Ti], "rachel";
	setarray @pTXcoords[@Ti], 130;
	setarray @pTYcoords[@Ti], 111;

	QWS_Make_Town_Menu 26;
//----------------Splendide
	setarray @pTmenuitems$[@Ti], "Splendide";
	setarray @pTprice[@Ti], $QW_OW_PRICE;

	setarray @pTmap$[@Ti], "splendide";
	setarray @pTXcoords[@Ti], 200;
	setarray @pTYcoords[@Ti], 153;

	QWS_Make_Town_Menu 27;
//----------------Umbala:
	setarray @pTmenuitems$[@Ti], "Umbala";
	setarray @pTprice[@Ti], $QW_AW_PRICE;

	setarray @pTmap$[@Ti], "umbala";
	setarray @pTXcoords[@Ti], 130;
	setarray @pTYcoords[@Ti], 130;

	QWS_Make_Town_Menu 19;
//----------------Veins
	setarray @pTmenuitems$[@Ti], "Veins";
	setarray @pTprice[@Ti], $QW_OW_PRICE;

	setarray @pTmap$[@Ti], "veins";
	setarray @pTXcoords[@Ti], 216;
	setarray @pTYcoords[@Ti], 123;

	QWS_Make_Town_Menu 28;
//----------------Juno:
	setarray @pTmenuitems$[@Ti], "Juno";
	setarray @pTprice[@Ti], $QW_AW_PRICE;

	setarray @pTmap$[@Ti], "yuno";
	setarray @pTXcoords[@Ti], 160;
	setarray @pTYcoords[@Ti], 168;

	QWS_Make_Town_Menu 20;
//----------------Cancel
	setarray @pTmenuitems$[@Ti], "Cancel";
	setarray @pTprice[@Ti], 0;

	set @Tmenulist$[@Tj],@pTmenuitems$[@Ti];
	set @Tmenuref[@Tj],57005; // 57005='dead' in hex
	return;


//----------------Make Town Menu Function

function	QWS_Make_Town_Menu	{
	set @temptownmenubin,1;
	if (getarg(0) == 0) goto menu_item;
	set @templooptownmenu,0;
	do {
		set @temptownmenubin, @temptownmenubin * 2;
		set @templooptownmenu, @templooptownmenu + 1;
		}while (getarg(0) > @templooptownmenu);
// check marker and make menu item
menu_item:
	if ((@temptownmenubin & ##QWS_T_Unlock) != @temptownmenubin) {
		set @Ti,@Ti+1;
		return;
	}
	if (@pTprice[@Ti] != 0)&&($QW_MapUserShow == 0) set @Tmenulist$[@Tj], @pTmenuitems$[@Ti]+" -> "+@pTprice[@Ti];
	if (@pTprice[@Ti] == 0)&&($QW_MapUserShow == 0) set @Tmenulist$[@Tj], @pTmenuitems$[@Ti];
	if (@pTprice[@Ti] != 0)&&($QW_MapUserShow == 1) set @Tmenulist$[@Tj], @pTmenuitems$[@Ti]+" ["+getmapusers(@pTmap$[@Ti])+"]"+" -> "+@pTprice[@Ti];
	if (@pTprice[@Ti] == 0)&&($QW_MapUserShow == 1) set @Tmenulist$[@Tj], @pTmenuitems$[@Ti]+" ["+getmapusers(@pTmap$[@Ti])+"]";
	set @Tmenuref[@Tj],@Ti;
	set @Tj,@Tj+1;
	set @Ti,@Ti+1;
	return;
}
}

//====================Dungeon=Menu=Arrays==========================
// Adding a dungeon:
//	setarray @pDmenuitems$[@Di], "Abyss Lake";	// Name of Dungeon shown in Dungeon select Menu
//	setarray @pDprice[@Di], $QW_AW_PRICE;		// Warp prices: Basic ($QW_BW_PRICE), Advanced ($QW_AW_PRICE), Overseas ($QW_OW_PRICE)
//	setarray @pDfee$[@Di], "$QW_AW_FEE";		// Identifier for fee caluclation: Basic ("$QW_BW_FEE"), Advanced ("$QW_AW_FEE"), Overseas ("$QW_OW_FEE")
//	setarray @DLevels[@Di], 3;			// Number of levels in dungeon
//
//	setarray @DGat$[@Dref], "abyss_01", "abyss_02", "abyss_03";	// Map names of dungeon levels
//	setarray @DLevelName$[@Dref], "Level 1", "Level 2", "Level 3";	// Name of dungeon levels shown in Menu
//	setarray @DXcoords[@Dref], 265, 275, 116;			// X warp coords
//	setarray @DYcoords[@Dref], 273, 270, 27;			// Y warp coords
//	setarray @DDepth[@Dref], 0, 1, 2;				// Relative depth to entrance, to calculate extra warp fee
//
//	QWS_Make_Dungeon_Menu 0;	// Use a free number (last used is 28, Kiel Dungeon)
//
// Adding a warpra:
//	map,x,y,facing	script	Warpra Helper#example	112,{
//		callfunc "QWS_Dungeon_Warpra",<dungeon number>,"Your Dungeon";
//		close;
//	}
//	Dungeon number is the same as "QWS_Make_Dungeon_Menu".
//==================================================================

function	script	QWS_Darray	{
	function QWS_Make_Dungeon_Menu;

	set @Di,0;
	set @Dj,0;
	set @Dref,0;

//----------------ABYSS LAKE
	setarray @pDmenuitems$[@Di], "Abyss Lake";
	setarray @pDprice[@Di], $QW_AW_PRICE;
	setarray @pDfee$[@Di], "$QW_AW_FEE";
	setarray @DLevels[@Di], 3;

	setarray @DGat$[@Dref], "abyss_01", "abyss_02", "abyss_03";
	setarray @DLevelName$[@Dref], "Level 1", "Level 2", "Level 3";
	setarray @DXcoords[@Dref], 265, 275, 116;
	setarray @DYcoords[@Dref], 273, 270, 27;
	setarray @DDepth[@Dref], 0, 1, 2;

	QWS_Make_Dungeon_Menu 0;

//----------------AMATSU DUNGEON
	setarray @pDmenuitems$[@Di], "Amatsu Dungeon";
	setarray @pDprice[@Di], $QW_OW_PRICE;
	setarray @pDfee$[@Di], "$QW_OW_FEE";
	setarray @DLevels[@Di], 3;

	setarray @DGat$[@Dref], "ama_dun01", "ama_dun02", "ama_dun03";
	setarray @DLevelName$[@Dref], "Level 1", "Level 2", "Level 3";
	setarray @DXcoords[@Dref], 227, 32, 119;
	setarray @DYcoords[@Dref], 10, 43, 15;
	setarray @DDepth[@Dref], 0, 1, 2;

	QWS_Make_Dungeon_Menu 1;

//----------------ANT HELL
	setarray @pDmenuitems$[@Di], "Ant Hell Dungeon"; 
	setarray @pDprice[@Di], $QW_BW_PRICE;
	setarray @pDfee$[@Di], "$QW_BW_FEE";
	setarray @DLevels[2], 2;

	setarray @DGat$[@Dref], "anthell01", "anthell02";
	setarray @DLevelName$[@Dref], "Level 1", "Level 2";
	setarray @DXcoords[@Dref], 32, 34;
	setarray @DYcoords[@Dref], 262, 263;
	setarray @DDepth[@Dref], 0, 1;

	QWS_Make_Dungeon_Menu 2;

//----------------AYOTHAYA
	setarray @pDmenuitems$[@Di], "Ayothaya Dungeon";
	setarray @pDprice[@Di], $QW_OW_PRICE;
	setarray @pDfee$[@Di], "$QW_OW_FEE";
	setarray @DLevels[3], 2;

	setarray @DGat$[@Dref], "ayo_dun01", "ayo_dun02";
	setarray @DLevelName$[@Dref], "Level 1", "Level 2";
	setarray @DXcoords[@Dref], 275, 150;
	setarray @DYcoords[@Dref], 17, 13;
	setarray @DDepth[@Dref], 0, 1;

	QWS_Make_Dungeon_Menu 3;

//----------------BYALAN
	setarray @pDmenuitems$[@Di], "Byalan Dungeon";
	setarray @pDprice[@Di], $QW_BW_PRICE;
	setarray @pDfee$[@Di], "$QW_BW_FEE";
	setarray @DLevels[4], 5;

	setarray @DGat$[@Dref], "iz_dun00", "iz_dun01", "iz_dun02", "iz_dun03", "iz_dun04";
	setarray @DLevelName$[@Dref], "Level 1", "Level 2", "Level 3", "Level 4", "Level 5";
	setarray @DXcoords[@Dref], 168, 41, 236, 32, 26;
	setarray @DYcoords[@Dref], 168, 37, 204, 63, 27;
	setarray @DDepth[@Dref], 0, 1, 2, 3, 4;

	QWS_Make_Dungeon_Menu 4;

//----------------CLOCK TOWER
	setarray @pDmenuitems$[@Di], "Clock Tower Dungeon";
	setarray @pDprice[@Di], $QW_AW_PRICE;
	setarray @pDfee$[@Di], "$QW_AW_FEE";
	setarray @DLevels[5], 8;

	setarray @DGat$[@Dref], "c_tower1", "c_tower2", "c_tower3", "c_tower4", "alde_dun01", "alde_dun02", "alde_dun03", "alde_dun04";
	setarray @DLevelName$[@Dref], "Clock Tower Level 1", "Clock Tower Level 2", "Clock Tower Level 3", "Clock Tower Level 4", "Basement 1F", "Basement 2F", "Basement 3F", "Basement 4F";
	setarray @DXcoords[@Dref], 200, 268, 64, 32, 197, 262, 276, 122;
	setarray @DYcoords[@Dref], 163, 26, 148, 63, 25, 41, 53, 125;
	setarray @DDepth[@Dref], 0, 1, 2, 3, 1, 2, 3, 4;

	QWS_Make_Dungeon_Menu 5;

//----------------COAL MINE
	setarray @pDmenuitems$[@Di], "Coal Mine Dungeon";
	setarray @pDprice[@Di], $QW_AW_PRICE;
	setarray @pDfee$[@Di], "$QW_AW_FEE";
	setarray @DLevels[6], 3;

	setarray @DGat$[@Dref], "mjo_dun01", "mjo_dun02", "mjo_dun03";
	setarray @DLevelName$[@Dref], "Level 1", "Level 2", "Level 3";
	setarray @DXcoords[@Dref], 52, 381, 302;
	setarray @DYcoords[@Dref], 17, 343, 261;
	setarray @DDepth[@Dref], 0, 1, 2;

	QWS_Make_Dungeon_Menu 6;

//----------------CULVERT
	setarray @pDmenuitems$[@Di], "Culvert Dungeon";
	setarray @pDprice[@Di], $QW_BW_PRICE;
	setarray @pDfee$[@Di], "$QW_BW_FEE";
	setarray @DLevels[7], 4;

	setarray @DGat$[@Dref], "prt_sewb1", "prt_sewb2", "prt_sewb3", "prt_sewb4";
	setarray @DLevelName$[@Dref], "Level 1", "Level 2", "Level 3", "Level 4";
	setarray @DXcoords[@Dref], 132, 19, 180, 100;
	setarray @DYcoords[@Dref], 248, 19, 169, 92;
	setarray @DDepth[@Dref], 0, 1, 2, 3;

	QWS_Make_Dungeon_Menu 7;

//----------------EINBECH DUNGEON
	setarray @pDmenuitems$[@Di], "Einbech Dungeon";
	setarray @pDprice[@Di], $QW_AW_PRICE;
	setarray @pDfee$[@Di], "$QW_AW_FEE";
	setarray @DLevels[8], 2;

	setarray @DGat$[@Dref], "ein_dun01", "ein_dun02";
	setarray @DLevelName$[@Dref], "Level 1", "Level 2";
	setarray @DXcoords[@Dref], 22, 292;
	setarray @DYcoords[@Dref], 14, 290;
	setarray @DDepth[@Dref], 0, 1;

	QWS_Make_Dungeon_Menu 8;

//----------------GEFENIA DUNGEON
	setarray @pDmenuitems$[@Di], "Gefenia Dungeon";
	setarray @pDprice[@Di], $QW_BW_PRICE;
	setarray @pDfee$[@Di], "$QW_BW_FEE";
	setarray @DLevels[9], 4;

	setarray @DGat$[@Dref], "gefenia01", "gefenia02", "gefenia03", "gefenia04";
	setarray @DLevelName$[@Dref], "Level 1", "Level 2", "Level 3", "Level 4";
	setarray @DXcoords[@Dref], 59, 201, 264, 33;
	setarray @DYcoords[@Dref], 167, 35, 236, 270;
	setarray @DDepth[@Dref], 0, 1, 2, 3;

	QWS_Make_Dungeon_Menu 9;

//----------------GEFFEN DUNGEON
	setarray @pDmenuitems$[@Di], "Geffen Dungeon";
	setarray @pDprice[@Di], $QW_BW_PRICE;
	setarray @pDfee$[@Di], "$QW_BW_FEE";
	setarray @DLevels[@Di], 4;

	setarray @DGat$[@Dref], "gef_dun00", "gef_dun01", "gef_dun02", "gef_dun03";
	setarray @DLevelName$[@Dref], "Level 1", "Level 2", "Level 3", "Level 4";
	setarray @DXcoords[@Dref], 104, 115, 106, 203;
	setarray @DYcoords[@Dref], 100, 236, 132, 200;
	setarray @DDepth[@Dref], 0, 1, 2, 3;

	QWS_Make_Dungeon_Menu 10;

//----------------GLAST HEIM
	setarray @pDmenuitems$[@Di], "Glast Heim Dungeon";
	setarray @pDprice[@Di], $QW_BW_PRICE;
	setarray @pDfee$[@Di], "$QW_BW_FEE";
	setarray @DLevels[@Di], 17;

	setarray @DGat$[@Dref], "glast_01", "gl_church", "gl_chyard", "gl_in01", "gl_cas01", "gl_cas02", "gl_knt01", "gl_knt02", "gl_prison", "gl_prison1", "gl_step", "gl_sew01", "gl_sew02", "gl_sew03", "gl_sew04", "gl_dun01", "gl_dun02";
	setarray @DLevelName$[@Dref], "Glast Heim Entrance", "St. Abbey", "Churchyard", "Inside Glast Heim", "Castle 1", "Castle 2", "Chivalry 1", "Chivalry 2", "Prison 1", "Prison 2", "Steps", "Sewers 1", "Sewers 2", "Sewers 3", "Sewers 4", "Lowest Cave 1", "Lowest Cave 2";
	setarray @DXcoords[@Dref], 370, 156, 147, 121, 199, 104, 150, 157, 14, 150, 117, 258, 108, 171, 68, 133, 224;
	setarray @DYcoords[@Dref], 300, 8, 15, 59, 29, 25, 10, 287, 70, 14, 124, 255, 291, 273, 277, 271, 274;
	setarray @DDepth[@Dref], 0, 1, 2, 1, 1, 2, 1, 2, 2, 3, 1, 4, 2, 3, 4, 5, 6;

	QWS_Make_Dungeon_Menu 11;

//----------------KUNLUN DUNGEON
	setarray @pDmenuitems$[@Di], "Kunlun Dungeon";
	setarray @pDprice[@Di], $QW_OW_PRICE;
	setarray @pDfee$[@Di], "$QW_OW_FEE";
	setarray @DLevels[@Di], 3;

	setarray @DGat$[@Dref], "gon_dun01", "gon_dun02", "gon_dun03";
	setarray @DLevelName$[@Dref], "Level 1", "Level 2", "Level 3";
	setarray @DXcoords[@Dref], 143, 17, 68;
	setarray @DYcoords[@Dref], 59, 114, 9;
	setarray @DDepth[@Dref], 0, 1, 2;

	QWS_Make_Dungeon_Menu 12;

//----------------HIDDEN DUNGEON
	setarray @pDmenuitems$[@Di], "Hidden Dungeon";
	setarray @pDprice[@Di], $QW_BW_PRICE;
	setarray @pDfee$[@Di], "$QW_BW_FEE";
	setarray @DLevels[@Di], 3;

	setarray @DGat$[@Dref], "prt_maze01", "prt_maze02", "prt_maze03";
	setarray @DLevelName$[@Dref], "Level 1", "Level 2", "Level 3";
	setarray @DXcoords[@Dref], 176, 94, 23;
	setarray @DYcoords[@Dref], 6, 19, 8;
	setarray @DDepth[@Dref], 0, 1, 2;

	QWS_Make_Dungeon_Menu 13;

//----------------JUPEROS CAVE
	setarray @pDmenuitems$[@Di], "Juperos Cave";
	setarray @pDprice[@Di], $QW_AW_PRICE;
	setarray @pDfee$[@Di], "$QW_AW_FEE";
	setarray @DLevels[@Di], 2;

	setarray @DGat$[@Dref], "juperos_01", "juperos_02";
	setarray @DLevelName$[@Dref], "Level 1", "Level 2";
	setarray @DXcoords[@Dref], 53, 36;
	setarray @DYcoords[@Dref], 247, 60;
	setarray @DDepth[@Dref], 0, 1;

	QWS_Make_Dungeon_Menu 14;

//----------------KIEL DUNGEON
	setarray @pDmenuitems$[@Di], "Kiel Dungeon";
	setarray @pDprice[@Di], $QW_AW_PRICE;
	setarray @pDfee$[@Di], "$QW_AW_FEE";
	setarray @DLevels[@Di], 2;

	setarray @DGat$[@Dref], "kh_dun01", "kh_dun02";
	setarray @DLevelName$[@Dref], "1st Floor", "2nd Floor";
	setarray @DXcoords[@Dref], 63, 42;
	setarray @DYcoords[@Dref], 10, 197;
	setarray @DDepth[@Dref], 0, 1;

	QWS_Make_Dungeon_Menu 28;

//----------------LIGHTHALZEN BIO LAB
	setarray @pDmenuitems$[@Di], "Lighthalzen Bio Lab";
	setarray @pDprice[@Di], $QW_AW_PRICE;
	setarray @pDfee$[@Di], "$QW_AW_FEE";
	setarray @DLevels[@Di], 3;

	setarray @DGat$[@Dref], "lhz_dun01", "lhz_dun02", "lhz_dun03";
	setarray @DLevelName$[@Dref], "Level 1", "Level 2", "Level 3";
	setarray @DXcoords[@Dref], 150, 150, 140;
	setarray @DYcoords[@Dref], 287, 18, 137;
	setarray @DDepth[@Dref], 0, 1, 2;

	QWS_Make_Dungeon_Menu 15;

//----------------LOUYANG DUNGEON
	setarray @pDmenuitems$[@Di], "Luoyang Dungeon";
	setarray @pDprice[@Di], $QW_OW_PRICE;
	setarray @pDfee$[@Di], "$QW_OW_FEE";
	setarray @DLevels[@Di], 2;

	setarray @DGat$[@Dref], "lou_dun02", "lou_dun03";
	setarray @DLevelName$[@Dref], "Royal Tomb Level 1", "Royal Tomb Level 2";
	setarray @DXcoords[@Dref], 282, 165;
	setarray @DYcoords[@Dref], 20, 38;
	setarray @DDepth[@Dref], 0, 1;

	QWS_Make_Dungeon_Menu 16;

//----------------MAGMA DUNGEON
	setarray @pDmenuitems$[@Di], "Magma Dungeon";
	setarray @pDprice[@Di], $QW_AW_PRICE;
	setarray @pDfee$[@Di], "$QW_AW_FEE";
	setarray @DLevels[@Di], 2;

	setarray @DGat$[@Dref], "mag_dun01", "mag_dun02";
	setarray @DLevelName$[@Dref], "Level 1", "Level 2";
	setarray @DXcoords[@Dref], 126, 47;
	setarray @DYcoords[@Dref], 69, 32;
	setarray @DDepth[@Dref], 0, 1;

	QWS_Make_Dungeon_Menu 17;

//----------------ODIN TEMPLE
	setarray @pDmenuitems$[@Di], "Odin Temple";
	setarray @pDprice[@Di], $QW_AW_PRICE;
	setarray @pDfee$[@Di], "$QW_AW_FEE";
	setarray @DLevels[@Di], 1;

	setarray @DGat$[@Dref], "odin_tem01";
	setarray @DLevelName$[@Dref], "Level 1";
	setarray @DXcoords[@Dref], 96;
	setarray @DYcoords[@Dref], 145;
	setarray @DDepth[@Dref], 0;

	QWS_Make_Dungeon_Menu 18;

//----------------ORC DUNGEON
	setarray @pDmenuitems$[@Di], "Orc Dungeon";
	setarray @pDprice[@Di], $QW_BW_PRICE;
	setarray @pDfee$[@Di], "$QW_BW_FEE";
	setarray @DLevels[@Di], 2;

	setarray @DGat$[@Dref], "orcsdun01", "orcsdun02";
	setarray @DLevelName$[@Dref], "Level 1", "Level 2";
	setarray @DXcoords[@Dref], 32, 21;
	setarray @DYcoords[@Dref], 169, 185;
	setarray @DDepth[@Dref], 0, 1;

	QWS_Make_Dungeon_Menu 19;

//----------------PAYON DUNGEON
	setarray @pDmenuitems$[@Di], "Payon Dungeon";
	setarray @pDprice[@Di], $QW_BW_PRICE;
	setarray @pDfee$[@Di], "$QW_BW_FEE";
	setarray @DLevels[@Di], 5;

	setarray @DGat$[@Dref], "pay_dun00", "pay_dun01", "pay_dun02", "pay_dun03", "pay_dun04";
	setarray @DLevelName$[@Dref], "Level 1", "Level 2", "Level 3", "Level 4", "Level 5";
	setarray @DXcoords[@Dref], 22, 19, 19, 155, 201;
	setarray @DYcoords[@Dref], 180, 33, 63, 159, 204;
	setarray @DDepth[@Dref], 0, 1, 2, 3, 4;

	QWS_Make_Dungeon_Menu 20;

//----------------PYRAMIDS
	setarray @pDmenuitems$[@Di], "Pyramids Dungeon";
	setarray @pDprice[@Di], $QW_BW_PRICE;
	setarray @pDfee$[@Di], "$QW_BW_FEE";
	setarray @DLevels[@Di], 6;

	setarray @DGat$[@Dref], "moc_pryd01", "moc_pryd02", "moc_pryd03", "moc_pryd04", "moc_pryd05", "moc_pryd06";
	setarray @DLevelName$[@Dref], "Level 1", "Level 2", "Level 3", "Level 4", "Basement 1", "Basement 2";
	setarray @DXcoords[@Dref], 192, 10, 100, 181, 94, 192;
	setarray @DYcoords[@Dref], 9, 192, 92, 11, 96, 8;
	setarray @DDepth[@Dref], 0, 1, 2, 3, 1, 2;

	QWS_Make_Dungeon_Menu 21;

//----------------SPHINX
	setarray @pDmenuitems$[@Di], "Sphinx Dungeon";
	setarray @pDprice[@Di], $QW_BW_PRICE;
	setarray @pDfee$[@Di], "$QW_BW_FEE";
	setarray @DLevels[@Di], 5;

	setarray @DGat$[@Dref], "in_sphinx1", "in_sphinx2", "in_sphinx3", "in_sphinx4", "in_sphinx5";
	setarray @DLevelName$[@Dref], "Level 1", "Level 2", "Level 3", "Level 4", "Level 5";
	setarray @DXcoords[@Dref], 192, 149, 210, 10, 100;
	setarray @DYcoords[@Dref], 9, 81, 54, 222, 99;
	setarray @DDepth[@Dref], 0, 1, 2, 3, 4;

	QWS_Make_Dungeon_Menu 22;

//----------------SUNKEN SHIP
	setarray @pDmenuitems$[@Di], "Sunken Ship Dungeon";
	setarray @pDprice[@Di], $QW_BW_PRICE;
	setarray @pDfee$[@Di], "$QW_BW_FEE";
	setarray @DLevels[@Di], 2;

	setarray @DGat$[@Dref], "treasure01", "treasure02";
	setarray @DLevelName$[@Dref], "Level 1", "Level 2";
	setarray @DXcoords[@Dref], 69, 102;
	setarray @DYcoords[@Dref], 24, 27;
	setarray @DDepth[@Dref], 0, 1;

	QWS_Make_Dungeon_Menu 23;

//----------------THANATOS TOWER
	setarray @pDmenuitems$[@Di], "Thanatos Tower";
	setarray @pDprice[@Di], $QW_AW_PRICE;
	setarray @pDfee$[@Di], "$QW_AW_FEE";
	setarray @DLevels[@Di], 13;

	setarray @DGat$[@Dref], "tha_t01", "tha_t02", "tha_t03", "tha_t04", "tha_t05", "tha_t06", "tha_t07", "tha_t08", "tha_t09", "tha_t10", "tha_t11", "tha_t12", "thana_boss";
	setarray @DLevelName$[@Dref], "Level 1", "Level 2", "Level 3", "Level 4", "Level 5", "Level 6", "Level 7", "Level 8", "Level 9", "Level 10", "Level 11", "Level 12", "Thanatos Boss";
	setarray @DXcoords[@Dref], 150, 150, 220, 59, 62, 206, 35, 105, 88, 168, 90, 129, 85;
	setarray @DYcoords[@Dref], 35, 136, 158, 143, 11, 8, 166, 44, 145, 138, 36, 83, 76;
	setarray @DDepth[@Dref], 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12;

	QWS_Make_Dungeon_Menu 24;

//----------------TOY FACTORY
	setarray @pDmenuitems$[@Di], "Toy Factory Dungeon";
	setarray @pDprice[@Di], $QW_AW_PRICE;
	setarray @pDfee$[@Di], "$QW_AW_FEE";
	setarray @DLevels[@Di], 2;

	setarray @DGat$[@Dref], "xmas_dun01", "xmas_dun02";
	setarray @DLevelName$[@Dref], "Factory Warehouse", "Classifying Room";
	setarray @DXcoords[@Dref], 205, 129;
	setarray @DYcoords[@Dref], 16, 133;
	setarray @DDepth[@Dref], 0, 1;

	QWS_Make_Dungeon_Menu 25;

//----------------TURTLE ISTLAND
	setarray @pDmenuitems$[@Di], "Turtle Island Dungeon";
	setarray @pDprice[@Di], $QW_AW_PRICE;
	setarray @pDfee$[@Di], "$QW_AW_FEE";
	setarray @DLevels[@Di], 4;

	setarray @DGat$[@Dref], "tur_dun01", "tur_dun02", "tur_dun03", "tur_dun04";
	setarray @DLevelName$[@Dref], "Outside", "Level 1", "Level 2", "Level 3";
	setarray @DXcoords[@Dref], 161, 148, 132, 100;
	setarray @DYcoords[@Dref], 34, 256, 190, 192;
	setarray @DDepth[@Dref], 0, 0, 1, 2;

	QWS_Make_Dungeon_Menu 26;

//----------------UMBALA
	setarray @pDmenuitems$[@Di], "Umbala Dungeon";
	setarray @pDprice[@Di], $QW_OW_PRICE;
	setarray @pDfee$[@Di], "$QW_OW_FEE";
	setarray @DLevels[@Di], 3;

	setarray @DGat$[@Dref], "um_dun01", "um_dun02", "yggdrasil01";
	setarray @DLevelName$[@Dref], "Carpenter's Shop in The Tree", "Passage to a Foreign World", "Hvergelmir's Fountain";
	setarray @DXcoords[@Dref], 205, 48, 40;
	setarray @DYcoords[@Dref], 26, 30, 63;
	setarray @DDepth[@Dref], 0, 1, 2;

	QWS_Make_Dungeon_Menu 27;

//----------------Cancel
	setarray @pDmenuitems$[@Di], "Cancel";
	setarray @pDprice[@Di], 0;

	set @Dmenulist$[@Dj],@pDmenuitems$[@Di];
	set @Dmenuref[@Dj],57005; // 57005='dead' in hex
	return;


//----------------Make Dungeon Menu Function

function	QWS_Make_Dungeon_Menu	{
	set @tempdungeonmenubin,1;
	if (getarg(0) == 0) goto menu_item;
	set @temploopdungeonmenu,0;
	do {
		set @tempdungeonmenubin, @tempdungeonmenubin * 2;
		set @temploopdungeonmenu, @temploopdungeonmenu + 1;
		}while (getarg(0) > @temploopdungeonmenu);
// check marker and make menu item
menu_item:
	if ((@tempdungeonmenubin & ##QWS_D_Unlock) != @tempdungeonmenubin) {
		setarray @DLocRef[@Di], @Dref;
		set @Dref, @Dref+@DLevels[@Di];
		set @Di,@Di+1;
		return;
	}
	if ($QW_MapUserShow == 1) {
		set @tempmapusers,0;
		set @mapusersloop,0;
		do {
			set @tempmapusers,(getmapusers(@DGat$[@Dref+@mapusersloop]) + @tempmapusers);
			set @mapusersloop, (@mapusersloop + 1);
		} while (@DLevels[@Di] > @mapusersloop);
		if (@pDprice[@Di] != 0) set @Dmenulist$[@Dj], @pDmenuitems$[@Di]+" ["+@tempmapusers+"] -> "+@pDprice[@Di];
		if (@pDprice[@Di] == 0) set @Dmenulist$[@Dj], @pDmenuitems$[@Di]+" ["+@tempmapusers+"]";
		set @Dmenuref[@Dj],@Di;
		set @Dj,@Dj+1;
		setarray @DLocRef[@Di], @Dref;
		set @Dref, @Dref+@DLevels[@Di];
		set @Di,@Di+1;
		return;
	}
	if (@pDprice[@Di] != 0) set @Dmenulist$[@Dj], @pDmenuitems$[@Di]+" -> "+@pDprice[@Di];
	if (@pDprice[@Di] == 0) set @Dmenulist$[@Dj], @pDmenuitems$[@Di];
	set @Dmenuref[@Dj],@Di;
	set @Dj,@Dj+1;
	setarray @DLocRef[@Di], @Dref;
	set @Dref, @Dref+@DLevels[@Di];
	set @Di,@Di+1;
	return;
}
}

function	script	QWS_DLarray	{
//----------------Start building Menu
	set @DWref,@DLocRef[@DwarpMenu]; // reference to the arrays with leveldata
	set @DWi,0;  // loop counter
	set @DWj,0; // menu lines counter
	cleararray @DWLmenulist$[0],"",20; // Clearing the array to get rid off ghost items in menu
	do {
		if ($QW_DL == 0)||($QW_DDL >= @DDepth[@DWref]) {
			set @warpprice, (@pDprice[@DwarpMenu]+ (getd(@pDfee$[@DwarpMenu])*@DDepth[@DWref]));
			if (@warpprice != 0)&&($QW_MapUserShow == 0) set @DWLmenulist$[@DWj], @DLevelName$[@DWref]+" -> "+@warpprice;
			if (@warpprice == 0)&&($QW_MapUserShow == 0) set @DWLmenulist$[@DWj], @DLevelName$[@DWref];
			if (@warpprice != 0)&&($QW_MapUserShow == 1) set @DWLmenulist$[@DWj], @DLevelName$[@DWref]+" ["+getmapusers(@DGat$[@DWref])+"] -> "+@warpprice;
			if (@warpprice == 0)&&($QW_MapUserShow == 1) set @DWLmenulist$[@DWj], @DLevelName$[@DWref]+" ["+getmapusers(@DGat$[@DWref])+"]";
			set @DWLmenuref[@DWj],@DWref;
			set @DWj,@DWj+1;
		}
		set @DWref, @DWref+1;
		set @DWi,@DWi+1;
	} while (@DWi < @DLevels[@DwarpMenu]);
	set @DWLmenulist$[@DWj], "Exit";
	set @DWLmenuref[@DWj],57005; // 57005='dead' in hex
	return;
}

//================Dungeon=Warpras=That=Only=Unlock=================

function	script	QWS_Dungeon_Warpra	{
	function QWS_D_getbin;
	function QWS_D_setbin;
	
	if ((QWS_D_getbin(getarg(0)) == 0) && (getarg(1) != "")) {
		mes "[Warpra]";
		mes getarg(1)+" unlocked!";
		QWS_D_setbin(getarg(0));
		return;
	} else if (QWS_D_getbin(getarg(0)) == 1) {
		mes "[Warpra]";
		mes "Sorry, I can only unlock this location.";
	} else
		debugmes "QWS_Dungeon_Warpra error, improper syntax?";
	return;


function	QWS_D_setbin	{
	set @tempsetbindata,1;
	if (getarg(0) == 0) goto binset;
	set @temploopsetbin,0;
	do {
		set @tempsetbindata, @tempsetbindata * 2;
		set @temploopsetbin, @temploopsetbin + 1;
	} while (getarg(0) > @temploopsetbin);
binset:
	set ##QWS_D_Unlock,(##QWS_D_Unlock | @tempsetbindata);
	return;
}

function	QWS_D_getbin	{
	set @tempgetbindata,1;
	if (getarg(0) == 0) goto binget;
	set @temploopgetbin,0;
	do {
		set @tempgetbindata, @tempgetbindata * 2;
		set @temploopgetbin, @temploopgetbin + 1;
	} while (getarg(0) > @temploopgetbin);
binget:
	if ((@tempgetbindata & ##QWS_D_Unlock) == @tempgetbindata) return 1;
	return 0;
}
}

//==========================Town=Warpras===========================

function	script	QWS_Town_Warpra	{
	function QWS_T_getbin;
	function QWS_T_setbin;
	
	if ((QWS_T_getbin(getarg(0)) == 0) && (getarg(1) != "")) {
		mes "[Warpra]";
		mes getarg(1)+" unlocked!";
		QWS_T_setbin(getarg(0));
		return;
	} else if (QWS_T_getbin(getarg(0)) == 1) {
		callfunc "Q_Warpra",0;
	} else
		debugmes "QWS_Town_Warpra error, improper syntax ?";
	return;

function	QWS_T_setbin	{
	set @tempsetbindata,1;
	if (getarg(0) == 0) goto binset;
	set @temploopsetbin,0;
	do {
		set @tempsetbindata, @tempsetbindata * 2;
		set @temploopsetbin, @temploopsetbin + 1;
	} while (getarg(0) > @temploopsetbin);
binset:
	set ##QWS_T_Unlock,(##QWS_T_Unlock | @tempsetbindata);
	return;
}

function	QWS_T_getbin	{
	set @tempgetbindata,1;
	if (getarg(0) == 0) goto binget;
	set @temploopgetbin,0;
	do {
		set @tempgetbindata, @tempgetbindata * 2;
		set @temploopgetbin, @temploopgetbin + 1;
	} while (getarg(0) > @temploopgetbin);
binget:
	if ((@tempgetbindata & ##QWS_T_Unlock) == @tempgetbindata) return 1;
	return 0;
}
}
//============================Warpras==============================

-	script	Warpra#0	-1,{ callfunc "Q_Warpra",1; }
alb2trea,73,101,4	duplicate(Warpra#0)	Warpra#1	113
ama_fild01,178,325,1	duplicate(Warpra#0)	Warpra#2	113
gef_fild10,71,339,4	duplicate(Warpra#0)	Warpra#3	113
izlu2dun,104,82,4	duplicate(Warpra#0)	Warpra#4	113
mjolnir_02,85,363,4	duplicate(Warpra#0)	Warpra#5	113
moc_fild04,207,331,4	duplicate(Warpra#0)	Warpra#6	113
moc_fild19,106,97,4	duplicate(Warpra#0)	Warpra#7	113
moc_ruins,64,166,4	duplicate(Warpra#0)	Warpra#8	113
niflheim,197,192,3	duplicate(Warpra#0)	Warpra#9	113
pay_arche,39,135,4	duplicate(Warpra#0)	Warpra#10	113
prt_fild05,273,215,4	duplicate(Warpra#0)	Warpra#11	113
tur_dun01,148,239,4	duplicate(Warpra#0)	Warpra#12	113
valkyrie,48,35,8	duplicate(Warpra#0)	Warpra#13	113
yuno_fild03,37,135,4	duplicate(Warpra#0)	Warpra#14	113

alberta,32,240,4	script	Warpra#15	113,{ callfunc "QWS_Town_Warpra",1,"Alberta Town"; close; }
aldebaran,146,118,4	script	Warpra#16	113,{ callfunc "QWS_Town_Warpra",2,"Aldebaran Town"; close; }
ayothaya,216,171,5	script	Warpra#17	113,{ callfunc "QWS_Town_Warpra",4,"Ayothaya Town"; close; }
amatsu,193,81,1	script	Warpra#18	113,{ callfunc "QWS_Town_Warpra",3,"Amatsu Town"; close; }
comodo,195,158,4	script	Warpra#19	113,{ callfunc "QWS_Town_Warpra",5,"Comodo Town"; close; }
einbroch,229,196,5	script	Warpra#20	113,{ callfunc "QWS_Town_Warpra",7,"Einbroch Town"; close; }
einbech,173,131,5	script	Warpra#21	113,{ callfunc "QWS_Town_Warpra",6,"Einbech Town"; close; }
geffen,116,66,4	script	Warpra#22	113,{ callfunc "QWS_Town_Warpra",8,"Geffen Town"; close; }
gonryun,152,130,4	script	Warpra#23	113,{ callfunc "QWS_Town_Warpra",9,"Kunlun Town"; close; }
hugel,90,127,5	script	Warpra#24	113,{ callfunc "QWS_Town_Warpra",10,"Hugel Town"; close; }
jawaii,107,182,5	script	Warpra#25	113,{ callfunc "QWS_Town_Warpra",12,"Jawaii Town"; close; }
izlude,132,116,4	script	Warpra#26	113,{ callfunc "QWS_Town_Warpra",11,"Izlude Town"; close; }
lighthalzen,153,100,5	script	Warpra#27	113,{ callfunc "QWS_Town_Warpra",13,"Lighthalzen Town"; close; }
louyang,211,106,4	script	Warpra#28	113,{ callfunc "QWS_Town_Warpra",14,"Luoyang Town"; close; }
morocc,157,95,4	script	Warpra#29	113,{ callfunc "QWS_Town_Warpra",16,"Morocc Town"; close; }
nif_fild01,319,77,1	script	Warpra#30	113,{ callfunc "QWS_Town_Warpra",17,"Niflheim Town"; close; }
payon,183,110,4	script	Warpra#31	113,{ callfunc "QWS_Town_Warpra",18,"Payon Town"; close; }
prontera,147,172,5	script	Warpra#32	113,{ callfunc "QWS_Town_Warpra",0,"Prontera Town"; close; }
umbala,133,130,4	script	Warpra#33	113,{ callfunc "QWS_Town_Warpra",19,"Umbala Town"; close; }
xmas,151,136,4	script	Warpra#34	113,{ callfunc "QWS_Town_Warpra",15,"Lutie Town"; close; }
yuno,138,162,4	script	Warpra#35	113,{ callfunc "QWS_Town_Warpra",20,"Juno Town"; close; }
brasilis,201,222,4	script	Warpra#36	113,{ callfunc "QWS_Town_Warpra",21,"Brasilis Town"; close; }
dicastes01,194,194,6	script	Warpra#37	113,{ callfunc "QWS_Town_Warpra",22,"El Dicastes Town"; close; }
manuk,262,177,4	script	Warpra#38	113,{ callfunc "QWS_Town_Warpra",23,"Manuk Town"; close; }
mora,110,100,4	script	Warpra#39	113,{ callfunc "QWS_Town_Warpra",24,"Mora Town"; close; }
moscovia,216,196,6	script	Warpra#40	113,{ callfunc "QWS_Town_Warpra",25,"Moscovia Town"; close; }
rachel,135,116,4	script	Warpra#41	113,{ callfunc "QWS_Town_Warpra",26,"Rachel Town"; close; }
splendide,205,153,4	script	Warpra#42	113,{ callfunc "QWS_Town_Warpra",27,"Splendide Town"; close; }
veins,214,123,4	script	Warpra#43	113,{ callfunc "QWS_Town_Warpra",28,"Veins Town"; close; }
dewata,194,178,6	script	Warpra#44	113,{ callfunc "QWS_Town_Warpra",29,"Dewata Town"; close; }
eclage,107,37,4	script	Warpra#45	113,{ callfunc "QWS_Town_Warpra",30,"Eclage Town"; close; }

abyss_02,274,266,1	script	Warpra Helper#0	112,{ callfunc "QWS_Dungeon_Warpra",0,"Abyss Lake"; close; }
ama_dun02,192,118,5	script	Warpra Helper#1	112,{ callfunc "QWS_Dungeon_Warpra",1,"Amatsu Dungeon"; close; }
anthell02,170,165,3	script	Warpra Helper#2	112,{ callfunc "QWS_Dungeon_Warpra",2,"Ant Hell Dungeon"; close; }
ayo_dun02,258,193,5	script	Warpra Helper#3	112,{ callfunc "QWS_Dungeon_Warpra",3,"Ayothaya Dungeon"; close; }
ein_dun02,292,282,1	script	Warpra Helper#4	112,{ callfunc "QWS_Dungeon_Warpra",8,"Einbech Dungeon"; close; }
iz_dun03,202,47,2	script	Warpra Helper#5	112,{ callfunc "QWS_Dungeon_Warpra",4,"Byalan Dungeon"; close; }
c_tower3,129,106,4	script	Warpra Helper#6	112,{ callfunc "QWS_Dungeon_Warpra",5,"Clock Tower Dungeon"; close; }
mjo_dun02,39,25,4	script	Warpra Helper#7	112,{ callfunc "QWS_Dungeon_Warpra",6,"Coal Mine Dungeon"; close; }
prt_sewb2,176,30,3	script	Warpra Helper#8	112,{ callfunc "QWS_Dungeon_Warpra",7,"Culvert Dungeon"; close; }
gefenia03,137,34,0	script	Warpra Helper#9	112,{ callfunc "QWS_Dungeon_Warpra",9,"Gefenia Dungeon"; close; }
gef_dun02,218,61,2	script	Warpra Helper#10	112,{ callfunc "QWS_Dungeon_Warpra",10,"Gefen Dungeon"; close; }
glast_01,371,308,3	script	Warpra Helper#11	112,{ callfunc "QWS_Dungeon_Warpra",11,"Glast Heim Dungeon"; close; }
gon_dun01,167,273,4	script	Warpra Helper#12	112,{ callfunc "QWS_Dungeon_Warpra",12,"Kunlun Dungeon"; close; }
juperos_02,127,154,5	script	Warpra Helper#13	112,{ callfunc "QWS_Dungeon_Warpra",14,"Juperos Cave"; close; }
kh_dun01,14,224,3	script	Warpra Helper#14	112,{ callfunc "QWS_Dungeon_Warpra",28,"Kiel Dungeon"; close; }
lhz_dun02,156,151,5	script	Warpra Helper#15	112,{ callfunc "QWS_Dungeon_Warpra",15,"Lighthalzen Bio Lab"; close; }
lou_dun02,168,264,4	script	Warpra Helper#16	112,{ callfunc "QWS_Dungeon_Warpra",16,"Luoyang Dungeon"; close; }
mag_dun02,46,41,3	script	Warpra Helper#17	112,{ callfunc "QWS_Dungeon_Warpra",17,"Magma Dungeon"; close; }
odin_tem01,115,148,3	script	Warpra Helper#18	112,{ callfunc "QWS_Dungeon_Warpra",18,"Odin Temple"; close; }
orcsdun01,185,11,3	script	Warpra Helper#19	112,{ callfunc "QWS_Dungeon_Warpra",19,"Orc Dungeon"; close; }
pay_dun03,162,143,3	script	Warpra Helper#20	112,{ callfunc "QWS_Dungeon_Warpra",20,"Payon Dungeon"; close; }
moc_pryd02,101,95,3	script	Warpra Helper#21	112,{ callfunc "QWS_Dungeon_Warpra",21,"Pyramides Dungeon"; close; }
in_sphinx2,274,268,1	script	Warpra Helper#22	112,{ callfunc "QWS_Dungeon_Warpra",22,"Sphinx Dungeon"; close; }
tha_t07,111,162,3	script	Warpra Helper#23	112,{ callfunc "QWS_Dungeon_Warpra",24,"Thanatos Tower"; close; }
treasure02,104,40,3	script	Warpra Helper#24	112,{ callfunc "QWS_Dungeon_Warpra",23,"Sunken Ship Dungeon"; close; }
xmas_dun02,124,131,3	script	Warpra Helper#25	112,{ callfunc "QWS_Dungeon_Warpra",25,"Toy Factory Dungeon"; close; }
um_dun02,44,28,3	script	Warpra Helper#26	112,{ callfunc "QWS_Dungeon_Warpra",27,"Umbala Dungeon"; close; }
tur_dun02,162,23,3	script	Warpra Helper#27	112,{ callfunc "QWS_Dungeon_Warpra",26,"Turtle Island Dungeon"; close; }
prt_maze02,102,69,4	script	Warpra Helper#28	112,{ callfunc "QWS_Dungeon_Warpra",13,"Hidden Dungeon"; close; }
