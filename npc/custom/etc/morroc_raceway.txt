//===== rAthena Script =======================================
//= Morocc Raceway
//===== By: ==================================================
//= acky
//===== Current Version: =====================================
//= 1.3
//===== Compatible With: =====================================
//= rAthena Project
//===== Description: =========================================
//= Lets players race around Morocc (pvp_y_1-5)
//===== Additional Comments: =================================
//= If there are more than 3 players, at least 3 people
//= must finish before a new race can be started.
//=
//= If there are less than 3 players, at least 1 person
//= must finish before a new race can be started.
//=
//= Removed permanent global variables
//= 1.2 Removed Duplicates [Silent]
//= 1.3 Replaced effect numerics with constants. [Samuray22]
//============================================================

//Warps you into race way
morocc,166,105,6	script	Race Girl#01	116,{
	mes "[Race Girl]";
	mes "Would you like to visit ^0000FFMorocc Raceway^000000?";
	next;
	menu "Yes",L_Warp,"No",-;
	mes "[Race Girl]";
	mes "Alright, talk to me again when you want to go.";
	close;
L_Warp:
	warp "pvp_y_1-5",165,256;
	close;
}

//Warps you out of raceway
pvp_y_1-5,169,265,5	script	Race Girl#02	116,{
	mes "[Race Girl]";
	mes "Welcome to Morocc Raceway!";
	next;
	menu "Information",-,"Leave",L_Warp,"Cancel",L_Cancel;
	mes "[Race Girl]";
	mes "Someone must click on the Starter NPC to start the race.";
	next;
	mes "[Race Girl]";
	mes "Once the race is started, run around Morocc anti-clockwise.";
	next;
	mes "[Race Girl]";
	mes "You must reach all the checkpoints - No cheating!";
	close;
L_Warp:
	warp "morocc",165,101;
L_Cancel:
	mes "[Race Girl]";
	mes "Come again soon!";
	close;
}

//Counts down and starts race
pvp_y_1-5,145,269,5	script	Starter	733,{
	if ($@race != 0) goto L_Started;
	if ($@counting != 0) goto L_Started;
	if ($@racecount == 1) goto L_Started;
L_Menu:
	mes "[Race Starter]";
	mes "Please stay on the Eastern side of me.";
	menu "Start Race",L_Count,"Cancel",-;
	close;
L_Count:
	set $@counting,1;
	mes "Counting down...";
	addtimer 1000, "Starter::OnCount1000";
	addtimer 2000, "Starter::OnCount2000";
	addtimer 3000, "Starter::OnCount3000";
	addtimer 4000, "Starter::OnCount4000";
	announce strcharinfo(0) + "Started a countdown",1;
	announce "Get ready to race!",1;
	close;

OnCount1000:
	announce "[3]",1;
	end;
OnCount2000:
	announce "[2]",1;
	end;
OnCount3000:
	announce "[1]",1;
	end;
OnCount4000:
	emotion ET_GO;
	specialeffect EF_CHIMTO;
	announce "[GO!]",1;
	set $@race,1;
	set $@position,0;
	set $@counting,0;
	set $@raceid,rand(100000,999999);
	end;

L_Started:
	if ((getmapusers("pvp_y_1-5") < 3) && ($@position > 0)) goto L_Menu;
	if ($@position > 2) goto L_Menu;
	mes "[Starter]";
	mes "Race in progress";
	close;

OnInit:
	set $@race,0;
	set $@position,0;
	set $@racecount,0;
	end;
}

//Checkpoint 1
pvp_y_1-5,144,262,5	script	Check Point 1	111,0,5,{
	end;
OnTouch:
	if (@raceid != $@raceid) goto L_Started;
	if (@race == 6) goto L_Finished;
	if ($@race == 1) goto L_Started;
	mes "The race has not started, please move back.";
	close;
L_Started:
	set @race,1;
	set @raceid,$@raceid;
	end;
L_Finished:
	mes "You have already completed the race.";
	close;
}

//Checkpoint 2
pvp_y_1-5,73,247,5	script	Check Point 2	111,6,6,{
	end;
OnTouch:
	if (@race != 1) goto L_Miss;
	set @race,2;
	announce "[" + strcharinfo(0) +"] has reached Checkpoint [1]",1;
	end;
L_Miss:
	mes "You have missed a Checkpoint. Please go back.";
	close;
}

//Checkpoint 3
pvp_y_1-5,77,44,5	script	Check Point 3	111,6,6,{
	end;
OnTouch:
	if (@race != 2) goto L_Miss;
	set @race,3;
	announce "[" + strcharinfo(0) +"] has reached Checkpoint [2]",1;
	end;
L_Miss:
	mes "You have missed a Checkpoint. Please go back.";
	close;
}

//Checkpoint 3
pvp_y_1-5,249,60,5	script	Check Point 4	111,6,6,{
	end;
OnTouch:
	if (@race != 3) goto L_Miss;
	set @race,4;
	announce "[" + strcharinfo(0) +"] has reached Checkpoint [3]",1;
	end;
L_Miss:
	mes "You have missed a Checkpoint. Please go back.";
	close;
}

//Checkpoint 4
pvp_y_1-5,255,256,5	script	Check Point 5	111,6,6,{
	end;
OnTouch:
	if (@race != 4) goto L_Miss;
	set @race,5;
	announce "[" + strcharinfo(0) +"] has reached Checkpoint [4]",1;
	end;
L_Miss:
	mes "You have missed a Checkpoint. Please go back.";
	close;
}

//Finish Line
pvp_y_1-5,174,244,5	script	Finish Line	111,6,6,{
	end;
OnTouch:
	if (@raceid != $@raceid) goto L_WrongRace;
	if (@race != 5) goto L_Miss;
	set @race,6;
	set $@position,$@position+1;
	announce "[" + strcharinfo(0) +"] has reached The Finish line! [Position: " + $@position + "]",1;
	end;
L_Miss:
	mes "You have missed a Checkpoint. Please go back.";
	close;
L_WrongRace:
	mes "You are not in this race.";
	close;
}

//Check Point Marker Flags
pvp_y_1-5,144,267,4	script	Check Point 1#01	722,{
	end;
}
pvp_y_1-5,144,257,4	script	Check Point 1#02	722,{
	end;
}
pvp_y_1-5,70,252,3	script	Check Point 2#01	722,{
	end;
}
pvp_y_1-5,77,243,3	script	Check Point 2#02	722,{
	end;
}
pvp_y_1-5,81,48,1	script	Check Point 3#01	722,{
	end;
}
pvp_y_1-5,72,40,1	script	Check Point 3#02	722,{
	end;
}
pvp_y_1-5,244,65,7	script	Check Point 4#01	722,{
	end;
}
pvp_y_1-5,252,57,7	script	Check Point 4#02	722,{
	end;
}
pvp_y_1-5,259,260,5	script	Check Point 5#01	722,{
	end;
}
pvp_y_1-5,251,252,5	script	Check Point 5#02	722,{
	end;
}
pvp_y_1-5,174,249,4	script	Finish Line#01	722,{
	end;
}
pvp_y_1-5,174,238,4	script	Finish Line#02	722,{
	end;
}
