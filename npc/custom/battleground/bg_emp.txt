//===== rAthena <PERSON>t =======================================
//= Battleground: Emperium
//===== Description: =========================================
//= A simple battleground script:
//= Destroy the opponent's Emperium to win the match.
//===== Changelogs: ==========================================
//= 1.0 First version, edited. [Euphy] [AnnieRuru]
//= 1.1 Use up to date battleground script commands [<PERSON><PERSON><PERSON>]
//= 1.2 Fix player can be kill multiple times within 250ms time frame [Annie<PERSON><PERSON>]
//============================================================

-	script	bg_emp#control	-1,{
OnInit:
	.minplayer2start = 1;      // minimum players to start (ex. if 3vs3, set to 3)
	.eventlasting    = 20*60;  // event duration before auto-reset (20 minutes * seconds)
	setarray .rewarditem[0],   // rewards for the winning team: <item>,<amount>,...
		501, 10;
	end;
OnStart:
	if ( getwaitingroomstate( 0, .rednpcname$ ) < .minplayer2start || getwaitingroomstate( 0, .bluenpcname$ ) < .minplayer2start )
		end;

	// create Battleground and teams
	.red = waitingroom2bg( "-", 0,0, strnpcinfo(0)+"::OnRedQuit", strnpcinfo(0)+"::OnRedDead", .rednpcname$ );
	.blue = waitingroom2bg( "-", 0,0, strnpcinfo(0)+"::OnBlueQuit", strnpcinfo(0)+"::OnBlueDead", .bluenpcname$ );
	delwaitingroom .rednpcname$;
	delwaitingroom .bluenpcname$;
	setwall "bat_a01", 164,347, 6, 4, 0, "bg_emp_town_red";
	setwall "bat_a01", 154,51, 6, 4, 0, "bg_emp_town_blue";
	bg_warp .red, "bat_a01", 171,346;
	bg_warp .blue, "bat_a01", 162,50;

	// delay before match begins
	sleep 6000;
	mapannounce "bat_a01", "The rules are simple. The first team to break the opponent's Emperium wins!", bc_map;
	sleep 3000;
	for ( .@i = 5; .@i > 0; --.@i ) {
		mapannounce "bat_a01", "["+ .@i +"]", bc_map;
		sleep 1000;
	}
	mapannounce "bat_a01", "Start!", bc_map;

	// spawn Emperiums
	bg_monster .red,"bat_a01",171,346, "--ja--",1915, strnpcinfo(3)+"::OnRedDown";
	bg_monster .blue,"bat_a01",162,50, "--ja--",1914, strnpcinfo(3)+"::OnBlueDown";
	delwall "bg_emp_town_red";
	delwall "bg_emp_town_blue";

	// match duration
	sleep .eventlasting * 1000;

	// end match, destroy Battleground, reset NPCs
	killmonster "bat_a01", strnpcinfo(3)+"::OnRedDown";
	killmonster "bat_a01", strnpcinfo(3)+"::OnBlueDown";
	if ( .winside ) {
		mapannounce "bat_a01", "- "+( (.winside == .red)? "Red" : "Blue" )+" Team is victorious! -", bc_map;
		bg_get_data .winside, 1;
		for ( .@i = 0; .@i < $@arenamemberscount; ++.@i )
			getitem .rewarditem[0], .rewarditem[1], $@arenamembers[.@i];
	} else
		mapannounce "bat_a01", "- The match has ended in a draw! -", bc_map;
	sleep 5000;
	bg_warp .red, "prontera", 155,182;
	bg_warp .blue, "prontera", 158,182;
	bg_destroy .red;
	bg_destroy .blue;
	.winside = 0;
	donpcevent .rednpcname$ +"::OnStart";
	donpcevent .bluenpcname$ +"::OnStart";
	end;

// Emperium destroyed
OnRedDown:  callsub L_EmpDown, "Red", .blue;
OnBlueDown: callsub L_EmpDown, "Blue", .red;
L_EmpDown:
	mapannounce "bat_a01", strcharinfo(0) +" has destroyed "+ getarg(0) +" Team's Emperium.", bc_map;
	.winside = getarg(1);
	awake strnpcinfo(0);
	end;

// "OnDeath" event
OnRedDead:  callsub L_Dead, 157,347;
OnBlueDead: callsub L_Dead, 142,51;
L_Dead:
	warp "bat_a01", getarg(0), getarg(1);
	percentheal 100,100;
	end;

// "OnQuit" event
OnRedQuit:  callsub L_Quit, .red, "Red";
OnBlueQuit: callsub L_Quit, .blue, "Blue";
L_Quit:
	percentheal 100, 100;
	if ( !bg_get_data( getarg(0), 0 ) )
		mapannounce "bat_a01", "All "+ getarg(1) +" team members have quit!", bc_map, 0xff3333;
	end;
}

prontera,155,182,5	script	Red Team#bg_emp	733,{
	end;
OnInit:
	sleep 1;
	set getvariableofnpc( .rednpcname$, "bg_emp#control" ), strnpcinfo(0);
OnStart:
	waitingroom "Red Team", getvariableofnpc( .minplayer2start, "bg_emp#control" ) +1, "bg_emp#control::OnStart", getvariableofnpc( .minplayer2start, "bg_emp#control" );
	end;
}

prontera,158,182,5	script	Blue Team#bg_emp	734,{
	end;
OnInit:
	sleep 1;
	set getvariableofnpc( .bluenpcname$, "bg_emp#control" ), strnpcinfo(0);
OnStart:
	waitingroom "Blue Team", getvariableofnpc( .minplayer2start, "bg_emp#control" ) +1, "bg_emp#control::OnStart", getvariableofnpc( .minplayer2start, "bg_emp#control" );
	end;
}

bat_a01	mapflag	battleground
bat_a01	mapflag	nosave	SavePoint
bat_a01	mapflag	nowarp
bat_a01	mapflag	nowarpto
bat_a01	mapflag	noteleport
bat_a01	mapflag	nomemo
bat_a01	mapflag	nopenalty
bat_a01	mapflag	nobranch
bat_a01	mapflag	noicewall
bat_a01	mapflag	hidemobhpbar
