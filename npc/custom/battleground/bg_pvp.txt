//===== rAthena Script =======================================
//= Battleground: PVP
//===== By: ==================================================
//= AnnieRuru
//===== Current Version: =====================================
//= 1.1
//===== Compatible With: =====================================
//= rAthena Project
//===== Description: =========================================
//= A simple battleground script:
//= Kill players from the other team.
//===== Additional Comments: =================================
//= 1.0 First version, edited. [Euphy]
//= 1.1 Use up to date battleground script commands [AnnieRuru]
//============================================================

-	script	bg_pvp#control	-1,{
OnInit:
	.minplayer2start = 2;      // minimum players to start (ex. if 3vs3, set to 3)
	.eventlasting    = 20*60;  // event duration before auto-reset (20 minutes * seconds)
	setarray .rewarditem[0],   // rewards for the winning team: <item>,<amount>,...
		501, 10;
	end;
OnStart:
	if ( getwaitingroomstate( 0, .rednpcname$ ) < .minplayer2start || getwaitingroomstate( 0, .bluenpcname$ ) < .minplayer2start )
		end;

	// create Battleground and teams
	.red = waitingroom2bg( "guild_vs3", 13,50, strnpcinfo(0)+"::OnRedQuit", strnpcinfo(0)+"::OnRedDead", .rednpcname$ );
	.blue = waitingroom2bg( "guild_vs3", 86,50, strnpcinfo(0)+"::OnBlueQuit", strnpcinfo(0)+"::OnBlueDead", .bluenpcname$ );
	delwaitingroom .rednpcname$;
	delwaitingroom .bluenpcname$;
	bg_warp .red, "guild_vs3", 13,50;
	bg_warp .blue, "guild_vs3", 86,50;
	.red_score = .blue_score = .minplayer2start;
	bg_updatescore "guild_vs3", .red_score, .blue_score;

	// match duration
	sleep .eventlasting * 1000;

	// end match, destroy Battleground, reset NPCs
	if ( .red_score > .blue_score ) {
		mapannounce "guild_vs3", "- Red Team is victorious! -", bc_map;
		callsub L_Reward, .red;
	}
	else if ( .blue_score > .red_score ) {
		mapannounce "guild_vs3", "- Blue Team is victorious! -", bc_map;
		callsub L_Reward, .blue;
	}
	else
		mapannounce "guild_vs3", "- The match has ended in a draw! -", bc_map;
	bg_warp .red, "prontera",152,178;
	bg_warp .blue, "prontera",154,178;
	bg_destroy .red;
	bg_destroy .blue;
	donpcevent .rednpcname$ +"::OnStart";
	donpcevent .bluenpcname$ +"::OnStart";
	end;

L_Reward:
	bg_get_data getarg(0), 1;
	for ( .@i = 0; .@i < $@arenamemberscount; ++.@i )
		getitem .rewarditem[0], .rewarditem[1], $@arenamembers[.@i];
	return;

// "OnDeath" event
OnRedDead:  callsub L_Dead, .red_score;
OnBlueDead: callsub L_Dead, .blue_score;
L_Dead:
	set getarg(0), getarg(0) -1;
	bg_updatescore "guild_vs3", .red_score, .blue_score;
	bg_leave;
	if ( !getarg(0) )
		awake strnpcinfo(0);
	sleep2 1250;
	percentheal 100,100;
	end;

// "OnQuit" event
OnRedQuit:  callsub L_Quit, .red_score;
OnBlueQuit: callsub L_Quit, .blue_score;
L_Quit:
	set getarg(0), getarg(0) -1;
	bg_updatescore "guild_vs3", .red_score, .blue_score;
	percentheal 100, 100;
	if ( !getarg(0) )
		awake strnpcinfo(0);
	end;
}

prontera,152,178,5	script	Red Team#bg_pvp	733,{
	end;
OnInit:
	sleep 1;
	set getvariableofnpc( .rednpcname$, "bg_pvp#control" ), strnpcinfo(0);
OnStart:
	waitingroom "Red Team", getvariableofnpc( .minplayer2start, "bg_pvp#control" ) +1, "bg_pvp#control::OnStart", getvariableofnpc( .minplayer2start, "bg_pvp#control" );
	end;
}

prontera,154,178,5	script	Blue Team#bg_pvp	734,{
	end;
OnInit:
	sleep 1;
	set getvariableofnpc( .bluenpcname$, "bg_pvp#control" ), strnpcinfo(0);
OnStart:
	waitingroom "Blue Team", getvariableofnpc( .minplayer2start, "bg_pvp#control" ) +1, "bg_pvp#control::OnStart", getvariableofnpc( .minplayer2start, "bg_pvp#control" );
	end;
}

guild_vs3	mapflag	gvg	off
guild_vs3	mapflag	battleground	2
guild_vs3	mapflag	nosave	SavePoint
guild_vs3	mapflag	nowarp
guild_vs3	mapflag	nowarpto
guild_vs3	mapflag	noteleport
guild_vs3	mapflag	nomemo
guild_vs3	mapflag	nopenalty
guild_vs3	mapflag	nobranch
guild_vs3	mapflag	noicewall
guild_vs3	mapflag	hidemobhpbar
