//===== rAthena <PERSON> =======================================
//= Assassin Job Quest
//===== Description: =========================================
//= [Official Conversion]
//= Job Change quest for Assassin class.
//===== Changelog: ===========================================
//= 1.0 Used some dialogue from the aegis Assassin script translated By: Pgro Team (OwNaGe)
//=     Also converted the booby traps from the aegis script.[kobra_k88]
//= 1.1 Made adjustments to ontouch npcs to work with the new "ontouch" functionality.
//=     Fixed some duplicate npc names.  Added missing waitingroom triggers.
//=     Fixed warp in "TimerSin", was supposed to be an areawarp.
//=     For some reason sometimes the "Nameless One" would have message windows
//=     without controls.  Changed the doevent that triggers him to an
//=     addtimer and that seemed make the prob. go away[kobra_k88]
//= 1.2 Fixed WRONG skillpoint check! [Lupus]
//= 1.2b Fixed missing commands and typos [Lupus]
//= 1.3 Baby class Support added [Lupus]
//= 1.5 Fixed possible EXP abuse [Lupus]
//= 1.6 Added a func which prevent advanced classes passing
//=     2nd Job Quests again. It also guides adv classes where
//=     to go. [Lupus]
//= 2.0 Changed numbers to constants. [Vicious]
//= 2.1 Removed Duplicates [Silent]
//= 2.2 Merged back JFunc. Fixed missing dialogue [Lupus]
//= 2.3 Fixed typo and change Nameless addtimer to 100 (with 700ms I
//=     was still able to bypass it) [Toms]
//= 2.4 Fixed skipping of Nameless NPC [Lupus]
//= 2.5 Fixed a Rogue exploit [Lupus]
//= 2.6 Rescripted to Aegis 10.3 standards. [L0ne_W0lf]
//= 2.7 Several bugfixes with the help of Samuray22 and Barron-Monster. [L0ne_W0lf]
//= 2.8 Removed duplicate warps. [L0ne_W0lf]
//= 2.8a Deleted unused variables. [Samuray22]
//= 2.8b Fixed error caused in last updated, removed useless variable 'joblvl'. [L0ne_W0lf]
//= 2.9 Fixed an indent and added a 'break;' in hopes of 'fixing' an error [L0ne_W0lf]
//= 2.9a Corrected a Typo error ";;". [Samuray22]
//= 2.9b Moved a wrong 'break'. [Zephyrus_cr]
//= 3.0 Now uses enable and disable waitingroomevent. [L0ne_W0lf]
//= 3.1 Fixed problem with waiting room. (bugreport:1099) [L0ne_W0lf]
//= 3.1a Follow up fix. Forgot to replace a label. [L0ne_W0lf]
//= 3.1b Fixed waiting room not enabled in some situations [Zephyrus]
//= 3.2 Fixed a question having all correct answers. [brianluau]
//= 3.3 Added Quest Log commands. [Kisuka]
//= 3.4 Removed the need for use of 'goto.' [L0ne_W0lf]
//= 3.5 Fixed the waitingroom where player can enter the Test Area without passing the written exam. Now player will not be warp inside the Test Area when did not passed the written exam instead the player will be warp back to the written exam entrance [JayPee]
//= 3.6 Fixed broken event calls. [Euphy]
//= 3.7 Fixed issue that the player can't progress in the quest if timed out at weapon reward step. This issue is fixed on iRO but not on kRO. [https://forums.warpportal.com/index.php?/topic/130060-didnt-receive-necklace-of-oblivion-when-changing-assassin/#entry1496042] [MrAntares]
//= 3.8 Another 'unofficial fix'. Player can't change job if time out after the necklace is deleted. [Atemo]
//============================================================

in_moc_16,19,33,1	script	Guildsman#asn	55,{
	if (Upper == 1) {
		mes "[Ferocious-looking guy]";
		mes "Hm? You....?";
		mes "I sense that you're different than most people...";
		next;
		mes "[Ferocious-looking guy]";
		mes "I've never met anyone as intimidating as you! For some reason, I don't like you. I think you should leave!";
		close;
	}
	if (SkillPoint) {
		mes "[Ferocious-looking guy]";
		mes "You can't change your job if you have any unused skill points from the 1st job. You better go and use those up first.";
		close;
	}
	if (ASSIN_Q == 4) {
		mes "[Ferocious-looking guy]";
		mes "Oh, stop making that face. Can you really be in that much pain?";
		next;
		mes "[Ferocious-looking guy]";
		mes "Wah wah wah, you're hurting, I can see that. Look, I'll restore HP and SP. Happy?";
		percentheal 100,100;
		next;
		mes "[Ferocious-looking guy]";
		mes "Is it that hard to stay alive?";
		mes "Why don't you try harder next time? You can't force yourself too hard to become an Assassin...";
		next;
		if (select("I will become an Assassin no matter what!:Oh man, I gotta take a break.") == 1) {
			mes "[Ferocious-looking guy]";
			mes "Oh...";
			mes "Well then,";
			mes "go for it!";
			close2;
			ASSIN_Q = 0;
			warp "in_moc_16",19,76;
			end;
		}
		mes "[Ferocious-looking guy]";
		mes "Take a break? Oh alright, have it your way. When you feel like you're ready to become an Assassin, come back.";
		next;
		mes "[Ferocious-looking guy]";
		mes "You'll have to walk if you want to get back to town. Oh, and don't forget to save your spawn point, alright?";
		close2;
		ASSIN_Q = 0;
		ASSIN_Q2 = 0;
		if (ASSIN_Q3 < 3)
			ASSIN_Q3 = 0;
		savepoint "in_moc_16",18,14,1,1;
		warp "in_moc_16",18,14;
		end;
	}
	if (BaseJob == Job_Thief && countitem(1008) == 0 && ASSIN_Q > 7) {
		mes "[Assassin Expert 'Huey']";
		mes "Hey, what happened...? How come you didn't bring the ^006699Necklace of Oblivion^000000? You're supposed to carry that with you, so where is it?";
		next;
		mes "[Assassin Expert 'Huey']";
		mes "You get better get that ^006699Necklace of Oblivion^000000 again before the guildmaster finds out! Hurry, and do your best to get it!";
		next;
		mes "[Assassin Expert 'Huey']";
		mes "When you finally succeed in getting it, bring it to me! ^666666*Sigh...*^000000";
		close;
	}
	if (BaseJob == Job_Thief && countitem(1008) > 0 && ASSIN_Q > 7) {
		mes "[Assassin Expert 'Huey']";
		mes "Well well well, you got it. Congratulations! But since it's been clearly scratched, I can't accept it. You'll never become an Assassin!";
		next;
		mes "[Assassin Expert 'Huey']";
		mes "Hahahah~! I'm just joking, don't take it seriously. But I do need to check this necklace with the guildmaster first.";
		next;
		mes "[Assassin Expert 'Huey']";
		mes "...";
		next;
		mes "[Assassin Expert 'Huey']";
		mes "...";
		mes "......";
		next;
		mes "[Assassin Expert 'Huey']";
		mes "Alright!";
		mes "You've been approved!";
		next;
		delitem 1008,1; //Frozen_Heart
		changequest 8007,8008;
		completequest 8008;
		callfunc "Job_Change",Job_Assassin;
		callfunc "F_ClearJobVar";		// clears all job variables for the current player
		mes "[Assassin Expert 'Huey']";
		mes "Now! Do your best to be a great Assassin! Travel with faith and kill with dignity. Come by anytime and pay us a visit. Once again, congratulations.";
		close;
	}
	else if (countitem(1008) > 0 && BaseJob == Job_Thief && ASSIN_Q < 7) {
		mes "[Ferocious-looking guy]";
		mes "Eh?";
		mes "What do you want?";
		next;
		mes "[Ferocious-looking guy]";
		mes "I see you're carrying a ^006699Necklace of Oblivion^000000... You want to become an Assassin, don't you? Let me check it...";
		next;
		mes "[Ferocious-looking guy]";
		mes "...";
		next;
		mes "[Ferocious-looking guy]";
		mes "...";
		mes "......";
		next;
		mes "[Ferocious-looking guy]";
		mes "Wait a second...";
		mes "Why you no good BASTARD! THIS IS A FAKE!";
		next;
		mes "[Ferocious-looking guy]";
		mes "How dare you think of trying to trick me with a fake! Are you stupid or what!? I should kill you...";
		close2;
		warp "moc_fild16",206,229;
		end;
	}
	else {
		mes "[Ferocious-looking guy]";
		mes "What brings you here?";
		mes "I don't think I like the way you're looking at me... Punk.";
		next;
		if (BaseClass == Job_Novice) {
			mes "[Ferocious-looking guy]";
			mes "Hey Newbie. You should really get out of here as soon as you can. I can't guarantee your safety.";
			close;
		}
		else if (BaseClass == Job_Swordman) {
			mes "[Ferocious-looking guy]";
			mes "What brings a man of the sword to this place? Why don't you try smashing stuff somewhere else, ya lunkhead.";
			close;
		}
		else if (BaseClass == Job_Mage) {
			mes "[Ferocious-looking guy]";
			mes "Now what would a magic user be doing here?";
			next;
			mes "[Ferocious-looking guy]";
			mes "There's a library in Prontera and Juno where you're welcome, so why don't you make like a magic trick and disappear?";
			close;
		}
		else if (BaseClass == Job_Archer) {
			mes "[Ferocious-looking guy]";
			mes "Well well well.";
			mes "Look at that purdy bow.";
			next;
			mes "[Ferocious-looking guy]";
			mes "There aren't many Bowmen with the gall to even come close to this place. Well, what do you think you're doin' here?!";
			close;
		}
		else if (BaseClass == Job_Acolyte) {
			mes "[Ferocious-looking guy]";
			mes "I thought something smelled funny. What's a servant of God doing in this place? You don't belong here.";
			close;
		}
		else if (BaseClass == Job_Merchant) {
			mes "[Ferocious-looking guy]";
			mes "We don't like greedy people around these parts. You better sell your stuff somewhere else, Moneybags.";
			close;
		}
		else if (BaseJob == Job_Rogue) {
			mes "[Ferocious-looking guy]";
			mes "You look like you don't have a care in the world. Well, I hope you enjoy your rest while you stay here. It's okay, since the Rogue and Assassin Guilds have always gotten along pretty well.";
			next;
			mes "[Ferocious-looking guy]";
			mes "By the way...";
			mes "Have you ever seen";
			mes "a girl named Markie?";
			next;
			mes "[Ferocious-looking guy]";
			mes "Markie...";
			mes "We promised that we'd be together forever. ^666666*Sigh...*^000000 I don't even think she remembers that promise anymore. Then again, we were pretty young back then...";
			close;
		}
		else if (BaseJob == Job_Assassin) {
			mes "[Assassin Expert 'Huey']";
			mes "Hey, I remember you~";
			mes "Wasn't your name, umm, I remember 'cause it sounded funny to me...";
			next;
			mes "[Assassin Expert 'Huey']";
			mes ":+:" + strcharinfo(0) + ":+:, right? No wait, just "+ strcharinfo(0) +". Yeah, how's it goin'?";
			next;
			mes "[Assassin Expert 'Huey']";
			mes "Unfortunately, I don't have any requests for you at this time from the guild. Just keep focusing on your training. Till then, see ya.";
			close;
		}
		else if (BaseJob == Job_Thief && JobLevel > 39) {
			if (SkillPoint) {
				mes "[Ferocious-looking guy]";
				mes "You can't change your job if you still have unused skill points from First Job. You better use up those skill points first.";
				close;
			}
			else {
				mes "[Ferocious-looking guy]";
				mes "Hmm...";
				mes "A Thief...?";
				next;
				mes "[Ferocious-looking guy]";
				mes "And a well-experienced Thief since I can't seem to find my wallet. We do need people like you, you know.";
				next;
				mes "[Ferocious-looking guy]";
				mes "So how about taking the next step and becoming an Assassin?";
				next;
				switch(select("Yes. I've picked my last pocket.:What's the requirements?:Maybe later, I need to steal some things first.")) {
				case 1:
					mes "[Ferocious-looking guy]";
					mes "It's been a while since I've received a guest. I'm sending";
					mes "you to the office.";
					close2;
					ASSIN_Q = 0;
					if (checkquest(8000) != -1) {
						changequest 8000,8001;
					} else {
						setquest 8001;
					}
					warp "in_moc_16",19,76;
					end;
				case 2:
					mes "[Ferocious-looking guy]";
					mes "Requirements? Well, first you need to be a Thief. Second, you need to be at least Thief job level 40.";
					next;
					mes "[Ferocious-looking guy]";
					mes "And third, you need to pass a test to become an Assassin. You got";
					mes "all that? If you're sure of your ability as a Thief, you won't have to worry.";
					close;
				case 3:
					mes "[Ferocious-looking guy]";
					mes "Hmm...";
					mes "Alright then.";
					mes "But come back when";
					mes "you think you're ready.";
					close;
				}
			}
		}
		else {
			mes "[Ferocious-looking guy]";
			mes "Huh. You're not qualified to become an Assassin yet. There are requirements you need to meet first, you know.";
			next;
			mes "[Ferocious-looking guy]";
			mes "Well, keep training. You need to be at least job level 40, got it? But if you're above job level 40, that will probably be even better.";
			close;
		}
	}
}

in_moc_16,25,90,1	script	Guildsman#ASN2	730,2,2,{
	mes "[Assassin 'Khai']";
	mes "Umm?!";
	emotion ET_SURPRISE;
	next;
	mes "[Assassin 'Khai']";
	mes "Come closer. I prefer to talk to people face to face. It really irritates me if I have to raise my voice, just so you can hear me.";
	mes "I feel irritated when somebody talks to me behind my back.";
	close;

OnTouch_:
	if (ASSIN_Q2 == 4) {
		mes "[Assassin 'Khai']";
		mes "Ehhh?";
		mes "Didn't you just";
		mes "pass me a minute ago?";
		next;
		mes "[Assassin 'Khai']";
		mes "Eh...?!";
		mes "You failed?";
		mes "Even on the";
		mes "writing test?";
		mes "Bwahahahahaha!";
		next;
		mes "[Assassin 'Khai']";
		mes "Well...";
		mes "It's been a long time since";
		mes "I've met such a big failure.";
		next;
		mes "[Assassin 'Khai']";
		mes "HAH!";
		mes "Hahahahah~!";
		mes "Oh, you're killing me....";
		next;
		mes "[Assassin 'Khai']";
		mes "Sorry for laughing, but this is hilarious! Hahaha~ So do you want me to give you some hints?";
		next;
		switch(select("I beg you, give me hints.:Don't laugh at me! Now, give me hints!:...Shut up, I don't need your help!")) {
		case 1:
			mes "[Assassin 'Khai']";
			mes "Haaahahahaha!!!";
			mes "Well well, aren't we honest. You're not even an Assassin yet, but you're killing me, I tell you, killing me!";
			next;
			mes "[The Anonymous One]";
			mes "Ho ho ho...";
			next;
			mes "[Assassin 'Khai']";
			mes "Did you hear that Anonymous one?! 'I beg you, give me hints.' Hahahah!";
			next;
			mes "[The Anonymous One]";
			mes "Yes.";
			mes "This one is quite hilarious";
			mes "in a pathetic sort of way.";
			next;
			mes "[Assassin 'Khai']";
			mes "Hahahahahahah!";
			mes "Soooooo, you wanted";
			mes "some hints, right?";
			next;
			mes "[Assassin 'Khai']";
			mes "...";
			next;
			mes "[Assassin 'Khai']";
			mes "...";
			mes "......";
			next;
			mes "[Assassin 'Khai']";
			mes "...";
			mes "......";
			mes ".........";
			next;
			mes "[Assassin 'Khai']";
			mes "...";
			mes "......";
			mes ".........";
			mes "............";
			next;
			mes "[Assassin 'Khai']";
			mes "...";
			mes "......";
			mes ".........";
			mes "............";
			mes "...............";
			next;
			mes "[Assassin 'Khai']";
			mes "Nah.";
			mes "I changed my mind!";
			mes "I'm not gonna give you any hints after all. Hee hee hee~";
			close;
		case 2:
			mes "[Assassin 'Khai']";
			if (Sex == SEX_MALE) {
				mes "Huh. You must have a lot of self confidence to be a Thief nowadays.";
				next;
				mes "[Assassin 'Khai']";
				mes "Yeah yeah, I understand. Everyone messes up from time to time. Sorry for laughing at your mistakes.";
			}
			else
				mes "Hmm. I like your attitude. You should keep your pride as a Thief. Sorry for laughing at your mistakes. I think you'll do better next time.";
			next;
			mes "[Assassin 'Khai']";
			mes "I'm not allowed to give you hints, I can tell you more about being an Assassin...";
			next;
			mes "[Assassin 'Khai']";
			mes "Above all else, we value our dignity. We're Assassins, after all and people will need us.";
			next;
			mes "[Assassin 'Khai']";
			mes "If people are close to you in some way, they might not understand what I'm saying. We're born to be loners due to our nature.";
			next;
			mes "[Assassin 'Khai']";
			mes "Imagine if a lover or a friend saw the blood on your hands. There's a chance that they might not stay with you.";
			next;
			mes "[Assassin 'Khai']";
			mes "Sometimes it gets lonely but it's not that bad. At least I can do what I want to do, you know, and do things my way.";
			next;
			mes "[Assassin 'Khai']";
			mes "Well, that's all I can tell you for now. Does being an Assassin";
			mes "seem depressing to you?";
			close;
		case 3:
			mes "[Assassin 'Khai']";
			mes "...Hm.";
			next;
			mes "[Assassin 'Khai']";
			mes "Right, that's the spirit. Don't ever let anyone else look down";
			mes "on you. We're Assassins...";
			next;
			mes "[Assassin 'Khai']";
			mes "I apologize for laughing at you earlier. I want you to remember to keep that sense of pride and dignity as an Assassin.";
			next;
			mes "[Assassin 'Khai']";
			mes "Along with keeping your pride,";
			mes "I want that you respect the blood that may stain your Katar or Dagger.";
			next;
			if (select("...Got you.:...I'm confused.") == 1) {
				mes "[Assassin 'Khai']";
				mes "Yeah, I can trust you now. Let me give you some important tips.";
				next;
				switch(rand(1,3)) {
				case 1:
					mes "[Assassin 'Khai']";
					mes "First of all, Grimtooth is ...A skill specifically for the Katar. Therefore, it doesn't require any skills related to Dagger weapons.";
					next;
					mes "[Assassin 'Khai']";
					mes "Double attack ...Haven't you tried it? It allows you to attack an enemy twice at a time.";
					next;
					mes "[Assassin 'Khai']";
					mes "Red Blood is an elemental stone, Blue Gemstone doesn't have to do the Assassin job at all!";
					next;
					mes "[Assassin 'Khai']";
					mes "Have you ever seen Mages hunt Elder willow using the Cold Bolt skill? Water overpowers Fire. Water puts Fire under control, and Wind puts water under control.";
					next;
					mes "[Assassin 'Khai']";
					mes "As long as you stick close to the shadows, by walls and things like that, Cloaking will hide you from sight perfectly! Unless some bastard uses a certain detecting skill, you know.";
					next;
					break;
				case 2:
					mes "[Assassin 'Khai']";
					mes "'Sharpened Legbone of Ghoul' possesses the Undead property.";
					next;
					mes "[Assassin 'Khai']";
					mes "What kind of weapon have you used so far? Damascus? Gladius? Stiletto? Or Main Gauche? What is that you're carrying now?";
					next;
					mes "[Assassin 'Khai']";
					mes "It's possible to get a slotted Katar from Desert Wolf. Well, just keep that in mind. You will need this information someday.";
					next;
					mes "[Assassin 'Khai']";
					mes "You can gain a slotted Jur from a buddy living in a dark and damp place under the ground. Well, I have no idea why that dude has that weapon... Maybe he needs it to dig a hole?";
					next;
					mes "[Assassin 'Khai']";
					mes "And...";
					mes "I've always wanted a frog as a pet. But it's impossible!";
					next;
					mes "[Assassin 'Khai']";
					mes "As far as I know, a Goblin carrying a hammer possesses the Earth property. Keep in mind that Fire overcomes the Earth property.";
					next;
					mes "[Assassin 'Khai']";
					mes "You know elemental weapons? The names of Blacksmiths are engraved on them usually...";
					next;
					break;
				case 3:
					mes "[Assassin 'Khai']";
					mes "Sell an Elder Willow Card to a Mage as soon as you can. They are mad about the card for some reason. Doesn't it increase the INT of a character? Hmmm...";
					next;
					mes "[Assassin 'Khai']";
					mes "For us, Dodge and Attack is more important than defense. Don't ever think about wearing a helm. It's heavy, uncomfortable and will even block your sight.";
					next;
					mes "[Assassin 'Khai']";
					mes "'Increase Dodge' allows you to have +3% flee rate per skill lvl.";
					next;
					mes "[Assassin 'Khai']";
					mes "As I have told you repeatedly: Katar class weapons (Jamadhar/Jur/Katar etc) are two-handed!";
					next;
					mes "[Assassin 'Khai']";
					mes "City of desert... I miss my hometown, Morocc. I haven't been there for a long time. I feel like I became a Thief a few days ago. Time flies so fast...";
					next;
					mes "[Assassin 'Khai']";
					mes "Heh. I remember my Thief quest. I was so damn nervous when I broke into the farm to get Mushrooms...";
					next;
					mes "[Assassin 'Khai']";
					mes "Insects detect hiding/cloaking skills. Their feelers never fail to find targets.";
					next;
					mes "[Assassin 'Khai']";
					mes "I've heard that the Baphomet Jr. Card adds +3 points to Agility and +1 point on Critical Attack...";
					next;
					mes "[Assassin 'Khai']";
					mes "Yeah, we Assassins specialize in training Agility. We can gain a bonus of 10 Agility points even before mastering job level. The problem is it won't go up anymore after that, you know.";
					next;
				}
				mes "[Assassin 'Khai']";
				mes "^666666*Phew*^000000 That's all I can tell you, though that's a lot of hints. I don't doubt that I told you almost everything.";
				next;
				mes "[Assassin 'Khai']";
				mes "Well then, go ask to take the test again with 'The Anonymous.'";
				close2;
				warp "in_moc_16",19,144;
				end;
			}
			mes "[Assassin 'Khai']";
			mes "^666666*Sigh...*^000000";
			mes "How can you not understand the concept of dignity? You just showed some to me just now!";
			next;
			mes "[Assassin 'Khai']";
			mes "Oh, I get it. It wasn't pride you were showing, you were just being a jerk!";
			next;
			mes "[Assassin 'Khai']";
			mes "Grrrrr...";
			mes "WARP PORTAL!";
			close2;
			warp "c_tower4",64,76;
			end;
		}
	}
	else {
		mes "[Assassin 'Khai']";
		mes "Oh, you must be an Assassin trainee. You are here to become";
		mes "an Assassin, aren't you?";
		next;
		if (select("Yes, I am. :...No, I'm not.") == 1) {
			mes "[Assassin 'Khai']";
			mes "Okay, good. Let's fill out the application form. Please sign your name and include your job level.";
			next;
			mes "[Assassin 'Khai']";
			mes "Let's see.";
			mes "Your name is";
			mes "" + strcharinfo(0) + "...";
			mes "Job level " + JobLevel + "...";
			next;
			if (JobLevel > 48) {
				mes "[Assassin 'Khai']";
				mes "Wait, Job level " + JobLevel + "?! I can see you've been training pretty hard! My bosses will like this~";
				next;
				mes "[Assassin 'Khai']";
				mes "Did you finish the form? Alright, go ahead and give it to me. Give me a second and I'll transport you to the Test Hall.";
				next;
				mes "[Assassin 'Khai']";
				mes "Alright then,";
				mes "best of luck to you!";
				close2;
				if (ASSIN_Q3 < 3)
					ASSIN_Q3 = 1;
				ASSIN_Q = 1;
				changequest 8001,8002;
				warp "in_moc_16",19,144;
				end;
			}
			else if (JobLevel < 49) {
				mes "[Assassin 'Khai']";
				mes "Well, you passed";
				mes "the requirements.";
				mes "Not bad at all.";
				next;
				mes "[Assassin 'Khai']";
				mes "Go ahead and give";
				mes "me the form when you're";
				mes "done filling it out.";
				mes "Alright, thanks.";
				next;
				mes "[Assassin 'Khai']";
				mes "I'll transport you";
				mes "to the Test Hall.";
				mes "Best of luck~";
				close2;
				if (ASSIN_Q3 < 3)
					ASSIN_Q3 = 2;
				ASSIN_Q = 1;
				changequest 8001,8002;
				warp "in_moc_16",19,144;
				end;
			}
			else {
				mes "[Assassin 'Khai']";
				mes "Who the";
				mes "hell are you?";
				mes "...Guards!";
				close2;
				warp "moc_fild16",206,229;
				end;
			}
		}
		else {
			mes "[Assassin 'Khai']";
			mes "Huh...?";
			mes "What, are you trying to trick me or something? Don't you wanna be an Assassin?";
			next;
			if (select("No.:Yes, I want to be an Assassin.") == 1) {
				mes "[Assassin 'Khai']";
				mes "Eh, get outta here.";
				mes "Stop wastin' my time...";
				close2;
				ASSIN_Q = 0;
				ASSIN_Q2 = 0;
				erasequest 8001;
				warp "moc_fild16",206,229;
				end;
			}
			mes "[Assassin 'Khai']";
			mes "...";
			mes "What the hell?";
			mes "Okay, then.";
			next;
			mes "[Assassin 'Khai']";
			mes "Fill out the application form with your name and job level.";
			next;
			mes "[Assassin 'Khai']";
			mes "" + strcharinfo(0) + "?";
			mes "That's your name?";
			mes "It sounds funny.";
			mes "Let's see... Job Level " + JobLevel + "...";
			next;
			if (JobLevel > 48) {
				mes "[Assassin 'Khai']";
				mes "Ho? Job Level " + JobLevel + "?! You must have been training really hard. The bosses will like that for sure...";
				next;
				mes "[Assassin 'Khai']";
				mes "Are you done filling out the form? Alright, give it to me so I can send you to the Test Hall. Good luck~";
				next;
				if (ASSIN_Q3 < 3)
					ASSIN_Q3 = 1;
				ASSIN_Q = 1;
				changequest 8001,8002;
				warp "in_moc_16",19,144;
				end;
			}
			else if (JobLevel < 49) {
				mes "[Assassin 'Khai']";
				mes "Not bad. You fulfilled our requirements. Not bad at all. Now are you done filling out the form?";
				next;
				mes "[Assassin 'Khai']";
				mes "Then give me the form so that I can send you to the Test Hall, alright?";
				mes "Good luck...";
				next;
				if (ASSIN_Q3 < 3)
					ASSIN_Q3 = 2;
				ASSIN_Q = 1;
				changequest 8001,8002;
				warp "in_moc_16",19,144;
				end;
			}
			else {
				mes "[Assassin 'Khai']";
				mes "How the hell did";
				mes "you get in here?";
				mes "Get out!";
				close2;
				warp "moc_fild16",206,229;
				end;
			}
		}
	}
}

in_moc_16,19,154,0	script	nameless_one	-1,8,2,{
OnTouch_:
	if (ASSIN_Q2 < 5) {
		if (ASSIN_Q2 < 3) {
			mes "[The Anonymous One]";
			mes "Welcome, guest.";
			mes "Mwahaha, it's useless";
			mes "to try to find or see me...";
			next;
			mes "[The Anonymous One]";
			mes "I am perfectly hidden!";
			mes "To become undetectable can only be done by the greatest Assassins!";
			next;
			mes "[The Anonymous One]";
			mes "Aren't you scared that you can't see me? I could kill you at any time and it would be so easy...";
			next;
			if (select("I think I crapped my pants!:You're all talk. I challenge you!") == 1) {
				mes "[The Anonymous One]";
				mes "Now I see that";
				mes "you're nothing";
				mes "but a wimp.";
				next;
				mes "[The Anonymous One]";
				mes "Bwahahahahahah!";
				mes "Stop cowering in fear!";
				mes "It's making me laugh!";
				close;
			}
			mes "[The Anonymous One]";
			mes "So...";
			mes "You wish for";
			mes "a challenge?";
			mes "From me?!";
			next;
			mes "[The Anonymous One]";
			mes "A river of blood follows my every footstep. I am nameless, for the sting of my blades is all anyone needs to know.";
			next;
			mes "[The Anonymous One]";
			mes "I am here to test your knowledge, as well as your capacity for heartlessness. Those are both necessary to become an Assassin.";
			next;
			mes "[The Anonymous One]";
			mes "For your challenge, you must";
			mes "answer my questions correctly. Very difficult questions that only an Assassin can answer.";
			next;
			mes "[The Anonymous One]";
			mes "Although I am heartless,";
			mes "I am not necessarily cruel. Before we proceed, is there anything you wish to know?";
			next;
			ASSIN_Q2 = 0;
			while(ASSIN_Q2 < 3) {
				switch(select("...Skills?:...Stats?:Hmpf, I know it all.")) {
				case 1:
					mes "[The Anonymous One]";
					mes "Skills...?";
					mes "Although skills can have circumstantial applications, I will tell you about the basic concepts.";
					next;
					mes "[The Anonymous One]";
					mes "First, ^3355FFKatar Mastery^000000. This skill increases the damage of Katar class weapons. The higher the skill level, the more damage is increased.";
					next;
					mes "[The Anonymous One]";
					mes "^3355FFLeft Hand Mastery^000000 and ^3355FFRight Hand Mastery^000000. Assassins can equip different weapons in each hand when using Dagger class weapons.";
					next;
					mes "[The Anonymous One]";
					mes "But it is obviously more difficult to handle 2 weapons at a time than using just one. The Left and Right Hand Mastery skills increase the damage when using two Daggers.";
					next;
					mes "[The Anonymous One]";
					mes "However, if you don't want to use two Daggers, you won't need this skill. You will see how 'Left Hand Mastery' works as soon as you reach 'Right Hand Mastery' Level 2.";
					next;
					mes "[The Anonymous One]";
					mes "^3355FFSonic Blow^000000 allows you to strike an enemy 8 times at once. This skill only works with Katar weapons because of the speed it requires.";
					next;
					mes "[The Anonymous One]";
					mes "Of course, the damage is affected by STR and weapon damage. You'll understand how this skill works when you reach Level 4 Katar Mastery.";
					next;
					mes "[The Anonymous One]";
					mes "^3355FFGrimtooth^000000 allows you to attack enemies while hiding under the ground. As you master it, you'll be able to attack foes from a distance.";
					next;
					mes "[The Anonymous One]";
					mes "Since it's a ranged attack, it can be very useful when you're surrounded by enemies.";
					next;
					mes "[The Anonymous One]";
					mes "Because you're required to perfectly hide yourself to use this skill, you must first learn Level 2 Cloaking before you can learn Grimtooth.";
					next;
					mes "[The Anonymous One]";
					mes "To learn ^3355FFCloaking^000000, you must learn Level 2 Hiding. Then you will be able to move while hiding if you are close to a wall.";
					next;
					mes "[The Anonymous One]";
					mes "The ^3355FFEnchant Poison^000000 skill allows you to enchant poison on the weapon you're using. This will temporarily give the weapon the Poison property.";
					next;
					mes "[The Anonymous One]";
					mes "This will also make your attacks poison the enemy by chance. You can also use this skill to enchant the weapons of your party members...";
					next;
					mes "[The Anonymous One]";
					mes "^3355FFPoison React^000000 shields the user from attacks with the Poison property, and can be used on other people as well. However, you must learn Level 3 Enchant Poison first.";
					next;
					mes "[The Anonymous One]";
					mes "^3355FFVenom Dust^000000 consumes a Red Gemstone to contaminate an area with poison. The duration of contamination increases with the level of this skill.";
					next;
					mes "[The Anonymous One]";
					mes "You can learn the Venom Dust skill after you learn Level 5 Enchant Poison.";
					next;
					mes "[The Anonymous One]";
					mes "^3355FFVenom Splasher^000000 is a skill that, after it is used on a target, will cause it to explode when its HP is less than a certain amount after three seconds.";
					next;
					mes "[The Anonymous One]";
					mes "When the target explodes, the enemies in the vicinity are also damaged. This is an essential skill for Assassins. It requires Level 5 Poison React and Level 5 Venom Dust.";
					next;
					mes "[The Anonymous One]";
					mes "Now...";
					mes "That's all I have to tell you";
					mes "about Assassin skills.";
					ASSIN_Q2 = 1;
					next;
					break;
				case 2:
					mes "[The Anonymous One]";
					mes "Hmm, Stats...";
					mes "For Assassins, Agility, or AGI, is the most important stat.";
					next;
					mes "[The Anonymous One]";
					mes "For the sake of assassination, STR is probably the second most important stat. But that is only my recommendation.";
					next;
					mes "[The Anonymous One]";
					mes "I cannot give you better advice than that in regards to Stats. You should research and see which stats suit you, and decide what kind of Assassin you want to be.";
					ASSIN_Q2 = 2;
					next;
					break;
				case 3:
					if (ASSIN_Q2 == 0) {
						mes "[The Anonymous One]";
						mes "Know everything do you?!";
						mes "I'll be the judge of that!";
						next;
					}
					ASSIN_Q2 = 3;
					break;
				}
			}
			mes "[The Anonymous One]";
			mes "Hmpf. It is now time to test your knowledge. You are not allowed to miss more than one question.";
			next;
			mes "[The Anonymous One]";
			mes "In other words, if you want to pass this test, you must give me 9 correct answers out of 10 questions. I won't let you know which answer you got wrong...";
			next;
			mes "[The Anonymous One]";
			mes "Are you ready?";
			mes "Prepare yourself!";
		}
		else if (ASSIN_Q2 < 5) {
			mes "[The Anonymous One]";
			mes "Having problems";
			mes "passing a simple test?";
			mes "You should have";
			mes "known better.";
			next;
			if (select("Help me, how do I pass?:I challenge you again!") == 1) {
				mes "[The Anonymous One]";
				mes "Well, that's a damn good question. But you're banished from the Assassin Guild, so it's no concern of mine...";
				close2;
				warp "moc_fild16",206,151;
				end;
			}
			mes "[The Anonymous One]";
			mes "So I see...";
			mes "Now go, but do not fear. I will be by your side as you learn the outcome of your choice...";
			next;
			mes "[The Anonymous One]";
			mes "Now, we shall test you once more! Keep in mind, you must answer 9 questions out of 10 correctly. Remember I am doing you a favor...";
			next;
			mes "[The Anonymous One]";
			mes "You must answer 9 questions out of 10 correctly. If you miss more than one question, you can never become an Assassin.";
			next;
			mes "[The Anonymous One]";
			mes "Okay,";
			mes "are you ready?";
			mes "Good luck.";
		}
		next;
		switch(rand(1,3)) {
		case 1:
			mes "[The Anonymous One]";
			mes "1. Choose skill that is not required to learn Grimtooth.";
			next;
			if (select("Cloaking level 2:Sonic Blow level 5:Katar Mastery level 4:Right hand Mastery level 2") == 4)
				.@assassin_t += 10;
			mes "[The Anonymous One]";
			mes "2. What property does Enchant Poison possess?";
			next;
			if (select("Poison:Earth:Fire:Wind") == 1)
				.@assassin_t += 10;
			mes "[The Anonymous One]";
			mes "3. How does Level 4 Right Hand Mastery work?";
			next;
			if (select("Recover 80% of damage decrease:Recover 90% of damage decrease:Increase 90% of damage:Increase 108% of damage") == 2)
				.@assassin_t += 10;
			mes "[The Anonymous One]";
			mes "4. What is the item required for using Venom Dust?";
			next;
			if (select("Red Blood:Blue Gemstone:Yellow Gemstone:Red Gemstone") == 4)
				.@assassin_t += 10;
			mes "[The Anonymous One]";
			mes "5. Which skill can you learn when you reach Level 5 Enchant Poison?";
			next;
			if (select("Envenom:Sonic Blow:Venom Splasher:Venom Dust") == 4)
				.@assassin_t += 10;
			mes "[The Anonymous One]";
			mes "6. Among the following skills, which allows you to walk while invisible?";
			next;
			if (select("Hiding:Back Slide:Cloaking:Sand Attack") == 3)
				.@assassin_t += 10;
			mes "[The Anonymous One]";
			mes "7. Choose the condition that is unrelated to Venom Splasher.";
			next;
			if (select("Poisoned target.:Red Gemstone.:Remaing HP of Target.") == 2)
				.@assassin_t += 10;
			mes "[The Anonymous One]";
			mes "8. Which monster is weak to a weapon with Vadon card (adds 20% damage on Fire property monster)?";
			next;
			if (select("Steel Chonchon:Deviruchi:Elder Willow:Baphomet") == 3)
				.@assassin_t += 10;
			mes "[The Anonymous One]";
			mes "9. How much SP does";
			mes "Double Attack need?";
			next;
			if (select("15:It's a passive skill, so SP use is 0.:It's passive skill, so SP use is 10.:54") == 2)
				.@assassin_t += 10;
			mes "[The Anonymous One]";
			mes "10. What is the best elemental Main Gauche weapon for hunting in Izlude dungeon?";
			next;
			if (select("Wind Main Gauche:Ice Main Gauche:Earth Main Gauche:Fire Main Gauche") == 1)
				.@assassin_t += 10;
			break;
		case 2:
			mes "[The Anonymous One]";
			mes "1. Which monster";
			mes "drops a slotted Katar?";
			next;
			if (select("Thief Bug:Peco Peco:Desert Wolf:Hammer Cobolt") == 3)
				.@assassin_t += 10;
			mes "[The Anonymous One]";
			mes "2. Which monster";
			mes "drops a slotted Jur?";
			next;
			if (select("Martin:Desert Wolf:Marionette:Myst") == 1)
				.@assassin_t += 10;
			mes "[The Anonymous One]";
			mes "3. Which class is allowed to craft elemental weapons?";
			next;
			if (select("Merchant:Blacksmith:Thief:Priest") == 2)
				.@assassin_t += 10;
			mes "[The Anonymous One]";
			mes "4. Choose the weapon which is not in the Katar class.";
			next;
			if (select("Jamadhar:Jur:Katar:Gladius") == 4)
				.@assassin_t += 10;
			mes "[The Anonymous One]";
			mes "5. What property do Izlude dungeon monsters posses?";
			next;
			if (select("Water:Fire:Wind:Earth") == 1)
				.@assassin_t += 10;
			mes "[The Anonymous One]";
			mes "6. Which monster";
			mes "cannot be a Cute Pet?";
			next;
			if (select("Poporing:Roda Frog:Smokie:Poison Spore") == 2)
				.@assassin_t += 10;
			mes "[The Anonymous One]";
			mes "7. Choose a monster that Fire property Daggers work the best on.";
			next;
			if (select("Dagger Goblin:Mace Goblin:Morning Star Goblin:Hammer Goblin") == 4)
				.@assassin_t += 10;
			mes "[The Anonymous One]";
			mes "8. Choose the non-elemental Katar from the following:";
			next;
			if (select("Katar of Raging Blaze:Katar of Dusty Thornbush:Sharpened Legbone of Ghoul:Infiltrator") == 4)
				.@assassin_t += 10;
			mes "[The Anonymous One]";
			mes "9. Which is the uncommon monster?";
			next;
			if (select("Poring:Mastering:Ghostring:Spore") == 3)
				.@assassin_t += 10;
			mes "[The Anonymous One]";
			mes "10. Choose the monster";
			mes "that is not Undead.";
			next;
			if (select("Drake:Megalodon:Spore:Khalitzburg") == 3)
				.@assassin_t += 10;
			break;
		case 3:
			mes "[The Anonymous One]";
			mes "1. Choose the correct amount of the maximum dodge rate increase from the 'Increase Dodge' skill when at level 10.";
			next;
			if (select("30:40:160:20") == 1)
				.@assassin_t += 10;
			mes "[The Anonymous One]";
			mes "2. Choose a monster which detects hiding/cloaking Thieves and Assassins.";
			next;
			if (select("Worm Tail:Andre:Mummy:Soldier Skeleton") == 2)
				.@assassin_t += 10;
			mes "[The Anonymous One]";
			mes "3. Choose a group of weapons that cannot be used by an Assassin at once.";
			next;
			if (select("Main Gaughe + Gladius:Stiletto + Main Gauche:Katar + Maingauche:Hammer + Stiletto") == 3)
				.@assassin_t += 10;
			mes "[The Anonymous One]";
			mes "4. Choose the town where Thieves can change their jobs.";
			next;
			if (select("Prontera:Lutie:Alberta:Morocc") == 4)
				.@assassin_t += 10;
			mes "[The Anonymous One]";
			mes "5. Choose a card that does not affect the AGI stat.";
			next;
			if (select("Baphomet Jr. card:Whisper Card:Female Thiefbug card:Male Thiefbug card") == 2)
				.@assassin_t += 10;
			mes "[The Anonymous One]";
			mes "6. Choose the correct specialty of the Assassin class.";
			next;
			if (select("Excellent singing talent:Excellent reading talent:Excellent dancing talent:Excellent dodge ability") == 4)
				.@assassin_t += 10;
			mes "[The Anonymous One]";
			mes "7. Choose the maximum AGI bonus an Assassin can get at job level 50.";
			next;
			if (select("7:8:9:10") == 4)
				.@assassin_t += 10;
			mes "[The Anonymous One]";
			mes "8. Choose the item that an Assassin cannot equip.";
			next;
			if (select("Dagger:Helm:Boots:Brooch") == 2)
				.@assassin_t += 10;
			mes "[The Anonymous One]";
			mes "9. Choose the job change item for Thief.";
			next;
			switch(select("Orange Gooey Mushroom:Red Gooey Mushroom:Orange Net Mushroom:Orange Hair Mushroom")) {
			case 1:
			case 3:
				.@assassin_t += 10;
				break;
			default:
				break;
			}
			mes "[The Anonymous One]";
			mes "10. Choose a card that would typically benefit an Assassin the least.";
			next;
			if (select("Whisper card:Elder Willow card:Soldier Skeleton card:Cobold card") == 2)
				.@assassin_t += 10;
			break;
		}
		if (ASSIN_Q2 == 3) {
			next;
			mes "[The Anonymous One]";
			mes "Hmpf.";
			mes "Somehow, you";
			mes "have shown me";
			mes "great effort.";
			next;
			mes "[The Anonymous One]";
			mes "Let's see...";
			mes "You scored";
			mes "" + .@assassin_t + " percent...";
			if (.@assassin_t > 80) {
				ASSIN_Q2 = 5;
				changequest 8002,8003;
				mes "Well done.";
				mes "You pass.";
				next;
				mes "[The Anonymous One]";
				mes "However, another test awaits you. When you go inside the next area, you will receive your instructions...";
				close;
			}
			else {
				ASSIN_Q2 = 4;
				mes "That means you fail!";
				next;
				mes "[The Anonymous One]";
				mes "How could you expect to be an Assassin with this score? Keep training and come back when you're ready.";
				next;
				mes "[The Anonymous One]";
				mes "I would ask 'Khai,' the one who processed your application, for advice.";
				next;
				mes "[The Anonymous One]";
				mes "You may also use this code: ^880000iro.ragnarokonline.com^000000. Somehow, those words are linked to a vast body of otherworldly knowledge...";
				close2;
				warp "in_moc_16",19,76;
				end;
			}
		}
		else if (ASSIN_Q2 == 4) {
			next;
			mes "[The Anonymous One]";
			mes "You showed";
			mes "great effort...";
			next;
			mes "[The Anonymous One]";
			mes "Let's see...";
			mes "You scored";
			mes "" + .@assassin_t + " points...";
			if (.@assassin_t > 80) {
				ASSIN_Q2 = 5;
				changequest 8002,8003;
				next;
				mes "[The Anonymous One]";
				mes "You didn't fail this time! But you're not done just yet. You have another test ahead of you. Once you proceed, you will be informed about your next trial.";
				close;
			}
			else {
				ASSIN_Q2 = 4;
				mes "You failed!";
				next;
				mes "[The Anonymous One]";
				mes "You're too underqualified. How can you even think about becoming an Assassin?!";
				next;
				mes "[The Anonymous One]";
				mes "I'm surprised that you were even able to become a Thief. Go away, and come back only when you know what the hell you're doing.";
				next;
				mes "[The Anonymous One]";
				mes "Hmpf, if you really don't have a clue, I will give you a little advice.";
				next;
				mes "[The Anonymous One]";
				mes "Go ask 'Khai,' the guy who takes care of your test application, maybe he will help you.";
				next;
				mes "[The Anonymous One]";
				mes "You may also wish to take advantage of the ancient code, ^3355FFiro.ragnarokonline.com^000000. Supposedly, those words are linked to a vast body of otherworldly knowledge...";
				close2;
				warp "in_moc_16",19,76;
				end;
			}
		}
	}
	else {
		mes "[The Anonymous One]";
		mes "...I will keep watching you.";
		close;
	}
}

in_moc_16,21,165,2	script	Standby Room#ASNTEST	725,{
	end;

OnInit:
	//disablenpc "Standby Room#ASNTEST";
	hideonnpc "Standby Room#ASNTEST";
	waitingroom "Standby Room",10,"Standby Room#ASNTEST::OnStartArena",1;
	enablewaitingroomevent;
	end;

OnStartArena:
	warpwaitingpc "in_moc_16", 66, 151;
	attachrid($@warpwaitingpc[0]);
	if (ASSIN_Q2 < 5) {
		warpchar "in_moc_16",20,145,getcharid(0);
		end;
	}
	donpcevent "Beholder#ASNTEST::OnEnable";
	donpcevent "Keeper of the Door#ASN::OnDisable";
	set getvariableofnpc(.DisableTraps,"Beholder#ASNTEST"),0;
	disablewaitingroomevent;
	end;

OnStart:
	enablewaitingroomevent;
	end;
}

in_moc_16,21,165,2	script	Test Guide#ASN	725,4,4,{
	end;

OnTouch_:
	if (ASSIN_Q2 < 5) {
		mes "[Barcardi]";
		mes "You can't take the next trial without passing the written test first. You better speak to the Anonymous One...";
		close2;
		warp "in_moc_16",19,76;
		end;
	}
	if (ASSIN_Q == 1 && ASSIN_Q2 == 5) {
		mes "[Barcardi]";
		mes "" + strcharinfo(0) + "...";
		mes "You passed the test..?";
		next;
		mes "[Barcardi]";
		mes "To be honest, I want to grant you the job change without any other condition. Too many pathetic people don't even have the basic knowledge to be Assassins...";
		next;
		mes "[Barcardi]";
		mes "We must keep our dignity as Assassins and be truly great! Regrettably, there are too many idiots that don't have any pride.";
		next;
		mes "[Barcardi]";
		mes "All Assassins must respect the enemies they slay, the blood that they spill, and above all, maintain their sense of dignity!";
		next;
		mes "[Barcardi]";
		mes "Alright. This next trial will test your ability to quickly find your target.";
		next;
		mes "[Barcardi]";
		mes "If you're going to be an Assassin, we need to determine whether or not you can distinguish friend from foe in an instant.";
		next;
		mes "[Barcardi]";
		mes "The main goal of this test is to find and kill as many monsters named ^008800Job change target^000000 as possible.";
		next;
		mes "[Barcardi]";
		mes "You must kill at least";
		mes "6 ^008800Job change target^000000 monsters. They're intermingled among similar looking monsters, so you need to be careful...";
		next;
		mes "[Barcardi]";
		mes "If you fail, you'll have to restart this test. Go to the room above";
		mes "me to be transported to the Test Hall.";
		next;
		mes "[Barcardi]";
		mes "Only one person is allowed to take the test at a time, so if anyone is taking the test, you'll have to wait until that person finishes.";
		close;
	}
	else {
		mes "[Barcardi]";
		mes "Hey, don't be too hard";
		mes "on yourself. Cheer up!";
		next;
		mes "[Barcardi]";
		mes "Hmm, if you're exhausted, I'm willing to bring you back. Of course, if you leave, you'll have to take the job test over again. So what do you want to do?";
		next;
		if (select("Continue!:Quit the job change test for now.") == 1) {
			mes "[Barcardi]";
			mes "Good choice!";
			mes "Remember, you";
			mes "must find and kill";
			mes "6 ^008800Job change target^000000 monsters!";
			mes "Good luck!";
			close;
		}
		mes "[Barcardi]";
		mes "Alright...";
		mes "I guess you";
		mes "could use a break...";
		close2;
		ASSIN_Q = 0;
		ASSIN_Q2 = 0;
		changequest 8003,8000;
		warp "in_moc_16",19,13;
		end;
	}
}

in_moc_16,1,1,0	script	Beholder#ASNTEST	-1,{
	end;

OnEnable:
	.MyMobs = 6;
	// Target mobs
	monster "in_moc_16",62,161,"Job change target",1002,1,"Beholder#ASNTEST::OnMyMobDead";
	monster "in_moc_16",85,169,"Job change target",1063,1,"Beholder#ASNTEST::OnMyMobDead";
	monster "in_moc_16",88,152,"Job change target",1002,1,"Beholder#ASNTEST::OnMyMobDead";
	monster "in_moc_16",90,143,"Job change target",1113,1,"Beholder#ASNTEST::OnMyMobDead";
	monster "in_moc_16",74,167,"Job change target",1031,1,"Beholder#ASNTEST::OnMyMobDead";
	monster "in_moc_16",77,173,"Job change target",1002,1,"Beholder#ASNTEST::OnMyMobDead";
	// Decoy mobs
	monster "in_moc_16",62,161,"Job change creature",1063,1,"Beholder#ASNTEST::OnMyMobDead2";
	monster "in_moc_16",85,169,"Job change creature",1031,1,"Beholder#ASNTEST::OnMyMobDead2";
	monster "in_moc_16",79,174,"Job change creature",1113,1,"Beholder#ASNTEST::OnMyMobDead2";
	monster "in_moc_16",85,156,"Job change creature",1063,1,"Beholder#ASNTEST::OnMyMobDead2";
	monster "in_moc_16",74,171,"Job change monster",1002,1,"Beholder#ASNTEST::OnMyMobDead2";
	monster "in_moc_16",68,173,"Job change dummy",1113,1,"Beholder#ASNTEST::OnMyMobDead2";
	monster "in_moc_16",65,158,"Battle test target",1002,1,"Beholder#ASNTEST::OnMyMobDead2";
	monster "in_moc_16",60,158,"Warrior test target",1113,1,"Beholder#ASNTEST::OnMyMobDead2";
	monster "in_moc_16",64,169,"Job change targets",1002,1,"Beholder#ASNTEST::OnMyMobDead2";
	monster "in_moc_16",71,173,"Jobs change target",1063,1,"Beholder#ASNTEST::OnMyMobDead2";
	monster "in_moc_16",77,172,"Please don't hit me",1002,1,"Beholder#ASNTEST::OnMyMobDead2";
	monster "in_moc_16",76,172,"Job change sample",1063,1,"Beholder#ASNTEST::OnMyMobDead2";
	monster "in_moc_16",75,172,"Not me",1113,1,"Beholder#ASNTEST::OnMyMobDead2";
	monster "in_moc_16",67,167,"I got yours right here",1063,1,"Beholder#ASNTEST::OnMyMobDead2";
	monster "in_moc_16",86,170,"Job changes target",1031,1,"Beholder#ASNTEST::OnMyMobDead2";
	monster "in_moc_16",86,171,"Job quest target",1002,1,"Beholder#ASNTEST::OnMyMobDead2";
	monster "in_moc_16",85,170,"Job target change",1113,1,"Beholder#ASNTEST::OnMyMobDead2";
	monster "in_moc_16",89,171,"Hit me",1063,1,"Beholder#ASNTEST::OnMyMobDead2";
	monster "in_moc_16",85,170,"Battle Monster",1031,1,"Beholder#ASNTEST::OnMyMobDead2";
	monster "in_moc_16",89,156,"Bouncer",1002,1,"Beholder#ASNTEST::OnMyMobDead2";
	monster "in_moc_16",89,156,"Mungamorp",1113,1,"Beholder#ASNTEST::OnMyMobDead2";
	monster "in_moc_16",89,156,"Battle test target",1063,1,"Beholder#ASNTEST::OnMyMobDead2";
	monster "in_moc_16",89,156,"Dew of the Battle field",1113,1,"Beholder#ASNTEST::OnMyMobDead2";
	monster "in_moc_16",89,156,"Tear of test",1031,1,"Beholder#ASNTEST::OnMyMobDead2";
	monster "in_moc_16",83,169,"Evil Druid",1002,1,"Beholder#ASNTEST::OnMyMobDead2";
	monster "in_moc_16",63,158,"Doppelganger",1063,1,"Beholder#ASNTEST::OnMyMobDead2";
	monster "in_moc_16",63,157,"Job change dummy",1002,1,"Beholder#ASNTEST::OnMyMobDead2";
	monster "in_moc_16",64,159,"Job ready dummy",1002,1,"Beholder#ASNTEST::OnMyMobDead2";
	monster "in_moc_16",63,159,"Job change ready",1063,1,"Beholder#ASNTEST::OnMyMobDead2";
	monster "in_moc_16",63,159,"Archer test target",1002,1,"Beholder#ASNTEST::OnMyMobDead2";
	monster "in_moc_16",63,159,"Swordman test target",1002,1,"Beholder#ASNTEST::OnMyMobDead2";
	monster "in_moc_16",83,148,"Thief test target",1002,1,"Beholder#ASNTEST::OnMyMobDead2";
	monster "in_moc_16",82,148,"Acolyte test target",1002,1,"Beholder#ASNTEST::OnMyMobDead2";
	monster "in_moc_16",84,148,"Merchant test target",1002,1,"Beholder#ASNTEST::OnMyMobDead2";
	initnpctimer;
	end;

OnReset:
	killmonster "in_moc_16","Beholder#ASNTEST::OnMyMobDead";
	killmonster "in_moc_16","Beholder#ASNTEST::OnMyMobDead2";
	stopnpctimer;
	donpcevent "Standby Room#ASNTEST::OnStart";
	end;

OnResetmob:
	killmonster "in_moc_16","Beholder#ASNTEST::OnMyMobDead";
	killmonster "in_moc_16","Beholder#ASNTEST::OnMyMobDead2";
	stopnpctimer;
	end;

OnMyMobDead:
	.MyMobs--;
	if (.MyMobs < 1) {
		mapannounce "in_moc_16","You seem to be doing quite well. Keep it up!",bc_map;
		ASSIN_Q = 3;
		changequest 8003,8004;
		donpcevent "timestopper#1::OnEnable";
		donpcevent "Keeper of the Door#ASN::OnEnable";
		donpcevent "Beholder#ASNTEST::OnResetmob";
		.DisableTraps = 1;
		stopnpctimer;
	}
	else {
		mapannounce "in_moc_16","Okay, you're doing good! Hang in there, you're almost there!",bc_map;
	}
	end;

OnMyMobDead2:
	mapannounce "in_moc_16",strcharinfo(0) + "! You made a mistake! I'm bringing you back!",bc_map;
	ASSIN_Q = 2;
	warp "in_moc_16",19,161;
	donpcevent "Beholder#ASNTEST::OnReset";
	end;

OnTimer1000:
	mapannounce "in_moc_16"," Okay, let the test begin!",bc_map;
	end;

OnTimer2000:
	mapannounce "in_moc_16","As you've been told before, find and only kill monsters named 'Job change target!'",bc_map;
	end;

OnTimer3000:
	mapannounce "in_moc_16","The purpose of this test is to examine your ability to quickly distinguish enemies from other people!",bc_map;
	end;

OnTimer4000:
	mapannounce "in_moc_16","You will have 3 minutes for the test! We will inform you of every minute passed.",bc_map;
	end;

OnTimer5000:
	mapannounce "in_moc_16","Ok, now you've got exactly 3 minutes. Move! Move!",bc_map;
	end;

OnTimer65000:
	mapannounce "in_moc_16","2 minutes left. As I've told you, get the 'Job change target' monsters!",bc_map;
	end;

OnTimer125000:
	mapannounce "in_moc_16","1 minute left.",bc_map;
	end;

OnTimer180000:
	mapannounce "in_moc_16","5 seconds left...",bc_map;
	end;

OnTimer181000:
	mapannounce "in_moc_16","4 seconds left...",bc_map;
	end;

OnTimer182000:
	mapannounce "in_moc_16","3 seconds left...",bc_map;
	end;

OnTimer183000:
	mapannounce "in_moc_16","2 seconds left...",bc_map;
	end;

OnTimer184000:
	mapannounce "in_moc_16","1 second left.",bc_map;
	end;

OnTimer185000:
	mapannounce "in_moc_16","Time's up!",bc_map;
	mapannounce "in_moc_16","Well, good job... If you wanted to waste your time. You'll have to try again!",bc_map;
	//ASSIN_Q = 2;
	end;

OnTimer186000:
	areawarp "in_moc_16",60,136,93,177,"in_moc_16",19,161;
	end;

OnTimer187000:
	donpcevent "Beholder#ASNTEST::OnReset";
	end;
}

in_moc_16,68,158,0	script	01_1::SinTrap	-1,0,0,{
OnTouch_:
	if (getvariableofnpc(.DisableTraps,"Beholder#ASNTEST")  < 1) {
		mapannounce "in_moc_16",strcharinfo(0) + ", you're trapped. You will be sent back.",bc_map;
		ASSIN_Q = 2;
		warp "in_moc_16",19,161;
		donpcevent "Beholder#ASNTEST::OnResetmob";
		donpcevent "Standby Room#ASNTEST::OnStart";
	}
	end;
}

in_moc_16,69,158,0	duplicate(SinTrap)	01_2	-1,0,0
in_moc_16,68,159,0	duplicate(SinTrap)	01_3	-1,0,0
in_moc_16,69,159,0	duplicate(SinTrap)	01_4	-1,0,0
in_moc_16,64,162,0	duplicate(SinTrap)	02_1	-1,0,0
in_moc_16,65,162,0	duplicate(SinTrap)	02_2	-1,0,0
in_moc_16,64,163,0	duplicate(SinTrap)	02_3	-1,0,0
in_moc_16,65,163,0	duplicate(SinTrap)	02_4	-1,0,0
in_moc_16,62,168,0	duplicate(SinTrap)	03_1	-1,0,0
in_moc_16,63,168,0	duplicate(SinTrap)	03_2	-1,0,0
in_moc_16,62,169,0	duplicate(SinTrap)	03_3	-1,0,0
in_moc_16,63,169,0	duplicate(SinTrap)	03_4	-1,0,0
in_moc_16,66,170,0	duplicate(SinTrap)	04_1	-1,0,0
in_moc_16,67,170,0	duplicate(SinTrap)	04_2	-1,0,0
in_moc_16,66,171,0	duplicate(SinTrap)	04_3	-1,0,0
in_moc_16,67,171,0	duplicate(SinTrap)	04_4	-1,0,0
in_moc_16,64,174,0	duplicate(SinTrap)	05_1	-1,0,0
in_moc_16,64,175,0	duplicate(SinTrap)	05_2	-1,0,0
in_moc_16,65,174,0	duplicate(SinTrap)	05_3	-1,0,0
in_moc_16,65,175,0	duplicate(SinTrap)	05_4	-1,0,0
in_moc_16,72,174,0	duplicate(SinTrap)	06_1	-1,0,0
in_moc_16,72,175,0	duplicate(SinTrap)	06_2	-1,0,0
in_moc_16,73,174,0	duplicate(SinTrap)	06_3	-1,0,0
in_moc_16,73,175,0	duplicate(SinTrap)	06_4	-1,0,0
in_moc_16,72,166,0	duplicate(SinTrap)	07_1	-1,0,0
in_moc_16,72,167,0	duplicate(SinTrap)	07_2	-1,0,0
in_moc_16,73,166,0	duplicate(SinTrap)	07_3	-1,0,0
in_moc_16,73,167,0	duplicate(SinTrap)	07_4	-1,0,0
in_moc_16,72,168,0	duplicate(SinTrap)	08_1	-1,0,0
in_moc_16,72,169,0	duplicate(SinTrap)	08_2	-1,0,0
in_moc_16,73,168,0	duplicate(SinTrap)	08_3	-1,0,0
in_moc_16,73,169,0	duplicate(SinTrap)	08_4	-1,0,0
in_moc_16,78,168,0	duplicate(SinTrap)	09_1	-1,0,0
in_moc_16,78,169,0	duplicate(SinTrap)	09_2	-1,0,0
in_moc_16,79,168,0	duplicate(SinTrap)	09_3	-1,0,0
in_moc_16,79,169,0	duplicate(SinTrap)	09_4	-1,0,0
in_moc_16,80,172,0	duplicate(SinTrap)	10_1	-1,0,0
in_moc_16,81,172,0	duplicate(SinTrap)	10_2	-1,0,0
in_moc_16,82,172,0	duplicate(SinTrap)	10_3	-1,0,0
in_moc_16,83,172,0	duplicate(SinTrap)	10_4	-1,0,0
in_moc_16,80,173,0	duplicate(SinTrap)	10_5	-1,0,0
in_moc_16,81,173,0	duplicate(SinTrap)	10_6	-1,0,0
in_moc_16,82,173,0	duplicate(SinTrap)	10_7	-1,0,0
in_moc_16,83,173,0	duplicate(SinTrap)	10_8	-1,0,0
in_moc_16,88,174,0	duplicate(SinTrap)	11_1	-1,0,0
in_moc_16,88,175,0	duplicate(SinTrap)	11_2	-1,0,0
in_moc_16,89,174,0	duplicate(SinTrap)	11_3	-1,0,0
in_moc_16,89,175,0	duplicate(SinTrap)	11_4	-1,0,0
in_moc_16,86,166,0	duplicate(SinTrap)	12_1	-1,0,0
in_moc_16,86,167,0	duplicate(SinTrap)	12_2	-1,0,0
in_moc_16,87,166,0	duplicate(SinTrap)	12_3	-1,0,0
in_moc_16,87,167,0	duplicate(SinTrap)	12_4	-1,0,0
in_moc_16,90,164,0	duplicate(SinTrap)	13_1	-1,0,0
in_moc_16,90,165,0	duplicate(SinTrap)	13_2	-1,0,0
in_moc_16,91,164,0	duplicate(SinTrap)	13_3	-1,0,0
in_moc_16,91,165,0	duplicate(SinTrap)	13_4	-1,0,0
in_moc_16,84,160,0	duplicate(SinTrap)	14_1	-1,0,0
in_moc_16,85,160,0	duplicate(SinTrap)	14_2	-1,0,0
in_moc_16,86,160,0	duplicate(SinTrap)	14_3	-1,0,0
in_moc_16,87,160,0	duplicate(SinTrap)	14_4	-1,0,0
in_moc_16,88,160,0	duplicate(SinTrap)	14_5	-1,0,0
in_moc_16,89,160,0	duplicate(SinTrap)	14_6	-1,0,0
in_moc_16,84,161,0	duplicate(SinTrap)	14_7	-1,0,0
in_moc_16,85,161,0	duplicate(SinTrap)	14_8	-1,0,0
in_moc_16,86,161,0	duplicate(SinTrap)	14_9	-1,0,0
in_moc_16,87,161,0	duplicate(SinTrap)	14_10	-1,0,0
in_moc_16,88,161,0	duplicate(SinTrap)	14_11	-1,0,0
in_moc_16,89,161,0	duplicate(SinTrap)	14_12	-1,0,0
in_moc_16,86,154,0	duplicate(SinTrap)	15_1	-1,0,0
in_moc_16,86,155,0	duplicate(SinTrap)	15_2	-1,0,0
in_moc_16,87,154,0	duplicate(SinTrap)	15_3	-1,0,0
in_moc_16,87,155,0	duplicate(SinTrap)	15_4	-1,0,0
in_moc_16,84,150,0	duplicate(SinTrap)	16_1	-1,0,0
in_moc_16,84,151,0	duplicate(SinTrap)	16_2	-1,0,0
in_moc_16,85,150,0	duplicate(SinTrap)	16_3	-1,0,0
in_moc_16,85,151,0	duplicate(SinTrap)	16_4	-1,0,0
in_moc_16,90,150,0	duplicate(SinTrap)	17_1	-1,0,0
in_moc_16,90,151,0	duplicate(SinTrap)	17_2	-1,0,0
in_moc_16,91,150,0	duplicate(SinTrap)	17_3	-1,0,0
in_moc_16,91,151,0	duplicate(SinTrap)	17_4	-1,0,0
in_moc_16,86,146,0	duplicate(SinTrap)	18_1	-1,0,0
in_moc_16,86,147,0	duplicate(SinTrap)	18_2	-1,0,0
in_moc_16,87,146,0	duplicate(SinTrap)	18_3	-1,0,0
in_moc_16,87,147,0	duplicate(SinTrap)	18_4	-1,0,0

in_moc_16,87,137,0	script	Keeper of the Door#ASN	45,2,1,{
OnInit:
	disablenpc "Keeper of the Door#ASN";
	end;

OnTouch_:
	donpcevent "Thomas#ASNTEST::OnDisable";
	if (ASSIN_Q == 3)
		ASSIN_Q = 3;
	else
		ASSIN_Q = 4;
	warp "in_moc_16",87,102;
	savepoint "in_moc_16",16,13,1,1;
	end;

OnEnable:
	mapannounce "in_moc_16","The door to the next room, at coordinates 87 137, has opened.",bc_map;
	enablenpc "Keeper of the Door#ASN";
	end;

OnDisable:
	disablenpc "Keeper of the Door#ASN";
	end;
}

in_moc_16,3,3,0	script	timestopper#1	-1,{
OnEnable:
	initnpctimer;
	end;

OnTimer187000:
	donpcevent "Thomas#ASNTEST::OnDisable";
	stopnpctimer;
	end;

OnDisable:
	stopnpctimer;
	end;

OnMyMobDead:
	mapannounce "in_moc_16","Hey, what the hell was that?! I told you: No killing monsters!",bc_map;
	mapannounce "in_moc_16","I'm bringing you back... *Sigh...*",bc_map;
	ASSIN_Q = 3;
	warp "in_moc_16",87,102;
	killmonster "in_moc_16","timestopper#1::OnMyMobDead";
	end;
}

in_moc_16,89,98,1	script	Thomas#ASNTEST	118,5,1,{
	end;

OnTouch_:
	if (ASSIN_Q == 4) {
		mes "[Thomas]";
		mes "Damn...! You look like you're in a lot of pain. ^666666*Sigh*^000000 Give me a second, let me try to restore your HP and SP...";
		percentheal 100,100;
		next;
		mes "[Thomas]";
		mes "It looks like you're having a tough time. You're either trying too hard, or not trying hard";
		mes "enough, kid.";
		next;
		switch(select("I'm gonna try it again!:I... I quit!")) {
		case 1:
			mes "[Thomas]";
			mes "Hmm. Well, okay.";
			mes "Good luck out there.";
			close;
		case 2:
			mes "[Thomas]";
			mes "Huh...";
			mes "Quit the test, eh? Well, I guess you don't wanna waste any more of our time.";
			next;
			mes "[Thomas]";
			mes "Oh hey, don't forget to save your respawn point in town.";
			close2;
			mapannounce "in_moc_16",strcharinfo(0) + " got scared and quit the test...Who's Next?!",bc_map;
			ASSIN_Q = 0;
			ASSIN_Q2 = 0;
			changequest 8004,8000;
			savepoint "in_moc_16",18,14,1,1;
			warp "in_moc_16",18,14;
			donpcevent "Standby Room#ASNTEST::OnStart";
			end;
		}
	}

	mes "[Thomas]";
	mes "Hey, I'm Thomas. I'm in charge of testing your use of the hiding skill. Think you're up to it?";
	next;
	mes "[Thomas]";
	mes "Listen. In this test, you can't kill any monsters. Your goal is to reach 'Barcardi' at the opposite side of this room.";
	next;
	mes "[Thomas]";
	mes "So basically, get to the other side of this room and meet 'Barcardi' without killing a single monster. Understand?";
	next;
	mes "[Thomas]";
	mes "If you run away, get a nose bleed and pass out or something like that, I'll fail ya'. Enough talk. Let's see what you got.";
	close2;
	ASSIN_Q = 4;

	monster "in_moc_16",81,77,"Mummy",1041,1,"timestopper#1::OnMyMobDead";
	monster "in_moc_16",82,77,"Mummy",1041,1,"timestopper#1::OnMyMobDead";
	monster "in_moc_16",83,77,"Mummy",1041,1,"timestopper#1::OnMyMobDead";
	monster "in_moc_16",84,77,"Mummy",1041,1,"timestopper#1::OnMyMobDead";
	monster "in_moc_16",85,77,"Mummy",1041,1,"timestopper#1::OnMyMobDead";
	monster "in_moc_16",86,77,"Mummy",1041,1,"timestopper#1::OnMyMobDead";
	monster "in_moc_16",88,77,"Mummy",1041,1,"timestopper#1::OnMyMobDead";
	monster "in_moc_16",89,77,"Mummy",1041,1,"timestopper#1::OnMyMobDead";
	monster "in_moc_16",90,77,"Mummy",1041,1,"timestopper#1::OnMyMobDead";
	monster "in_moc_16",77,77,"Mummy",1041,1,"timestopper#1::OnMyMobDead";
	monster "in_moc_16",78,56,"Mummy",1041,1,"timestopper#1::OnMyMobDead";
	monster "in_moc_16",79,56,"Mummy",1041,1,"timestopper#1::OnMyMobDead";
	monster "in_moc_16",80,56,"Mummy",1041,1,"timestopper#1::OnMyMobDead";
	monster "in_moc_16",81,56,"Mummy",1041,1,"timestopper#1::OnMyMobDead";
	monster "in_moc_16",91,55,"Mummy",1041,1,"timestopper#1::OnMyMobDead";
	monster "in_moc_16",92,56,"Mummy",1041,1,"timestopper#1::OnMyMobDead";
	monster "in_moc_16",93,56,"Mummy",1041,1,"timestopper#1::OnMyMobDead";
	monster "in_moc_16",94,56,"Mummy",1041,1,"timestopper#1::OnMyMobDead";
	monster "in_moc_16",95,56,"Mummy",1041,1,"timestopper#1::OnMyMobDead";
	monster "in_moc_16",96,56,"Mummy",1041,1,"timestopper#1::OnMyMobDead";
	monster "in_moc_16",97,56,"Mummy",1041,1,"timestopper#1::OnMyMobDead";
	monster "in_moc_16",76,62,"Hydra",1068,1,"timestopper#1::OnMyMobDead";
	monster "in_moc_16",79,62,"Hydra",1068,1,"timestopper#1::OnMyMobDead";
	monster "in_moc_16",79,65,"Hydra",1068,1,"timestopper#1::OnMyMobDead";
	monster "in_moc_16",76,65,"Hydra",1068,1,"timestopper#1::OnMyMobDead";
	monster "in_moc_16",96,62,"Hydra",1068,1,"timestopper#1::OnMyMobDead";
	monster "in_moc_16",96,65,"Hydra",1068,1,"timestopper#1::OnMyMobDead";
	monster "in_moc_16",99,62,"Hydra",1068,1,"timestopper#1::OnMyMobDead";
	monster "in_moc_16",99,65,"Hydra",1068,1,"timestopper#1::OnMyMobDead";
	end;

OnDisable:
	donpcevent "Standby Room#ASNTEST::OnStart";
	killmonster "in_moc_16","timestopper#1::OnMyMobDead";
	end;
}

in_moc_16,87,48,2	script	Barcardi#ASN	725,2,2,{
	end;
OnTouch_:
	donpcevent "timestopper#1::OnDisable";
	donpcevent "Thomas#ASNTEST::OnDisable";
	mes "[Barcardi]";
	mes "Oh! Congratulations!";
	mes "You may now proceed to our Guildmaster's room. Good luck!!";
	close2;
	ASSIN_Q = 5;
	changequest 8004,8005;
	warp "in_moc_16",181,183;
	end;
}

in_moc_16,182,169,0	script	Maze Assistant	45,1,1,{
OnTouch_:
	if (ASSIN_Q == 5 || ASSIN_Q == 6) {
		warp "in_moc_16",181,183;
		ASSIN_Q++;
		if (!isbegin_quest(8006))
			changequest 8005,8006;
	}
	else {
		mapannounce "in_moc_16",strcharinfo(0)+" has entered 'Guildmaster's room.'",bc_map;
		savepoint "in_moc_16",181,183,1,1;
		donpcevent "Guildmaster#ASN1::OnCast";
		warp "in_moc_16",167,113;
		end;
	}
}

in_moc_16,167,110,0	script	Guildmaster#ASN1	-1,3,1,{
OnTouch_:
	savepoint "in_moc_16",167,110,1,1;
	mes "[Guildmaster]";
	mes "Welcome. ";
	mes "This place is called the 'Guildmaster's room,' the deepest place in the Assassin guild.";
	next;
	mes "[Guildmaster]";
	mes "I'm going to give you a simple test. Please find your way through this maze and come to me. It is this maze that protects our guild from intruders.";
	next;
	mes "[Guildmaster]";
	mes "I look forward";
	mes "to meeting you";
	mes "at the end of maze.";
	close;

OnCast:
	mapannounce "in_moc_16","...Next volunteer, please come in.",bc_map;
	end;
}

in_moc_16,149,80,4	script	Guildmaster#ASN2	106,1,1,{
	end;
OnTouch_:
	if (checkre(0))
		savepoint "morocc",100,100,1,1;
	else
		savepoint "moc_ruins",79,99,1,1;
	if (ASSIN_Q == 7 && BaseJob == Job_Thief) {
		ASSIN_Q = 8;
		mes "[Guildmaster]";
		mes "Welcome.";
		mes "I apologize for";
		mes "making you go";
		mes "through the maze.";
		next;
		mes "[Guildmaster]";
		mes "I saw your resume just now. You're well known as a Thief with guts. Rarely do we receive potential Assassins of your stature.";
		next;
		mes "[Guildmaster]";
		mes "May I ask you some questions if you don't mind? You don't have to be nervous. Just remember: if you lie, I will kill you.";
		next;
		mes "[Guildmaster]";
		mes "First off, what do you think is the priority of an Assassin?";
		next;
		switch(select("More power.:An Assassin's pride.:Endless practice.")) {
		case 1:
			mes "[Guildmaster]";
			mes "More power...";
			mes "Yes, you may think";
			mes "of Assassins as much";
			mes "stronger than Thieves.";
			next;
			mes "[Guildmaster]";
			mes "However, for what reason do you wish for more power? To show off? Personal revenge? For what purpose will you use this power?";
			next;
			mes "[Guildmaster]";
			mes "Why do you want";
			mes "to be stronger";
			mes "than you are now?";
			next;
			switch(select("Revenge...!:Money~:I want to travel.")) {
			case 1:
				mes "[Guildmaster]";
				mes "Revenge...?";
				mes "Yes, I understand. All of us hold grudges against someone else eventually.";
				next;
				mes "[Guildmaster]";
				mes "However, keep in mind that we are not allowed to be emotionally attached. Carry out your duties without question. That is our way.";
				next;
				mes "[Guildmaster]";
				mes "Being an Assassin means";
				mes "to abandon the ego.";
				next;
				break;
			case 2:
				ASSIN_Q = 9;
				mes "[Guildmaster]";
				mes "Financial reasons...? I won't deny that we all need money to live. But being Assassin means living for a higher purpose.";
				next;
				mes "[Guildmaster]";
				mes "Being an Assassin means";
				mes "to abandon such worldly";
				mes "attachments...";
				next;
				break;
			case 3:
				ASSIN_Q = 10;
				mes "[Guildmaster]";
				mes "Good idea. Traveling around the world will allow you to broaden your experiences.";
				next;
				mes "[Guildmaster]";
				mes "Most of us tend to avoid gathering into groups though, but I'm sure you already caught that, yes?";
				next;
				mes "[Guildmaster]";
				mes "In a way, being an Assassin is to live life in loneliness...";
				next;
				break;
			}
			break;
		case 2:
			mes "[Guildmaster]";
			mes "An Assassin's pride...";
			mes "Did other Assassins tell you that...?";
			next;
			mes "[Guildmaster]";
			mes "Pride is certainly important, but pride is worth nothing if you do not have any ability.";
			next;
			mes "[Guildmaster]";
			mes "Most of the Assassins you've met before me are brethren that have shared many difficult times together.";
			next;
			mes "[Guildmaster]";
			mes "I can understand why their pride and dignity would be so important to them. Now, for what reason do you wish to become an Assassin?";
			next;
			switch(select("I like the solitude.:Making money being an Assassin.:They just look interesting.")) {
			case 1:
				ASSIN_Q = 11;
				mes "[Guildmaster]";
				mes "You got the point...";
				mes "We are lonely. We will always be alone, even amongst each other...";
				next;
				mes "[Guildmaster]";
				mes "In a way, being";
				mes "an Assassin equals";
				mes "nothing, I would say.";
				next;
				mes "[Guildmaster]";
				mes "But, as I told you before, we have comrades. I recommend having at least one comrade to back up you when you're on a mission.";
				next;
				break;
			case 2:
				ASSIN_Q = 12;
				mes "[Guildmaster]";
				mes "Well, I can't deny it, we do need money to make a living. But don't you think we should pursue something even more important than money?";
				next;
				break;
			case 3:
				ASSIN_Q = 13;
				mes "[Guildmaster]";
				mes "Style and appearance is only superficial. It is sad that many people think this way...";
				next;
				mes "[Guildmaster]";
				mes "Such disgraceful Assassins that have lost their true focus are dealt with in our own manner...";
				next;
				mes "[Guildmaster]";
				mes "Don't forget...";
				mes "Assassins don't toy around. We are not into a style or trend, and we never will be.";
				next;
				break;
			}
			break;
		case 3:
			mes "[Guildmaster]";
			mes "Endless Practice...";
			mes "I think you have what it takes. Is there a reason you want to be an Assassin?";
			next;
			mes "[Guildmaster]";
			mes "Unlike the Thief class, the Assassin job doesn't allow self-indulgence. Tell me the";
			mes "reason you train endlessly.";
			next;
			switch(select("To broaden my skills.:It's a goal of mine.:For spiritual improvement.")) {
			case 1:
				ASSIN_Q = 14;
				mes "[Guildmaster]";
				mes "Learning skills comes naturally with the Assassin job. But don't think of skills as the best value of your training.";
				next;
				mes "[Guildmaster]";
				mes "You won't be satisfied in becoming an Assassin if you think this...";
				next;
				break;
			case 2:
				ASSIN_Q = 15;
				mes "[Guildmaster]";
				mes "It's a goal of yours, eh? Well, I guess you've got to have goals...";
				next;
				mes "[Guildmaster]";
				mes "I once knew a person who had goals. Long ago, I met someone on assignment who wanted to keep from getting killed before becoming a level 54 knight.";
				next;
				mes "[Guildmaster]";
				mes "It's to bad I had to kill him before he was level 52... ^666666*Sigh*^000000 Oh well.";
				next;
				mes "I'm a bit worried about you. I hope you realize that once you become an Assassin, there's no turning back...";
				next;
				break;
			case 3:
				ASSIN_Q = 16;
				mes "[Guildmaster]";
				mes "Good idea...";
				mes "That is a good way to improve yourself. I've seen many people who know how to be strong physically but not in their mental state.";
				next;
				mes "[Guildmaster]";
				mes "I hope you're not a hypocrite. Spiritual discipline is the best way for you to survive.";
				next;
				break;
			}
			break;
		}
		mes "[Guildmaster]";
		mes "Sadly, there are some nit-wits who are eager to be Assassins even though they don't know anything...";
		next;
		mes "[Guildmaster]";
		mes "They cause problems and bring us disgrace. Their activities often result in horrible situations.";
		next;
		mes "[Guildmaster]";
		mes "Be careful lest you become one of them once you become an Assassin. The responsibility rests solely on your shoulders...";
		next;
		mes "[Guildmaster]";
		mes "So if you could become an Assassin right now, what is the first thing you would do?";
		next;
		switch(select("I would go hunt right away.:There are people waiting for me.:Check how I can help as an Assassin.")) {
		case 1:
			mes "[Guildmaster]";
			mes "Hunt...";
			mes "Is that all...?";
			next;
			switch(select("I would level up fast.:I want to explore my Assassin skills.:I will go where I couldn't go as a Thief.")) {
			case 1:
				.@assassin_sangdam += 10;
				mes "[Guildmaster]";
				mes "Don't act recklessly...";
				mes "Being an Assassin never makes you a different person. And don't rely on chance.";
				next;
				break;
			case 2:
				.@assassin_sangdam += 5;
				mes "[Guildmaster]";
				mes "It is good for one to examine oneself. I can understand that you will be excited by the great change in your ability.";
				next;
				mes "[Guildmaster]";
				mes "In the meantime, I hope you won't forget the Assassin mentality.";
				next;
				break;
			case 3:
				mes "[Guildmaster]";
				mes "Very well...";
				mes "Exploring places you've never seen before. But know that being an Assassin never makes you a different person.";
				next;
				mes "[Guildmaster]";
				mes "Don't force yourself too much.";
				mes "Take your time and travel wisely.";
				next;
				break;
			}
			break;
		case 2:
			mes "[Guildmaster]";
			mes "Who is waiting";
			mes "for you, might I ask?";
			next;
			switch(select("My friends.:My Guildsmen.:My lover.")) {
			case 1:
				.@assassin_sangdam += 5;
				mes "[Guildmaster]";
				mes "I see...";
				mes "Appreciate them for caring about you, even when you're alone.";
				next;
				break;
			case 2:
				.@assassin_sangdam += 5;
				mes "[Guildmaster]";
				mes "Great...";
				mes "Comrades for whom you would die for...";
				next;
				mes "[Guildmaster]";
				mes "As an Assassin, find a job that you can do for them without them knowing..";
				next;
				break;
			case 3:
				mes "[Guildmaster]";
				mes "Haha, the needs of the body are sometimes hard to ignore. It's best to accept that part of human nature.";
				next;
				mes "[Guildmaster]";
				if (Sex == SEX_MALE)
					mes "But you must never reveal to her the Assassin side of your life. No matter what it takes.";
				else
					mes "But you must never reveal to him the Assassin side of your life, no matter what it takes.";
				next;
				mes "[Guildmaster]";
				mes "Love your beloved forever, even if you can't openly express it. Sometimes, life doesn't allow you to find true love more than once.";
				next;
				break;
			}
			break;
		case 3:
			mes "[Guildmaster]";
			mes "That's most admirable. Is there anything that you would like to ask me about?";
			next;
			switch(select("Places where Assassins can level up...:Main goals as an Assassin.:Financial consulting.")) {
			case 1:
				.@assassin_sangdam += 5;
				mes "[Guildmaster]";
				mes "It all depends on your mind. Any place could be the best to level up according to your mind state.";
				next;
				mes "[Guildmaster]";
				mes "You must know how to";
				mes "survive in any situation.";
				next;
				break;
			case 2:
				mes "[Guildmaster]";
				mes "There are many Assassins out there. Look to them as your trainers, and ask for their opinions.";
				next;
				mes "[Guildmaster]";
				mes "I hope you will become an excellent Assassin. And when you reach a certain level, you must guide newbies as your trainers have.";
				next;
				break;
			case 3:
				.@assassin_sangdam += 10;
				mes "[Guildmaster]";
				mes "Oh my lord...";
				mes "Are you planning to become an Assassin in order to make money?";
				next;
				mes "[Guildmaster]";
				mes "People of that nature are unwelcome. If such is your goal, you may wish to reconsider your job...";
				next;
				break;
			}
			break;
		}
		mes "[Guildmaster]";
		mes "It was nice to meet you. You reminded me of the good ol' days.";
		next;
		mes "[Guildmaster]";
		mes "Please give me";
		mes "one second...";
		next;
		mapannounce "in_moc_16","Those involved with the testing of " + strcharinfo(0) + ", please gather before me.",bc_map;
		mes "[Guildmaster]";
		mes "Those involved with the testing of " + strcharinfo(0) + ", please gather before me.";
		next;
		mes "[Guildmaster]";
		mes "They will";
		mes "be here soon.";
		next;
		enablenpc "[Huey]";
		enablenpc "[Khai]";
		enablenpc "[The Anonymous One]";
		enablenpc "[Barcardi]";
		enablenpc "[Beholder]";
		enablenpc "[Thomas]";
		enablenpc "[Gayle Maroubitz]";
		mes "[The Anonymous One]";
		mes "I am here.";
		next;
		mes "[Guildmaster]";
		mes "I would like to listen to your opinion of " + strcharinfo(0) + " for the job change test.";
		next;
		mes "[The Anonymous One]";
		mes "Ah yeah...";
		mes "I think";
		mes ""+ strcharinfo(0) +"";
		mes "is decent.";
		next;
		mes "[Guildmaster]";
		mes "Well...";
		mes "The Anonymous One";
		mes "supports you. How";
		mes "about you, Huey?";
		next;
		if (ASSIN_Q3 == 1) { // player Job Level was above 48
			mes "[Huey]";
			mes "A rarity.";
			mes "You can tell";
			mes "by the job level.";
			next;
			mes "[Huey]";
			mes "I agree with";
			mes "the Anonymous One.";
		}
		else { // player Job Level was below 48
			mes "[Huey]";
			mes "Although "+ strcharinfo(0) +" looks too mellow and gentle, kind of like a pussycat, "+ strcharinfo(0) +" has the stuff.";
			next;
			mes "[Huey]";
			mes "If it's alright with you, I'd like to get back to my job.";
			next;
			mes "[Guildmaster]";
			mes "Yes...";
			mes "That is all, Huey.";
		}
		next;
		mes "[Guildmaster]";
		mes "So...";
		mes "'Beholder,' what";
		mes "is your opinion?";
		next;
		disablenpc "[Huey]";
		mes "[Beholder]";
		mes "Well, I don't like the course score. But, somehow the whole test was passed. I'm okay with this person.";
		next;
		mes "[Guildmaster]";
		mes "Hmm...";
		mes "It seems we are all in agreement. Good. I don't have any problem with "+ strcharinfo(0) +" as well...";
		next;
		switch(ASSIN_Q) {
		case 8:
			mes "[Guildmaster]";
			mes "Even though you're driven by personal revenge, I hope it will go away as you train...";
			next;
			break;
		case 9:
			mes "[Guildmaster]";
			mes "Even though your main concern for now is being a rich, I'm sure you'll pursue something even greater...";
			next;
			break;
		case 10:
			mes "[Guildmaster]";
			mes "Eager to travel all around the world, I hope your real identity is found in your journeys...";
			next;
			break;
		case 11:
			mes "[Guildmaster]";
			mes "You seem to know a lot about Assassins. I don't think frustration from being alone will be difficult for you...";
			next;
			break;
		case 12:
			mes "[Guildmaster]";
			mes "You have an idiocy about money, but I believe that you should be able to overcome it.";
			next;
			mes "[Guildmaster]";
			mes "Although I do not trust you for now, I will give you a chance...";
			next;
			break;
		case 13:
			mes "[Guildmaster]";
			mes "Even though you're enamored with Assassins superficially, I believe you will realize the real value of the Assassin job sooner or later.";
			next;
			break;
		case 14:
			mes "[Guildmaster]";
			mes "One of the rare people who seeks better skills, I hope you will realize the importance of spiritual discipline sooner or later.";
			next;
			break;
		case 15:
			mes "[Guildmaster]";
			mes "Sooner or later, you will find a new goal to which you can devote yourself...";
			next;
			break;
		case 16:
			mes "[Guildmaster]";
			mes "I know some people care only about their physical training, but";
			mes "I believe you stand out amongst them...";
			next;
		}
		switch(ASSIN_Q3) {
		case 1: // player Job Level was above 48
			mes "[Guildmaster]";
			mes "Well, I've said too much. Please choose a weapon as a present.";
			next;
			mes "[Guildmaster]";
			mes "You can choose a Jur, Katar, Main Gauche, or a Gladius. As a master, I love them all.";
			next;
			switch(select("Jur:Katar:Main Gauche:Gladius")) {
			case 1:
				mes "[Guildmaster]";
				mes "A Jur...";
				mes "Good choice. There you are. I hope it will serve you well.";
				getitem 1251,1; //Jur_
				break;
			case 2:
				mes "[Guildmaster]";
				mes "A Katar...";
				mes "Here you are.";
				mes "Although it's used,";
				mes "I know it will";
				mes "serve you well.";
				getitem 1253,1; //Katar_
				break;
			case 3:
				mes "[Guildmaster]";
				mes "I see. You want to use both hands. Here, take your Main Gauche.";
				getitem 1208,1; //Main_Gauche_
				break;
			case 4:
				mes "[Guildmaster]";
				mes "A Gladius...";
				mes "It used to rule over the Assassin weapon market. Please take care of my gladius.";
				getitem 1220,1; //Gladius_
			}
			ASSIN_Q3 = 3;
			next;
			break;
		case 2: // player Job Level was below 48
			mes "[Guildmaster]";
			mes "Well, I talked too much.";
			mes "Please take this first.";
			next;
			switch(rand(1,5)) {
			case 1:
				getitem 1207,1; //Main_Gauche
				break;
			case 2:
				getitem 1250,1; //Jur
				break;
			case 3:
				getitem 1216,1; //Stiletto
				break;
			case 4:
				getitem 1201,1; //Knife
				break;
			case 5:
				getitem 1252,1; //Katar
			}
			ASSIN_Q3 = 3;
		}
		mes "[Guildmaster]";
		mes "Well, I am giving you a token. Please return to the Assassin expert, the Ferocious-Looking Huey, at the entrance.";
		next;
		mes "[Guildmaster]";
		mes "Upon receiving this token, Huey will promote you to an Assassin.";
		next;
		mes "[Guildmaster]";
		mes "You, " + strcharinfo(0) + ", have chosen to live as an Assassin. May you learn our ways and be an honorable example to others.";
		if (checkre(0))
			savepoint "morocc",100,100,1,1;
		else
			savepoint "moc_ruins",79,99,1,1;
		getitem 1008,1; //Frozen_Heart
		ASSIN_Q = 17;
		changequest 8006,8007;
		next;
		mes "[Guildmaster]";
		mes "Okay, all of you may go back to your positions. I will send you to the entrance as well. Let's move...";
		disablenpc "[Huey]";
		disablenpc "[Khai]";
		disablenpc "[The Anonymous One]";
		disablenpc "[Barcardi]";
		disablenpc "[Beholder]";
		disablenpc "[Thomas]";
		disablenpc "[Gayle Maroubitz]";
		close2;
		warp "in_moc_16",17,19;
		end;
	}
	else if (ASSIN_Q == 17) {
		mes "[Guildmaster]";
		mes "Umm...?";
		mes "How come you're in here...?";
		next;
		mes "[Guildmaster]";
		mes "You already finished your test. Why don't you go try to get the ^006699Necklace of Oblivion^000000 so you can change your job?";
		close2;
		warp "in_moc_16",17,19;
		end;
	}
	else if (ASSIN_Q > 7 && ASSIN_Q < 17) {
		ASSIN_Q = 7;
		mes "[Guildmaster]";
		mes "What the hell? You pressed 'Cancel' during the process. Do you want to change your job or what?";
		next;
		mes "[Guildmaster]";
		mes "^666666*Sigh...*^000000";
		mes "Ok, let's start again. If you don't listen this time, you won't leave this room alive. You got me?";
		close;
	}
	else {
		mes "[Guildmaster]";
		mes "Umm? How come your in here?";
		next;
		mes "[Guildmaster]";
		mes "You already finished your test, go give your ^006699Necklace of Oblivion^000000 to the Ferocious-looking guy so you can change your job!";
		close2;
		warp "in_moc_16",17,19;
		end;
	}
}

in_moc_16,186,81,1	script	Master Assist	55,1,1,{
	mes "[Assistent Gayle Maroubitz]";
	mes "Sorry, but I'm not in charge of job changes. Go to the Guildmaster, as he has told you.";
	close;
}

in_moc_16,170,90,0	script	info 1	-1,{
OnTouch_:
	mes "[Guildmaster]";
	mes "Huh.";
	mes "Now, that place is blocked. You might want to check the other side.";
	close;
}

in_moc_16,153,85,0	script	info 2	-1,1,1,{
OnTouch_:
	mes "[Guildmaster]";
	mes "You're getting warmer. You're almost there. Just, turn around a little bit.";
	close;
}

in_moc_16,160,85,0	script	info 3	-1,1,1,{
OnTouch_:
	mes "[Guildmaster]";
	mes "Hmm.";
	mes "Now, that place";
	mes "is blocked.";
	close;
}

in_moc_16,175,89,0	script	info 4	-1,1,1,{
OnTouch_:
	mes "[Guildmaster]";
	mes "Umm...";
	mes "You're heading for my assistant. Do you still need to talk to him?";
	close;
}

in_moc_16,164,88,0	script	info 5	-1,{
OnTouch_:
	mes "[Guildmaster]";
	mes "Well done...";
	mes "I can feel your steps near me.";
	close;
}

in_moc_16,149,95,0	script	info 6	-1,1,1,{
OnTouch_:
	mes "[Guildmaster]";
	mes "Hm? Not bad. You're almost here.";
	close;
}

in_moc_16,180,101,0	script	info 7	-1,1,1,{
OnTouch_:
	mes "[Guildmaster]";
	mes "I don't think you're going the right way.";
	close;
}

in_moc_16,186,107,0	script	info 8	-1,1,1,{
OnTouch_:
	mes "[Guildmaster]";
	mes "No sense of direction, eh?";
	close;
}

-	script	#moc_assin_dup	-1,{
	end;

OnInit:
	disablenpc();
	end;
}
in_moc_16,156,87,1	duplicate(#moc_assin_dup)	[Huey]	55
in_moc_16,156,85,1	duplicate(#moc_assin_dup)	[Khai]	730
in_moc_16,156,83,1	duplicate(#moc_assin_dup)	[The Anonymous One]	106
in_moc_16,156,81,2	duplicate(#moc_assin_dup)	[Barcardi]	725
in_moc_16,156,79,1	duplicate(#moc_assin_dup)	[Beholder]	118
in_moc_16,156,77,1	duplicate(#moc_assin_dup)	[Thomas]	118
in_moc_16,156,75,1	duplicate(#moc_assin_dup)	[Gayle Maroubitz]	55
